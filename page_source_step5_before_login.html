<html lang="fr" class="js flexbox canvas canvastext webgl no-touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients no-cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers no-applicationcache svg inlinesvg smil svgclippaths desktop landscape"><head><meta charset="utf-8"><meta http-equiv="x-ua-compatible" content="ie=edge"><title data-i18n="appforyou_title">4YOU - Se connecter</title><meta name="description" content=""><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0"><link rel="shortcut icon" type="image/png" href="/resources/icons/favicon.png"><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="main" src="./main.js"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="config.js" src="config.js"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="resources/config" src="/resources/config.js"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="require-css/css" src="/resources/js/lib/require-css/css.js"></script><link type="text/css" rel="stylesheet" href="/resources/js/lib/jquery/ui/jquery-ui.min.css"><link type="text/css" rel="stylesheet" href="/resources/js/lib/bootstrap/bootstrap.min.css"><link type="text/css" rel="stylesheet" href="/resources/js/lib/select2/select2.min.css"><link type="text/css" rel="stylesheet" href="/resources/js/lib/alpaca/alpaca.min.css"><link type="text/css" rel="stylesheet" href="/resources/js/lib/toastr/toastr.min.css"><link type="text/css" rel="stylesheet" href="/resources/js/lib/cropper/cropper.min.css"><link type="text/css" rel="stylesheet" href="/resources/js/lib/ladda/css/ladda-themeless.min.css"><link type="text/css" rel="stylesheet" href="/resources/js/lib/angular-global-styles/angular-material/16.2.13/indigo-pink.css"><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="config-js" src="/resources/js/lib/config-js.js"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="jquery-ui" src="/resources/js/lib/jquery-ui/jquery-ui.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="modernizer" src="/resources/js/lib/modernizer/modernizr-2.8.3.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="placholderfill" src="/resources/js/lib/polyfills/placeholders.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="es6Promise" src="/resources/js/lib/polyfills/es6-promise.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="routie" src="/resources/js/lib/routie/routie.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="select2/select2.full.min" src="/resources/js/lib/select2/select2.full.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="reflectMetadata" src="/resources/js/lib/reflect-metadata/reflect-metadata.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/resources/js/lib/jquery/jquery.3.5.prefilter.workaround.js" src="/resources/js/lib/jquery/jquery.3.5.prefilter.workaround.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="jquery" src="/resources/js/lib/jquery/jquery-3.5.1.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="handlebars" src="/resources/js/lib/handlebars/handlebars.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="bootstrap" src="/resources/js/lib/bootstrap/bootstrap.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/logging/Logger" src="/common/logging/Logger.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/resources/js/lib/select2/i18n/fr.js" src="/resources/js/lib/select2/i18n/fr.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/helpers/realTimeNotifHelper" src="/common/helpers/realTimeNotifHelper.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/helpers/ajaxHelper" src="/common/helpers/ajaxHelper.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/helpers/htmlBuilder" src="/common/helpers/htmlBuilder.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/app/foryou/controller/foryouController.js" src="/app/foryou/controller/foryouController.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/common/components/common.components.js" src="/common/components/common.components.js?bust=v2"></script><link type="text/css" rel="stylesheet" href="/theme/components/theme.foryou.css?bust=v2"><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="require-text/text" src="/resources/js/lib/require-text/text.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/user/authentication" src="/common/user/authentication.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="jsnlog" src="/resources/js/lib/jsnlog/jsnlog.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/helpers/basicImmediateFeedbackHelper" src="/common/helpers/basicImmediateFeedbackHelper.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/i18n/platformAppLocalizer" src="/common/i18n/platformAppLocalizer.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/events/EventsBroadcaster" src="/common/events/EventsBroadcaster.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/resolver/resource/ResourcesClient.js" src="/resolver/resource/ResourcesClient.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="moment" src="/resources/js/lib/moment/moment-with-locales-4you.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="source-map" src="/resources/js/lib/stacktrace/source-map.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="lodash" src="/resources/js/lib/lodash/lodash.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="esapi" src="/resources/js/lib/esapi4js/esapi-min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/controllers/AppController" src="/common/controllers/AppController.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="client/MessageSender" src="/client/MessageSender.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/events/sessiontimeout" src="/common/events/sessiontimeout.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="toastr" src="/resources/js/lib/toastr/toastr.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/i18n/Localizer" src="/common/i18n/Localizer.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="radio" src="/resources/js/lib/radio/radio.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="stacktrace" src="/resources/js/lib/stacktrace/stacktrace-with-polyfills.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/controllers/modules/ConfigurationManager" src="/common/controllers/modules/ConfigurationManager.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/helpers/defaultHelper" src="/common/helpers/defaultHelper.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/helpers/deviceHelper" src="/common/helpers/deviceHelper.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/routers/modules/routeParams" src="/common/routers/modules/routeParams.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/user/actors" src="/common/user/actors.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="bluebird" src="/resources/js/lib/bluebird/bluebird.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/usermgt/client/UserMgtManager.js" src="/bc/usermgt/client/UserMgtManager.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/i18n/thirdPartyLocalizer" src="/common/i18n/thirdPartyLocalizer.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/helpers/localeHelper" src="/common/helpers/localeHelper.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="jquery-i18n" src="/resources/js/lib/i18next/jquery-i18next.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="i18nextHttpBackend.min" src="/resources/js/lib/i18next/i18nextHttpBackend.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="device" src="/resources/js/lib/device/device.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/actors/client/ActorManager.js" src="/bc/actors/client/ActorManager.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/usermgt/client/UserModelFactory.js" src="/bc/usermgt/client/UserModelFactory.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/usermgt/client/LoginRequestModelFactory.js" src="/bc/usermgt/client/LoginRequestModelFactory.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/usermgt/client/LoginResponseModelFactory.js" src="/bc/usermgt/client/LoginResponseModelFactory.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="i18n" src="/resources/js/lib/i18next/i18next.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/actors/client/ActorModelFactory.js" src="/bc/actors/client/ActorModelFactory.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/foryoustream/client/ForYouStreamClient.js" src="/bc/foryoustream/client/ForYouStreamClient.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/app/foryou/routers/mainRouter.js" src="/app/foryou/routers/mainRouter.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/routers/Router" src="/common/routers/Router.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/space/login/loginController.js" src="/space/login/loginController.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/controllers/SpaceController" src="/common/controllers/SpaceController.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/helpers/templateHelper" src="/common/helpers/templateHelper.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="grid/Grid" src="/grid/Grid.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/user/accessmanager" src="/common/user/accessmanager.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="watchdog" src="/resources/js/lib/watchdog/watchdog.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="gridstackDev" src="/resources/js/lib/gridstack/gridstack.dev.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="imagesloaded" src="/resources/js/lib/imagesloaded/imagesloaded.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="grid/modules/widget" src="/grid/modules/widget.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/user/actions" src="/common/user/actions.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/interface/MainMenu" src="/common/interface/MainMenu.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="waitForImages" src="/resources/js/lib/waitForImages/jquery.waitforimages.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="loadstylesheet" src="/resources/js/lib/loadstylesheet/loadstylesheet.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/interface/ModalPopup" src="/common/interface/ModalPopup.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/interface/UpdatePasswordModalPopUp" src="/common/interface/UpdatePasswordModalPopUp.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="ractive/ractive" src="/resources/js/lib/ractive/ractive.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/helpers/swipeHelper" src="/common/helpers/swipeHelper.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/helpers/utilityHelper" src="/common/helpers/utilityHelper.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/interface/SpinLoader" src="/common/interface/SpinLoader.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="html2canvas" src="/resources/js/lib/html2canvas/html2canvas.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/interface/LocaleChooser" src="/common/interface/LocaleChooser.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/personaldata/client/PersonManager.js" src="/bc/personaldata/client/PersonManager.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/sitemap/client/SitemapAdapterLoader.js" src="/bc/sitemap/client/SitemapAdapterLoader.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="jquerymask" src="/resources/js/lib/jquery/mask/jquery.maskedinput.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="bloodhound" src="/resources/js/lib/typeahead/0.10.5/bloodhound.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="spinz" src="/resources/js/lib/spin/spin.min.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="watch" src="/resources/js/lib/watch/watch.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/personaldata/client/PersonResponse.js" src="/bc/personaldata/client/PersonResponse.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/personaldata/client/ManagerResponse.js" src="/bc/personaldata/client/ManagerResponse.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/bc/personaldata/client/PersonRequest.js" src="/bc/personaldata/client/PersonRequest.js?bust=v2"></script><style type="text/css"></style><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="typeahead" src="/resources/js/lib/typeahead/0.10.5/typeahead.bundle.min.js?bust=v2"></script><style type="text/css"></style><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="alpaca" src="/resources/js/lib/alpaca/alpaca.min.js?bust=v2"></script><style type="text/css" data-gs-style-id="gridstack-style-21744"></style><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/interface/PageTransitions" src="/common/interface/PageTransitions.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/processes/processes" src="/common/processes/processes.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/page/login/loginController.js" src="/page/login/loginController.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/controllers/PageController" src="/common/controllers/PageController.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/controllers/WidgetController" src="/common/controllers/WidgetController.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/controllers/modules/LayoutManager" src="/common/controllers/modules/LayoutManager.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/controllers/modules/PlaceholdersManager" src="/common/controllers/modules/PlaceholdersManager.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/helpers/bindingHelper" src="/common/helpers/bindingHelper.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="sightglass" src="/resources/js/lib/rivets/sightglass.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="rivets" src="/resources/js/lib/rivets/rivets.min.js?bust=v2"></script><style type="text/css" data-gs-style-id="gridstack-style-51742"></style><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/widget/com.soprahr.foryou.appl.widget.login.main/loginController.js" src="/widget/com.soprahr.foryou.appl.widget.login.main/loginController.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="common/webcomponents/helper" src="/common/webcomponents/helper.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/widget/com.soprahr.foryou.appl.widget.login.main/remoteEntry.js" src="/widget/com.soprahr.foryou.appl.widget.login.main/remoteEntry.js?bust=v2"></script><script type="text/javascript" charset="utf-8" async="" data-requirecontext="_" data-requiremodule="/common/angular-dependencies-loader/remoteEntry.js" src="/common/angular-dependencies-loader/remoteEntry.js?bust=v2"></script><style>:root [ng-version],:host [ng-version]{--link-text-color: var(--color-pacific-blue);--link-text-decoration: none;--link-hover-text-color: var(--link-text-color);--link-hover-text-decoration: underline}:host{color:var(--theme-text-color)}:host a{color:var(--link-text-color);-webkit-text-decoration:var(--link-text-decoration);text-decoration:var(--link-text-decoration)}:host a:hover{color:var(--link-hover-text-color);-webkit-text-decoration:var(--link-hover-text-decoration);text-decoration:var(--link-hover-text-decoration)}:host .highlighted-value{font-weight:700}:host ol,:host ul{padding-left:revert}
</style><style>.shrs-button-login[_ngcontent-ng-c3034642007]{display:flex;justify-content:center;align-items:center}.logo-sopra[_ngcontent-ng-c3034642007]{text-align:center;margin-bottom:20px}  .logo-shrs foryou-icon svg.foryou-icon-extralarge{fill:var(--color-monza);height:37px;width:34px}.welcome[_ngcontent-ng-c3034642007]{display:flex;justify-content:center;align-items:center}.flex-container[_ngcontent-ng-c3034642007]{display:flex;align-items:center;margin-bottom:20px}.logo-shrs[_ngcontent-ng-c3034642007]{margin-right:6px;margin-left:10px}.display-welcome[_ngcontent-ng-c3034642007]{font-size:18px;margin-right:10px;margin-bottom:0;line-height:24px}.login-form[_ngcontent-ng-c3034642007]{position:absolute;width:50%;left:50%;top:10%;transform:translate(-50%,-10%);padding:20px 65px;background:var(--color-white);margin:auto}@media screen and (max-width: 1280px) and (max-height: 720px){.login-form[_ngcontent-ng-c3034642007]{transform:translate(-50%,-6%)}}@media only screen and (max-width: 768px){.login-form[_ngcontent-ng-c3034642007]{width:90%;padding:20px;top:11%}.display-welcome[_ngcontent-ng-c3034642007]{font-size:14px;margin-right:0}}#loginWidget[_ngcontent-ng-c3034642007]{position:fixed;background-position:center center;background-image:url(/theme/images/background_syd_employee.jpg);background-size:cover;width:100%;height:100%;overflow:auto}@keyframes _ngcontent-ng-c3034642007_fadeInDown{0%{opacity:0;transform:translate3d(-50%,-40%,0)}to{opacity:1;transform:translate3d(-50%,-5%,-5%)}}.fade-in-down[_ngcontent-ng-c3034642007]{animation-name:_ngcontent-ng-c3034642007_fadeInDown;animation-duration:1s}  .login-form #password,   .login-form #username{border-color:var(--input-border-color)}  .login-form .ng-select-container{border-color:var(--input-border-color)!important}  .login-form #username~control-feedback,   .login-form #password~control-feedback{display:none}  .login-form .search-select .search-select-line control-feedback .control-feedback{display:none}  .login-form small.invalid-feedback{display:none!important}[_nghost-ng-c3034642007] {--input-border-color: var(--color-nobel-gray)}</style><style>[_nghost-ng-c2938181516]{display:inline-flex}svg[_ngcontent-ng-c2938181516]{fill:currentColor}.foryou-icon-small[_ngcontent-ng-c2938181516]{height:12px;width:12px}.foryou-icon-medium[_ngcontent-ng-c2938181516]{height:16px;width:16px}.foryou-icon-large[_ngcontent-ng-c2938181516]{height:20px;width:20px}.foryou-icon-extralarge[_ngcontent-ng-c2938181516]{height:42px;width:42px}</style><style>[_nghost-ng-c1832471565]     .row{margin-right:-15px!important;margin-left:-15px!important}[_nghost-ng-c1832471565]     .row>.col, [_nghost-ng-c1832471565]     .row>[class*=col-]{padding-right:15px!important;padding-left:15px!important}[_nghost-ng-c1832471565]     .form-control{font-size:14px}[_nghost-ng-c1832471565]     dynamic-basic-form-control>div:not(.ng-dynamic-forms-group){margin-bottom:15px}.obligatory-mention-form[_ngcontent-ng-c1832471565]{font-size:.8em;margin-bottom:20px}button[_ngcontent-ng-c1832471565]{display:none}</style><style>[_nghost-ng-c758387775]    {--button-neutral-normal-text-color: var(--theme-text-color);--button-neutral-normal-bg-color: var(--theme-text-inverted-color);--button-neutral-normal-border-color: var(--theme-text-color);--button-neutral-hover-text-color: var(--theme-text-inverted-color);--button-neutral-hover-bg-color: var(--color-oxford-blue);--button-neutral-hover-border-color: var(--button-neutral-hover-bg-color);--button-neutral-active-text-color: var(--button-neutral-hover-text-color);--button-neutral-active-bg-color: var(--color-lynch);--button-neutral-active-border-color: var(--button-neutral-active-bg-color);--button-primary-normal-text-color: var(--theme-text-inverted-color);--button-primary-normal-bg-color: var(--color-ebony-clay);--button-primary-normal-border-color: var(--button-primary-normal-bg-color);--button-primary-hover-text-color: var(--button-primary-normal-text-color);--button-primary-hover-bg-color: var(--color-oxford-blue);--button-primary-hover-border-color: var(--button-primary-hover-bg-color);--button-primary-active-text-color: var(--button-primary-normal-text-color);--button-primary-active-bg-color: var(--color-lynch);--button-primary-active-border-color: var(--button-primary-active-bg-color);--button-disabled-text-color: var(--theme-disabled-color);--button-disabled-bg-color: var(--theme-disabled-bg-color);--button-disabled-border-color: var(--theme-disabled-border-color)}[_nghost-ng-c758387775]{display:inline-flex}.shrs-button[_ngcontent-ng-c758387775]{height:30px;margin:0;padding:0 18px;font-size:14px;font-weight:400;border-width:1px;border-style:solid;cursor:pointer;line-height:1;outline-offset:3px}.shrs-button.shrs-button-neutral[_ngcontent-ng-c758387775]{color:var(--button-neutral-normal-text-color);background-color:var(--button-neutral-normal-bg-color);border-color:var(--button-neutral-normal-border-color)}.shrs-button.shrs-button-neutral[_ngcontent-ng-c758387775]:active{color:var(--button-neutral-active-text-color);background-color:var(--button-neutral-active-bg-color);border-color:var(--button-neutral-active-border-color)}.shrs-button.shrs-button-neutral[_ngcontent-ng-c758387775]:hover, .shrs-button.shrs-button-neutral[_ngcontent-ng-c758387775]:focus:not(:focus-visible){color:var(--button-neutral-hover-text-color);background-color:var(--button-neutral-hover-bg-color);border-color:var(--button-neutral-hover-border-color)}.shrs-button.shrs-button-primary[_ngcontent-ng-c758387775]{color:var(--button-primary-normal-text-color);background-color:var(--button-primary-normal-bg-color);border-color:var(--button-primary-normal-border-color)}.shrs-button.shrs-button-primary[_ngcontent-ng-c758387775]:active{color:var(--button-primary-active-text-color);background-color:var(--button-primary-active-bg-color);border-color:var(--button-primary-active-border-color)}.shrs-button.shrs-button-primary[_ngcontent-ng-c758387775]:hover, .shrs-button.shrs-button-primary[_ngcontent-ng-c758387775]:focus:not(:focus-visible){color:var(--button-primary-hover-text-color);background-color:var(--button-primary-hover-bg-color);border-color:var(--button-primary-hover-border-color)}.shrs-button[_ngcontent-ng-c758387775]:disabled, .shrs-button.shrs-button-disabled[_ngcontent-ng-c758387775]{cursor:not-allowed;color:var(--button-disabled-text-color);background-color:var(--button-disabled-bg-color);border-color:var(--button-disabled-border-color)}.shrs-button[_ngcontent-ng-c758387775]:disabled:active, .shrs-button.shrs-button-disabled[_ngcontent-ng-c758387775]:active, .shrs-button[_ngcontent-ng-c758387775]:disabled:hover, .shrs-button.shrs-button-disabled[_ngcontent-ng-c758387775]:hover{color:var(--button-disabled-text-color);background-color:var(--button-disabled-bg-color);border-color:var(--button-disabled-border-color)}.shrs-button[_ngcontent-ng-c758387775]:focus:not(:focus-visible){outline:none}.shrs-button.shrs-button-type-icon[_ngcontent-ng-c758387775]{padding:0;min-width:30px;display:inline-flex;align-items:center;justify-content:center}.shrs-button.shrs-button-shape-round[_ngcontent-ng-c758387775]{border-radius:15px}.shrs-button.shrs-button-shape-square[_ngcontent-ng-c758387775]{border-radius:3px}.shrs-button[_ngcontent-ng-c758387775]   .shrs-transition[_ngcontent-ng-c758387775]{transition:all .3s ease-out}.link[_ngcontent-ng-c758387775]{border:none;background:none;padding:0}.link[disabled][_ngcontent-ng-c758387775]{color:var(--theme-text-color)}.link[_ngcontent-ng-c758387775]:not([disabled]){cursor:pointer;color:var(--link-text-color);-webkit-text-decoration:var(--link-text-decoration);text-decoration:var(--link-text-decoration)}.link[_ngcontent-ng-c758387775]:not([disabled]):hover{color:var(--link-hover-text-color);-webkit-text-decoration:var(--link-hover-text-decoration);text-decoration:var(--link-hover-text-decoration)}.link[_ngcontent-ng-c758387775]:not([disabled]).editable-value{font-weight:700}</style><style>.bottom[_ngcontent-ng-c3999466307]{padding-top:8px}</style><style>[_nghost-ng-c2905977793]    {--input-height: 36px;--input-text-color: var(--theme-text-color);--input-border-color: var(--color-nobel-gray);--input-disabled-bg-color: var(--theme-disabled-bg-color);--input-disabled-text-color: var(--theme-disabled-color);--input-focus-border-color: var(--theme-selected);--input-autocomplete-option-hover-bg-color: var(--color-ebony-clay)}[_nghost-ng-c2905977793]     .input .input-line .control-feedback{position:absolute;height:var(--input-height);line-height:var(--input-height);top:0;right:calc(-1 * var(--control-feedback-default-size))}.input[_ngcontent-ng-c2905977793]   .input-line[_ngcontent-ng-c2905977793]{position:relative}.input[_ngcontent-ng-c2905977793]   input[_ngcontent-ng-c2905977793]{border-width:1px;border-style:solid;border-radius:3px;border-color:var(--input-border-color);background-color:transparent;color:var(--input-text-color);height:var(--input-height)}.input[_ngcontent-ng-c2905977793]   input[disabled][_ngcontent-ng-c2905977793]{background-color:var(--input-disabled-bg-color);color:var(--input-disabled-text-color);cursor:not-allowed}.input[_ngcontent-ng-c2905977793]   input[_ngcontent-ng-c2905977793]:focus{box-shadow:none!important;border-color:var(--input-focus-border-color)!important}.input[_ngcontent-ng-c2905977793]   input.autocomplete-hidden-field[_ngcontent-ng-c2905977793]{visibility:hidden;display:block;transform:translateY(calc(var(--input-height) * -1));margin-bottom:calc(var(--input-height) * -1)}.input.valid[_ngcontent-ng-c2905977793]   input[_ngcontent-ng-c2905977793]{border-color:var(--theme-success)}.input.invalid[_ngcontent-ng-c2905977793]   input[_ngcontent-ng-c2905977793]{border-color:var(--theme-danger)}.input[_ngcontent-ng-c2905977793]   .secondary-data[_ngcontent-ng-c2905977793]{margin-left:5px}.uppercase[_nghost-ng-c2905977793]   input[_ngcontent-ng-c2905977793]{text-transform:uppercase}.lowercase[_nghost-ng-c2905977793]   input[_ngcontent-ng-c2905977793]{text-transform:lowercase}  .input .dropdown-menu{padding:0;max-height:300px;overflow-y:auto}  .input .dropdown-menu .dropdown-item{padding:8px 10px;font-size:14px}  .input .dropdown-menu .dropdown-item.active{background-color:var(--input-autocomplete-option-hover-bg-color);color:#fff}</style><style>small.invalid-feedback[_ngcontent-ng-c842679347]{display:block;width:100%;margin-top:.25rem;font-size:80%;color:var(--theme-danger)}</style><style>:root [ng-version]{--form-control-label-line-height: 20px;--form-control-label-margin-bottom: 7px;--form-control-label-text-color: var(--theme-text-color);--form-control-label-icon-hover-color: var(--color-pacific-blue);--form-control-label-icon-focus-color: var(--color-pacific-blue)}.form-control-label-wrapper[_ngcontent-ng-c3537923432]{display:flex;flex-direction:row;justify-content:start;align-items:center;width:100%;margin-bottom:var(--form-control-label-margin-bottom)}label[_ngcontent-ng-c3537923432]{display:block;line-height:var(--form-control-label-line-height)}.required[_ngcontent-ng-c3537923432]{margin-left:3px}button.info[_ngcontent-ng-c3537923432]{all:unset;cursor:pointer;color:var(--form-control-label-text-color);display:inline-flex;margin-left:5px;align-items:center}button.info[_ngcontent-ng-c3537923432]   foryou-icon[_ngcontent-ng-c3537923432]{line-height:1}button.info[_ngcontent-ng-c3537923432]   foryou-icon.icon-info[_ngcontent-ng-c3537923432]{display:none}button.info[_ngcontent-ng-c3537923432]   foryou-icon.icon-info-circle[_ngcontent-ng-c3537923432]{display:initial}button.info[_ngcontent-ng-c3537923432]:focus-visible{outline-color:inherit;outline-style:auto;outline-offset:3px}button.info.displayed[_ngcontent-ng-c3537923432]   foryou-icon.icon-info-circle[_ngcontent-ng-c3537923432]{display:none}button.info.displayed[_ngcontent-ng-c3537923432]   foryou-icon.icon-info[_ngcontent-ng-c3537923432]{display:initial}button.info[_ngcontent-ng-c3537923432]:hover, button.info[_ngcontent-ng-c3537923432]:active, button.info.displayed[_ngcontent-ng-c3537923432]:hover, button.info.displayed[_ngcontent-ng-c3537923432]:active{color:var(--form-control-label-icon-hover-color)}button.info[_ngcontent-ng-c3537923432]:hover   foryou-icon.icon-info-circle[_ngcontent-ng-c3537923432], button.info[_ngcontent-ng-c3537923432]:active   foryou-icon.icon-info-circle[_ngcontent-ng-c3537923432], button.info.displayed[_ngcontent-ng-c3537923432]:hover   foryou-icon.icon-info-circle[_ngcontent-ng-c3537923432], button.info.displayed[_ngcontent-ng-c3537923432]:active   foryou-icon.icon-info-circle[_ngcontent-ng-c3537923432]{display:initial}button.info[_ngcontent-ng-c3537923432]:hover   foryou-icon.icon-info[_ngcontent-ng-c3537923432], button.info[_ngcontent-ng-c3537923432]:active   foryou-icon.icon-info[_ngcontent-ng-c3537923432], button.info.displayed[_ngcontent-ng-c3537923432]:hover   foryou-icon.icon-info[_ngcontent-ng-c3537923432], button.info.displayed[_ngcontent-ng-c3537923432]:active   foryou-icon.icon-info[_ngcontent-ng-c3537923432]{display:none}button.info[_ngcontent-ng-c3537923432]:hover:active{color:var(--form-control-label-icon-focus-color)}button.info[_ngcontent-ng-c3537923432]:hover:active   foryou-icon.icon-info-circle[_ngcontent-ng-c3537923432]{display:none}button.info[_ngcontent-ng-c3537923432]:hover:active   foryou-icon.icon-info[_ngcontent-ng-c3537923432]{display:initial}p.helper[_ngcontent-ng-c3537923432]{margin:0 0 var(--form-control-label-margin-bottom) 0}</style><style>[_nghost-ng-c2570169349]    {--control-feedback-default-size: 20px}.control-feedback[_ngcontent-ng-c2570169349]{display:inline-block;width:var(--control-feedback-default-size);height:var(--control-feedback-default-size);line-height:var(--control-feedback-default-size);text-align:center;pointer-events:none}.control-feedback.valid[_ngcontent-ng-c2570169349]{color:var(--theme-success)}.control-feedback.invalid[_ngcontent-ng-c2570169349]{color:var(--theme-danger)}</style><style>.input[_ngcontent-ng-c1429023380]   input[_ngcontent-ng-c1429023380]{border-width:1px;border-style:solid;border-radius:3px;border-color:var(--color-nobel-gray);background-color:transparent;color:var(--theme-text-color);height:36px;padding-right:35px}.input[_ngcontent-ng-c1429023380]   input[disabled][_ngcontent-ng-c1429023380]{background-color:var(--theme-disabled-bg-color);color:var(--theme-disabled-color);cursor:not-allowed}.input[_ngcontent-ng-c1429023380]   input[_ngcontent-ng-c1429023380]:focus{box-shadow:none!important;border-color:var(--theme-selected)!important}.input.valid[_ngcontent-ng-c1429023380]   input[_ngcontent-ng-c1429023380]{border-color:var(--theme-success)}.input.invalid[_ngcontent-ng-c1429023380]   input[_ngcontent-ng-c1429023380]{border-color:var(--theme-danger)}[_nghost-ng-c1429023380]     .input .input-line .control-feedback{position:absolute;height:36px;line-height:36px;top:0;right:calc(-1 * var(--control-feedback-default-size))}.input-line[_ngcontent-ng-c1429023380]{position:relative}.btn-toggle-password[_ngcontent-ng-c1429023380]{position:absolute;top:50%;transform:translateY(-50%);right:10px;background-color:transparent;border:none;color:var(--color-nobel-gray);font-size:14px;margin-left:5px;padding:0}.password-visible[_ngcontent-ng-c1429023380]{color:var(--color-ebony-clay)}</style><style>.forgot-password-link[_ngcontent-ng-c4153466876]{margin-top:7px}.forgot-password-link[_ngcontent-ng-c4153466876]   foryou-icon[_ngcontent-ng-c4153466876]{color:var(--link-text-color);margin-right:4px}</style><style>[_nghost-ng-c3054294055]    {--search-select-text-color: var(--theme-text-color);--search-select-disabled-bg-color: var(--theme-disabled-bg-color);--search-select-disabled-border-color: var(--theme-disabled-border-color);--search-select-disabled-text-color: var(--theme-disabled-color);--search-select-focus-border-color: var(--theme-selected);--search-select-ng-select-container-height: 36px}[_nghost-ng-c3054294055]     .search-select .search-select-line{position:relative}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select .ng-dropdown-panel{min-width:100%;width:auto}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select .ng-select-container{height:var(--search-select-ng-select-container-height)}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select .ng-select-container .ng-value{color:var(--search-select-text-color)}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-focused>.ng-select-container, [_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.focus>.ng-select-container{border-radius:4px!important;border-color:var(--search-select-focus-border-color)!important;z-index:2100!important;border-width:1px!important}@media (forced-colors: active){[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-focused>.ng-select-container, [_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.focus>.ng-select-container{border:2px solid Highlight!important}}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-disabled .ng-select-container, [_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-disabled .ng-value-container, [_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-input>input{cursor:not-allowed}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-disabled .ng-select-container{background-color:var(--search-select-disabled-bg-color);border-color:var(--search-select-disabled-border-color)}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-disabled .ng-value-label{color:var(--search-select-disabled-text-color)}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-disabled .ng-arrow-wrapper, [_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-disabled .ng-arrow-wrapper:hover{cursor:not-allowed}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-disabled .ng-arrow-wrapper .ng-arrow, [_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-disabled .ng-arrow-wrapper:hover .ng-arrow{border-top-color:var(--search-select-disabled-text-color)}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.ng-select-focused:not(.ng-select-opened)>.ng-select-container{box-shadow:none}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.valid:not(.ng-select-disabled)>.ng-select-container{border-color:var(--theme-success)}[_nghost-ng-c3054294055]     .search-select .search-select-line .ng-select.touched.invalid:not(.ng-select-disabled)>.ng-select-container{border-color:var(--theme-danger)}[_nghost-ng-c3054294055]     .search-select .search-select-line control-feedback .control-feedback{position:absolute;height:var(--search-select-ng-select-container-height);line-height:var(--search-select-ng-select-container-height);top:0;right:calc(-1 * var(--control-feedback-default-size))}</style><style>[_nghost-ng-c3054294055]    {--dropdown-option-bg-color: var(--theme-text-inverted-color);--dropdown-option-color: var(--theme-text-color);--dropdown-selected-option-bg-color: var(--theme-text-color);--dropdown-selected-option-color: var(--theme-text-inverted-color);--dropdown-hovered-option-bg-color: var(--color-nobel-gray);--dropdown-hovered-option-color: var(--theme-text-color)}[_nghost-ng-c3054294055]     .ng-select .ng-select-container{border:1px solid var(--color-nobel-gray);border-radius:4px}[_nghost-ng-c3054294055]     .ng-dropdown-panel .ng-dropdown-panel-items .ng-option{background-color:var(--dropdown-option-bg-color);color:var(--dropdown-option-color)}[_nghost-ng-c3054294055]     .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-marked{background-color:var(--dropdown-hovered-option-bg-color);color:var(--dropdown-hovered-option-color)}[_nghost-ng-c3054294055]     .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected, [_nghost-ng-c3054294055]     .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked{background-color:var(--dropdown-selected-option-bg-color);color:var(--dropdown-selected-option-color)}[_nghost-ng-c3054294055]     .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected .ng-option-label, [_nghost-ng-c3054294055]     .ng-dropdown-panel .ng-dropdown-panel-items .ng-option.ng-option-selected.ng-option-marked .ng-option-label{font-weight:400}</style><style>@charset "UTF-8";.ng-select{position:relative;display:block;box-sizing:border-box}.ng-select div,.ng-select input,.ng-select span{box-sizing:border-box}.ng-select [hidden]{display:none}.ng-select.ng-select-searchable .ng-select-container .ng-value-container .ng-input{opacity:1}.ng-select.ng-select-opened .ng-select-container{z-index:1001}.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-placeholder,.ng-select.ng-select-disabled .ng-select-container .ng-value-container .ng-value{-webkit-user-select:none;user-select:none;cursor:default}.ng-select.ng-select-disabled .ng-arrow-wrapper{cursor:default}.ng-select.ng-select-filtered .ng-placeholder{display:none}.ng-select .ng-select-container{cursor:default;display:flex;outline:none;overflow:hidden;position:relative;width:100%}.ng-select .ng-select-container .ng-value-container{display:flex;flex:1}.ng-select .ng-select-container .ng-value-container .ng-input{opacity:0}.ng-select .ng-select-container .ng-value-container .ng-input>input{box-sizing:content-box;background:none transparent;border:0 none;box-shadow:none;outline:none;padding:0;cursor:default;width:100%}.ng-select .ng-select-container .ng-value-container .ng-input>input::-ms-clear{display:none}.ng-select .ng-select-container .ng-value-container .ng-input>input[readonly]{-webkit-user-select:none;user-select:none;width:0;padding:0}.ng-select.ng-select-single.ng-select-filtered .ng-select-container .ng-value-container .ng-value{visibility:hidden}.ng-select.ng-select-single .ng-select-container .ng-value-container,.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-single .ng-select-container .ng-value-container .ng-input{position:absolute;left:0;width:100%}.ng-select.ng-select-multiple.ng-select-disabled>.ng-select-container .ng-value-container .ng-value .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container{flex-wrap:wrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{position:absolute}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value{white-space:nowrap}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value.ng-value-disabled .ng-value-icon{display:none}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-value .ng-value-icon{cursor:pointer}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-input{flex:1;z-index:2}.ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder{z-index:1}.ng-select .ng-clear-wrapper{cursor:pointer;position:relative;width:17px;-webkit-user-select:none;user-select:none}.ng-select .ng-clear-wrapper .ng-clear{display:inline-block;font-size:18px;line-height:1;pointer-events:none}.ng-select .ng-spinner-loader{border-radius:50%;width:17px;height:17px;margin-right:5px;font-size:10px;position:relative;text-indent:-9999em;border-top:2px solid rgba(66,66,66,.2);border-right:2px solid rgba(66,66,66,.2);border-bottom:2px solid rgba(66,66,66,.2);border-left:2px solid #424242;transform:translateZ(0);animation:load8 .8s infinite linear}.ng-select .ng-spinner-loader:after{border-radius:50%;width:17px;height:17px}@keyframes load8{0%{transform:rotate(0)}to{transform:rotate(360deg)}}.ng-select .ng-arrow-wrapper{cursor:pointer;position:relative;text-align:center;-webkit-user-select:none;user-select:none}.ng-select .ng-arrow-wrapper .ng-arrow{pointer-events:none;display:inline-block;height:0;width:0;position:relative}.ng-dropdown-panel{box-sizing:border-box;position:absolute;opacity:0;width:100%;z-index:1050;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .ng-dropdown-panel-items{display:block;height:auto;box-sizing:border-box;max-height:240px;overflow-y:auto}.ng-dropdown-panel .ng-dropdown-panel-items .ng-optgroup{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option{box-sizing:border-box;cursor:pointer;display:block;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .ng-option-label:empty:before{content:"\200b"}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option .highlighted{font-weight:700;text-decoration:underline}.ng-dropdown-panel .ng-dropdown-panel-items .ng-option.disabled{cursor:default}.ng-dropdown-panel .scroll-host{overflow:hidden;overflow-y:auto;position:relative;display:block;-webkit-overflow-scrolling:touch}.ng-dropdown-panel .scrollable-content{top:0;left:0;width:100%;height:100%;position:absolute}.ng-dropdown-panel .total-padding{width:1px;opacity:0}
</style></head><body class="lightgray"><svg xmlns="http://www.w3.org/2000/svg" class="shrs-svg shrs-svg_hidden" xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape">
    <symbol viewBox="0 0 88 88" id="shrs-icon-triangle2-topH"><metadata id="shrs-icon-triangle2-topH-metadata4040"> <rdf:rdf> <cc:work rdf:about=""> <dc:format>image/svg+xml</dc:format> <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type> <dc:title>triangle top icon</dc:title> </cc:work> </rdf:rdf> </metadata>  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1920" inkscape:window-height="1017" id="shrs-icon-triangle2-topH-namedview4036" showgrid="false" inkscape:zoom="5.8352273" inkscape:cx="52.525803" inkscape:cy="44.042843" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-triangle-top" inkscape:pagecheckerboard="0"></sodipodi:namedview>
        <path id="shrs-icon-triangle2-topH-path4033" style="stroke-width:1.18754208" d="M 44 0 L 37.5 13 L 50.5 13 L 44 0 z M 31.5 25 L 25.5 37 L 62.5 37 L 56.5 25 L 31.5 25 z M 19.5 49 L 13.5 61 L 74.5 61 L 68.5 49 L 19.5 49 z M 7.5 73 L 0 88 L 88 88 L 80.5 73 L 7.5 73 z "></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-triangle2-bottomH">
        <metadata id="shrs-icon-triangle2-bottomH-metadata4040">
            <rdf:rdf>
                <cc:work rdf:about="">
                    <dc:format>image/svg+xml</dc:format>
                    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type>
                    <dc:title>triangle top icon</dc:title>
                </cc:work>
            </rdf:rdf>
        </metadata>
        <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1920" inkscape:window-height="1017" id="namedview4036" showgrid="false" inkscape:zoom="5.8352273" inkscape:cx="52.525803" inkscape:cy="43.87147" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-triangle-top" inkscape:pagecheckerboard="0"></sodipodi:namedview>
        <path id="path4033" style="stroke-width:1.18754" d="M 44,88 37.5,75 h 13 z M 31.5,63 25.5,51 h 37 l -6,12 z m -12,-24 -6,-12 h 61 l -6,12 z M 7.5,15 0,0 h 88 l -7.5,15 z"></path>
    </symbol>



    <symbol viewBox="0 0 88 88" id="shrs-icon-360">
        <path d="M87.34,51.85a3.6,3.6,0,0,1-3.55,2.93,4,4,0,0,1-.62-0.07,3.56,3.56,0,0,1-2.93-4.14,36.1,36.1,0,0,0,.55-4.92,3.61,3.61,0,0,1,3.62-3.42h0.16A3.59,3.59,0,0,1,88,46,44.13,44.13,0,0,1,87.34,51.85ZM83.92,36.67a3.3,3.3,0,0,1-.94.13,3.63,3.63,0,0,1-3.49-2.61,33.27,33.27,0,0,0-1.63-4.69,3.56,3.56,0,0,1,1.89-4.69,3.6,3.6,0,0,1,4.72,1.89,41.81,41.81,0,0,1,2,5.57A3.56,3.56,0,0,1,83.92,36.67ZM74.34,21a3.63,3.63,0,0,1-2.7-1.24c-0.52-.59-1.11-1.21-1.69-1.76h0v0a37.38,37.38,0,0,0-49.89-2.38l5.87,5.8a3.49,3.49,0,0,1,.91,3.45,3.54,3.54,0,0,1-2.54,2.51L4.52,30a2.94,2.94,0,0,1-.91.13,3.59,3.59,0,0,1-3.49-4.5L2.86,6A3.54,3.54,0,0,1,5.4,3.53a3.56,3.56,0,0,1,3.45.91l6.09,6.06A44.61,44.61,0,0,1,75,12.88v0h0c0.72,0.68,1.37,1.4,2,2.12A3.59,3.59,0,0,1,74.34,21ZM14.13,66.84c0.59,0.59,4.43,4.4,5.05,4.92a3.54,3.54,0,0,1,.33,5.05A3.61,3.61,0,0,1,16.8,78a3.56,3.56,0,0,1-2.35-.88c-0.75-.65-4.72-4.56-5.41-5.25a3.56,3.56,0,0,1,0-5.05A3.6,3.6,0,0,1,14.13,66.84ZM29.05,78a47.53,47.53,0,0,0,4.72,1.63,3.56,3.56,0,0,1,2.54,4.37,3.62,3.62,0,0,1-3.49,2.64,3.15,3.15,0,0,1-.94-0.13,42.69,42.69,0,0,1-5.64-2A3.56,3.56,0,1,1,29.05,78Zm16.29,2.9a36.46,36.46,0,0,0,5-.55,3.58,3.58,0,1,1,1.27,7,51.56,51.56,0,0,1-5.93.65H45.51A3.57,3.57,0,0,1,45.34,80.85Zm16-4.37a38.19,38.19,0,0,0,4.24-2.64,3.59,3.59,0,0,1,5,.78,3.55,3.55,0,0,1-.78,5,48.3,48.3,0,0,1-5.05,3.16,3.65,3.65,0,0,1-4.89-1.43A3.57,3.57,0,0,1,61.31,76.49Zm15.12-15A3.62,3.62,0,0,1,81.32,60a3.54,3.54,0,0,1,1.43,4.82,40.47,40.47,0,0,1-3.16,5,3.59,3.59,0,0,1-5.83-4.2A35.24,35.24,0,0,0,76.43,61.46Z"></path>
        <g>
            <path d="M34,39.22a5.8,5.8,0,0,1-1.4,3.93,7.25,7.25,0,0,1-3.93,2.22v0.1a7.84,7.84,0,0,1,4.52,1.81,5.07,5.07,0,0,1,1.53,3.88,6.59,6.59,0,0,1-2.58,5.54q-2.58,2-7.36,2a17.88,17.88,0,0,1-7.11-1.33V52.92a16.06,16.06,0,0,0,3.15,1.18,13.29,13.29,0,0,0,3.4.46,6.62,6.62,0,0,0,3.81-.88,3.21,3.21,0,0,0,1.23-2.81,2.54,2.54,0,0,0-1.42-2.46,10.46,10.46,0,0,0-4.52-.73H21.51v-4h1.9a8.86,8.86,0,0,0,4.19-.75,2.71,2.71,0,0,0,1.32-2.57q0-2.8-3.51-2.8a8,8,0,0,0-2.47.4,12.06,12.06,0,0,0-2.79,1.4l-2.41-3.59a13.39,13.39,0,0,1,8-2.43,10.39,10.39,0,0,1,6,1.55A5,5,0,0,1,34,39.22Z"></path>
        </g>
        <g>
            <path d="M35.32,47.88q0-7.31,3.09-10.89t9.26-3.57a16.84,16.84,0,0,1,3.3.25v4.16a13.49,13.49,0,0,0-3-.34,10.18,10.18,0,0,0-4.37.81A5.44,5.44,0,0,0,41.1,40.7a11,11,0,0,0-1,4.5h0.22a5.72,5.72,0,0,1,5.34-2.87,6.62,6.62,0,0,1,5.17,2.07,8.25,8.25,0,0,1,1.87,5.73,8.6,8.6,0,0,1-2.22,6.24,8.18,8.18,0,0,1-6.17,2.3,8.82,8.82,0,0,1-4.76-1.26,8.21,8.21,0,0,1-3.13-3.69A14.05,14.05,0,0,1,35.32,47.88Zm8.9,6.64a3.08,3.08,0,0,0,2.56-1.12,5,5,0,0,0,.89-3.19,4.41,4.41,0,0,0-.83-2.84,3,3,0,0,0-2.52-1,3.86,3.86,0,0,0-2.7,1,3.16,3.16,0,0,0-1.12,2.39,5.42,5.42,0,0,0,1.05,3.39A3.23,3.23,0,0,0,44.22,54.52Z"></path>
        </g>
        <g>
            <path d="M70.29,46q0,6.46-2.11,9.56a7.34,7.34,0,0,
            1-6.51,3.1,7.25,7.25,0,0,1-6.43-3.2q-2.16-3.2-2.17-9.45,0-6.52,2.11-9.61a7.31,7.31,0,0,1,6.49-3.09,7.28,7.28,0,0,1,6.45,3.24Q70.29,39.79,70.29,46Zm-12,0a19.17,19.17,0,0,0,.78,6.5,2.74,2.74,0,0,0,5.27,0A18.64,18.64,0,0,0,65.09,46a18.55,18.55,0,0,0-.82-6.51,2.73,2.73,0,0,0-5.25,0A19.15,19.15,0,0,0,58.24,46Z"></path>
        </g>
    </symbol>
     <symbol viewBox="0 0 88 88" id="shrs-icon-archives">
        <path d="M9.29,33.23V76.92a3.49,3.49,0,0,0,3.49,3.49H75.7a3.49,3.49,0,0,0,3.49-3.49V33.23ZM60,53.15a2.8,2.8,0,0,1-2.79,2.8H31.31a2.79,2.79,0,0,1-2.79-2.8V48.26a2.8,2.8,0,0,1,2.79-2.8H57.18A2.79,2.79,0,0,1,60,48.26Z"></path>
        <rect x="2.3" y="7.02" width="83.88" height="22.72" rx="1.86" ry="1.86"></rect>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-arrow-bottom">
        <path d="M79.64,48.3a4.64,4.64,0,0,1,0,6.27L47.13,86.84a1.23,1.23,0,0,1-.7.46l-0.23.23c-0.23,0-.23.23-0.46,0.23a0.46,0.46,0,0,0-.46.23H42.49A0.46,0.46,0,0,1,42,87.77c-0.23,0-.23-0.23-0.46-0.23a0.23,0.23,0,0,1-.23-0.23c-0.23-.23-0.46-0.23-0.7-0.46L8.36,54.33a4.35,4.35,0,0,1,0-6,4.64,4.64,0,0,1,6.27,0L39.94,73.37V4.18A4.22,4.22,0,0,1,44.35,0a4.16,4.16,0,0,1,4.18,4.18V73.37L73.6,48.76C75,46.67,77.78,46.67,79.64,48.3Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-arrow-left">
        <path d="M39.7,79.64a4.64,4.64,0,0,1-6.27,0L1.16,47.13a1.23,1.23,0,0,1-.46-0.7l-0.23-.23C0.46,46,.23,46,0.23,45.74A0.46,0.46,0,0,0,0,45.28V42.49A0.46,0.46,0,0,1,.23,42c0-.23.23-0.23,0.23-0.46A0.23,0.23,0,0,1,.7,41.33c0.23-.23.23-0.46,0.46-0.7L33.67,8.36a4.35,4.35,0,0,1,6,0,4.64,4.64,0,0,1,0,6.27L14.63,39.94H83.82A4.22,4.22,0,0,1,88,44.35a4.16,4.16,0,0,1-4.18,4.18H14.63L39.24,73.6C41.33,75,41.33,77.78,39.7,79.64Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-arrow-light-bottom">
        <path d="M0,28L44,72.6,88,28,75.5,15.4,44,47.3,12.5,15.4Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-arrow-light-left">
        <path d="M60,0L15.4,44,60,88,72.6,75.5,40.7,44,72.6,12.5Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-arrow-light-right">
        <path d="M28,88L72.6,44,28,0,15.4,12.5,47.3,44,15.4,75.5Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-arrow-light-top">
        <path d="M88,60L44,15.4,0,60,12.5,72.6,44,40.7,75.5,72.6Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-arrow-right">
        <path d="M48.3,8.36a4.64,4.64,0,0,1,6.27,0L86.84,40.87a1.23,1.23,0,0,1,.46.7l0.23,0.23c0,0.23.23,0.23,0.23,0.46a0.46,0.46,0,0,0,.23.46v2.79a0.46,0.46,0,0,1-.23.46c0,0.23-.23.23-0.23,0.46a0.23,0.23,0,0,1-.23.23c-0.23.23-.23,0.46-0.46,0.7L54.33,79.64a4.35,4.35,0,0,1-6,0,4.64,4.64,0,0,1,0-6.27L73.37,48.06H4.18A4.22,4.22,0,0,1,0,43.65a4.16,4.16,0,0,1,4.18-4.18H73.37L48.76,14.4C46.67,13,46.67,10.22,48.3,8.36Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-arrow-top">
        <path d="M8.36,39.7a4.64,4.64,0,0,1,0-6.27L40.87,1.16A1.23,1.23,0,0,1,41.56.7l0.23-.23C42,0.46,42,.23,42.26.23A0.46,0.46,0,0,0,42.72,0h2.79A0.46,0.46,0,0,1,46,.23c0.23,0,.23.23,0.46,0.23a0.23,0.23,0,0,1,.23.23c0.23,0.23.46,0.23,0.7,0.46L79.64,33.67a4.35,4.35,0,0,1,0,6,4.64,4.64,0,0,1-6.27,0L48.06,14.63V83.82A4.22,4.22,0,0,1,43.65,88a4.16,4.16,0,0,1-4.18-4.18V14.63L14.4,39.24C13,41.33,10.22,41.33,8.36,39.7Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-availability-team">
        <path d="M59.08,61.8L58.31,61l-0.09-.09a28.07,28.07,0,0,1-31.64-2.09c-1.33.92-2.7,1.95-4,3a4.59,4.59,0,0,0-1.68,3.55V72H67.1V69.81L60.31,63Z"></path>
        <path d="M45,22H43a8.57,8.57,0,0,0-8.56,8.56v9.5a4.28,4.28,0,0,0,1.91,3.57V51.8a16.77,16.77,0,0,0,15.34,0V43.7a4.28,4.28,0,0,0,1.91-3.57v-9.5A8.54,8.54,0,0,0,45,22Z"></path>
        <path d="M86.59,55.48a63.86,63.86,0,0,0-11.34-7.28,0.34,0.34,0,0,1-.2-0.32V40.19a3.59,3.59,0,0,0,1.6-3v-8A7.17,7.17,0,0,0,69.48,22H67.83a28.1,28.1,0,0,1,.27,29.06l0.09,0.09,0.36,0.36L69,52l1.23,1.23L80.93,63.95H88V58.49A3.93,3.93,0,0,0,86.59,55.48Z"></path>
        <path d="M81.83,69.9L67.65,55.71l-1.23-1.23L66,54.07h0l-0.36-.36-1,1-2.06-2.06a24.48,24.48,0,1,0-2.88,2.88l2.06,2.06-1,1h0l0.78,0.78,1.23,1.23L77,74.69a3.4,3.4,0,0,0,4.8,0,3.41,3.41,0,0,0,0-4.8h0ZM58.36,51.24a20.39,20.39,0,1,1,0-28.85A20.34,20.34,0,0,1,58.36,51.24Z"></path>
        <path d="M20.08,22H18.52a7.17,7.17,0,0,0-7.18,7.18v8a3.59,3.59,0,0,0,1.6,3v7.68a0.36,0.36,0,0,1-.2.32A62.76,62.76,0,0,0,1.41,55.48a3.92,3.92,0,0,0-1.41,3v5.46H17.52a8.08,8.08,0,0,1,2.81-4.74c1.19-1,2.4-1.89,3.59-2.74A28.07,28.07,0,0,1,20.08,22Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-burger-menu">
        <path d="M82.6,79.9H5.4A5.46,5.46,0,0,1,0,74.4V73a5.4,5.4,0,0,1,5.4-5.5H82.6A5.46,5.46,0,0,1,88,73v1.4A5.46,5.46,0,0,1,82.6,79.9Zm0-29.7H5.4A5.49,5.49,0,0,1,0,44.6V43.3a5.4,5.4,0,0,1,5.4-5.5H82.6A5.46,5.46,0,0,1,88,43.3v1.3A5.49,5.49,0,0,1,82.6,50.2Zm0-29.7H5.4A5.49,5.49,0,0,1,0,14.9V13.6A5.4,5.4,0,0,1,5.4,8.1H82.6A5.46,5.46,0,0,1,88,13.6v1.3A5.49,5.49,0,0,1,82.6,20.5Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-calendar-light">
        <path d="M80.69,87.8H7.35A7.34,7.34,0,0,1,0,80.49V15.72A7.34,7.34,0,0,1,7.35,8.41H19.24V2.93a2.76,2.76,0,0,1,5.51,0V8.41h16.5V2.93a2.74,2.74,0,1,1,5.48,0V8.41h16.5V2.93a2.76,2.76,0,0,1,5.51,0V8.41H80.69A7.33,7.33,0,0,1,88,15.72V80.49A7.33,7.33,0,0,1,80.69,87.8Zm1.8-72.08a1.83,1.83,0,0,0-1.8-1.84H68.76v5.48a2.76,2.76,0,0,1-5.51,0V13.88H46.74v5.48a2.74,2.74,0,1,1-5.48,0V13.88H24.76v5.48a2.76,2.76,0,0,1-5.51,0V13.88H7.35a1.84,1.84,0,0,0-1.84,1.84V80.49a1.84,1.84,0,0,0,1.84,1.84H80.69a1.83,1.83,0,0,0,1.8-1.84V15.72ZM57.77,60.43h11v8.21h-11V60.43Zm0-13.69h11V55h-11V46.74Zm0-13.69h11v8.21h-11V33ZM38.49,60.43h11v8.21h-11V60.43Zm0-13.69h11V55h-11V46.74Zm0-13.69h11v8.21h-11V33ZM19.24,60.43h11v8.21h-11V60.43Zm0-13.69h11V55h-11V46.74Zm0-13.69h11v8.21h-11V33Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-calendar">
        <path d="M82.38,85.9H5.62A5.53,5.53,0,0,1,0,80.46v-66A5.53,5.53,0,0,1,5.62,9h9.6v9.26a6.21,6.21,0,0,0,6.29,6.08h1.26A6.18,6.18,0,0,0,29,18.27V9H59.67v9.18A6.18,6.18,0,0,0,66,24.26h1.26c3.44,0,6.24-1.84,6.24-5.2V9h8.93A5.53,5.53,0,0,1,88,14.46v66A5.53,5.53,0,0,1,82.38,85.9Zm0-55.27H5.62V80.46H82.38V30.63Zm-8.84,44.5H63.4a0.73,0.73,0,0,1-.71-0.71V65.92a0.7,0.7,0,0,1,.71-0.67H73.54a0.7,0.7,0,0,1,.71.67v8.51A0.73,0.73,0,0,1,73.54,75.14Zm0-13.91H63.4a0.71,0.71,0,0,1-.71-0.71V52a0.73,0.73,0,0,1,.71-0.71H73.54a0.73,0.73,0,0,1,.71.71v8.46A0.71,0.71,0,0,1,73.54,61.22Zm0-13.91H63.4a0.7,0.7,0,0,1-.71-0.67V38.13a0.73,0.73,0,0,1,.71-0.71H73.54a0.73,0.73,0,0,1,.71.71v8.51A0.7,0.7,0,0,1,73.54,47.31ZM57,75.14H46.89a0.73,0.73,0,0,1-.71-0.71V65.92a0.7,0.7,0,0,1,.71-0.67H57a0.7,0.7,0,0,1,.71.67v8.51A0.73,0.73,0,0,1,57,75.14Zm0-13.91H46.89a0.71,0.71,0,0,1-.71-0.71V52a0.73,0.73,0,0,1,.71-0.71H57a0.73,0.73,0,0,1,.71.71v8.46A0.71,0.71,0,0,1,57,61.22Zm0-13.91H46.89a0.7,0.7,0,0,1-.71-0.67V38.13a0.73,0.73,0,0,1,.71-0.71H57a0.73,0.73,0,0,1,.71.71v8.51A0.7,0.7,0,0,1,57,47.31ZM40.52,75.14H30.42a0.74,0.74,0,0,1-.75-0.71V65.92a0.71,0.71,0,0,1,.75-0.67h10.1a0.7,0.7,0,0,1,.71.67v8.51A0.73,0.73,0,0,1,40.52,75.14Zm0-13.91H30.42a0.72,0.72,0,0,1-.75-0.71V52a0.74,0.74,0,0,1,.75-0.71h10.1a0.73,0.73,0,0,1,.71.71v8.46A0.71,0.71,0,0,1,40.52,61.22ZM24,75.14H13.91a0.74,0.74,0,0,1-.75-0.71V65.92a0.71,0.71,0,0,1,.75-0.67H24a0.67,0.67,0,0,1,.71.67v8.51A0.71,0.71,0,0,1,24,75.14Zm0-13.91H13.91a0.72,0.72,0,0,1-.75-0.71V52a0.74,0.74,0,0,1,.75-0.71H24a0.71,0.71,0,0,1,.71.71v8.46A0.68,0.68,0,0,1,24,61.22Zm43.2-39.68H66a3.42,3.42,0,0,1-3.48-3.35V5.45A3.42,3.42,0,0,1,66,2.1h1.26a3.41,3.41,0,0,1,3.44,3.35V18.19A3.41,3.41,0,0,1,67.22,21.54Zm-44.46.08H21.5A3.44,3.44,0,0,1,18,18.27V5.53A3.44,3.44,0,0,1,21.5,2.18h1.26a3.42,3.42,0,0,1,3.48,3.35V18.27A3.42,3.42,0,0,1,22.75,21.62Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-camera">
        <path d="M26.13,49.5A17.88,17.88,0,1,0,44,31.63,17.91,17.91,0,0,0,26.13,49.5ZM82.5,19.25H63.25c-1.37-5.5-2.75-11-8.25-11H33c-5.5,0-6.87,5.5-8.25,11H5.5A5.52,5.52,0,0,0,0,24.75v49.5a5.52,5.52,0,0,0,5.5,5.5h77a5.52,5.52,0,0,0,5.5-5.5V24.75A5.52,5.52,0,0,0,82.5,19.25ZM44,73.92A24.42,24.42,0,1,1,68.42,49.5,24.44,24.44,0,0,1,44,73.92ZM82.5,35.75h-11v-5.5h11v5.5Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-candle">
        <path d="M55.81,88H32.19a4.5,4.5,0,0,1-4.67-4.34V34.83a4.5,4.5,0,0,1,4.67-4.34H55.81a4.5,4.5,0,0,1,4.67,4.34V83.66A4.5,4.5,0,0,1,55.81,88ZM44,25.54c-5.27,0-9.5-4-9.5-8.79,0-4.28,6.76-9.06,7.69-15.55a1.35,1.35,0,0,1,1-1.1,1.44,1.44,0,0,1,1.54.27C52.9,7.31,53.5,12.31,53.5,16.76,53.5,21.59,49.27,25.54,44,25.54Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-chat-bubble">
        <path d="M83.5,65.15H39.23a4.38,4.38,0,0,0-1.75.53L11.14,83.54A3.76,3.76,0,0,1,9,84.29,3,3,0,0,1,6.51,83a3.45,3.45,0,0,1-.28-3.28l5.32-14.61H4.47A4.47,4.47,0,0,1,0,60.67V8.15A4.47,4.47,0,0,1,4.47,3.71h79A4.49,4.49,0,0,1,88,8.15V60.67A4.5,4.5,0,0,1,83.5,65.15ZM82.62,9.06H5.38V59.8h8.2a3.85,3.85,0,0,1,3.72,5.26L13.51,75.47l21-14.2a9.24,9.24,0,0,1,4.76-1.47H82.62V9.06ZM15.86,44.38a2.66,2.66,0,0,1,2.66-2.69h50a2.67,2.67,0,1,1,0,5.35h-50A2.66,2.66,0,0,1,15.86,44.38ZM57,29.64H18.52a2.66,2.66,0,0,1,0-5.32H57A2.66,2.66,0,1,1,57,29.64Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-check">
        <path d="M87.13,20.42l-9.81-9.81a3,3,0,0,0-4.2,0L32.26,51.47,14.88,34.09a3,3,0,0,0-4.2,0L.87,43.9a3,3,0,0,0,0,4.2L30.16,77.39a3,3,0,0,0,4.2,0L87.13,24.62a3,3,0,0,0,0-4.2Zm0,0"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-cross">
        <path d="M55.61,44L86,13.68a7.26,7.26,0,0,0-.19-10.3L84.55,2.17a7.23,7.23,0,0,0-10.2-.09L44,32.42,13.67,2.09a7.15,7.15,0,0,0-10.2.09L2.25,3.4a7.26,7.26,0,0,0-.19,10.3L32.39,44,2.09,74.33a7.15,7.15,0,0,0,.09,10.2L3.4,85.75a7.26,7.26,0,0,0,10.29.19L44,55.63,74.32,86a7.26,7.26,0,0,0,10.3-.19l1.22-1.22a7.23,7.23,0,0,0,.09-10.2Z"></path>
    </symbol>
    <symbol viewBox="0 0 115.53 88" id="shrs-icon-delegate">
        <g>
            <path d="M826.68,485.11l19.26-19-19.26-19-5.44,5.4L835,466.11l-13.78,13.61Z" transform="translate(-747.47 -424.1)"></path>
        </g>
        <g>
            <path d="M843.74,485.11l19.26-19-19.26-19-5.44,5.4,13.78,13.61-13.78,13.61Z" transform="translate(-747.47 -424.1)"></path>
        </g>
        <g>
            <path d="M813.66,506.64a70.61,70.61,0,0,1-10.32,2.86,83.54,83.54,0,0,1-41.36,0,70.61,70.61,0,0,1-10.32-2.86c-2.68-1-4.27-1.41-4.18-4.62a83.63,83.63,0,0,1,1.36-12.41c.88-5,2-12.23,6.34-15.49,2.46-1.85,5.72-2.46,8.58-3.61a34.48,34.48,0,0,0,3.83-1.67,20.52,20.52,0,0,0,30.16,0,34.48,34.48,0,0,0,3.83,1.67c2.86,1.14,6.08,1.76,8.58,3.58,4.36,3.26,5.46,10.52,6.34,15.49A83.63,83.63,0,0,1,817.86,502C817.95,505.24,816.36,505.68,813.66,506.64Zm-31-35.68c-13,0-17.69-13-18.92-23.72-1.5-13.2,4.71-23.14,18.92-23.14s20.42,9.94,18.92,23.14C800.35,457.94,795.66,471,782.66,471Z" transform="translate(-747.47 -424.1)"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-acompte">
        <path d="M44,2A42,42,0,1,1,2,44,42,42,0,0,1,44,2m0-2A44,44,0,1,0,88,44,44,44,0,0,0,44,0h0Z"></path>
        <rect x="25.07" y="42.41" width="4.93" height="4.83"></rect>
        <rect x="25.07" y="34.51" width="4.93" height="4.83"></rect>
        <rect x="25.07" y="50.26" width="4.93" height="4.86"></rect>
        <polygon points="33.34 55.15 33.34 55.16 38.28 55.16 38.28 50.26 33.34 50.26 33.34 55.15"></polygon>
        <rect x="25.07" y="24.23" width="21.46" height="6.26"></rect>
        <rect x="41.62" y="34.51" width="4.91" height="4.84"></rect>
        <polygon points="33.34 47.27 33.34 47.28 38.28 47.28 38.28 42.41 33.34 42.41 33.34 47.27"></polygon>
        <path d="M44,7.5A36.5,36.5,0,1,0,80.5,44,36.5,36.5,0,0,0,44,7.5Zm-20.58,52a1.75,1.75,0,0,1-1.75-1.73V22.36a1.75,1.75,0,0,1,1.75-1.73H48.14a1.75,1.75,0,0,1,1.77,1.73h0V42.13a13.81,13.81,0,0,0-3.39,2.12V42.37H41.62v12.5a14.16,14.16,0,0,0,.8,4.63h-19Zm32.36,7.87A12.69,12.69,0,0,1,44,59.5a11.86,11.86,0,0,1-.89-4.37,1.06,1.06,0,0,1,0-.25,12.43,12.43,0,0,1,3.44-8.58A13.35,13.35,0,0,1,50,43.77a12.93,12.93,0,0,1,5.84-1.4A12.5,12.5,0,0,1,55.78,67.37Z"></path>
        <path d="M58.57,59.79a6.62,6.62,0,0,1-2.13.27,3.86,3.86,0,0,1-2.44-.9,4.57,4.57,0,0,1-1.61-2.5h2.73l0.38-1H52.22V54h3.92l0.38-1H52.41a4.55,4.55,0,0,1,1.52-2.39,3.83,3.83,0,0,1,2.47-.9,6.56,6.56,0,0,1,1.93.26,4.58,4.58,0,0,1,1.55.83H60V49a5.88,5.88,0,0,0-1.55-.57,10,10,0,0,0-2-.18,5.79,5.79,0,0,0-3.71,1.28A6,6,0,0,0,50.62,53H49.81l-0.38,1h1v1.65H49.78l-0.38,1h1.15a5.72,5.72,0,0,0,2.07,3.56,5.88,5.88,0,0,0,3.75,1.31,9.54,9.54,0,0,0,1.89-.16A7.74,7.74,0,0,0,60,60.78h0V59H59.93A4,4,0,0,1,58.57,59.79Z"></path>
        <rect x="33.34" y="34.51" width="4.94" height="4.87"></rect>
    </symbol>
    <symbol viewBox="0 0 48 48" id="shrs-icon-demarche-entretien-pro">
        <path d="M 24.01987,3.9346754 A 20.045455,20.045455 0 1 1 3.9744152,23.980129 20.045455,20.045455 0 0 1 24.01987,3.9346754 m 0,-0.9545454 a 21,21 0 1 0 21,20.999999 21,21 0 0 0 -21,-20.999999 l 0,0 z"></path>
        <path d="M 24 6.5800781 A 17.420455 17.420455 0 1 0 41.419922 24 A 17.420455 17.420455 0 0 0 24 6.5800781 z M 19.679688 9.0332031 L 31.228516 9.0332031 L 31.228516 16.367188 L 29.007812 16.367188 L 29.007812 19.013672 L 26.902344 16.910156 L 26.902344 12.230469 L 26.740234 12.230469 L 26.496094 12.230469 L 19.679688 12.230469 L 19.679688 9.0332031 z M 19.679688 12.722656 L 26.496094 12.722656 L 26.496094 18.775391 L 26.523438 18.775391 L 26.523438 18.785156 L 22.472656 18.785156 L 19.533203 21.742188 L 19.533203 18.785156 L 17.310547 18.785156 L 17.310547 12.724609 L 19.679688 12.724609 L 19.679688 12.722656 z M 14.466797 18.207031 C 16.745061 18.207031 17.724782 19.795841 17.486328 21.888672 C 17.287666 23.597392 16.546399 25.677734 14.466797 25.677734 C 12.400461 25.677734 11.657667 23.597392 11.458984 21.888672 C 11.220559 19.795841 12.201773 18.207031 14.466797 18.207031 z M 33.123047 18.257812 C 35.379929 18.257812 36.370368 19.840497 36.132812 21.925781 C 35.934829 23.628339 35.19515 25.701172 33.123047 25.701172 C 31.064186 25.701172 30.324898 23.628339 30.126953 21.925781 C 29.889397 19.840497 30.866203 18.257813 33.123047 18.257812 z M 12.068359 25.345703 C 12.306783 25.59737 12.531292 25.796554 12.835938 25.96875 C 13.33927 26.26014 13.842974 26.392578 14.425781 26.392578 L 14.466797 26.392578 L 14.533203 26.392578 C 15.102798 26.392578 15.60604 26.260136 16.109375 25.96875 C 16.414012 25.796554 16.638497 25.59737 16.876953 25.345703 C 17.088866 25.451677 17.274415 25.531858 17.486328 25.611328 C 17.949948 25.783523 18.465919 25.888301 18.863281 26.179688 C 19.552065 26.696275 19.725402 27.849772 19.871094 28.644531 C 19.990325 29.333315 20.055521 29.928823 20.082031 30.630859 C 20.095282 31.134195 19.843366 31.200432 19.40625 31.359375 C 18.849909 31.571327 18.360151 31.704991 17.777344 31.824219 C 16.624978 32.0891 15.645665 32.207865 14.466797 32.234375 C 13.30118 32.207871 12.320777 32.0891 11.181641 31.824219 C 10.585585 31.704991 10.094955 31.571327 9.5253906 31.359375 C 9.1015246 31.213684 8.8500212 31.134195 8.8632812 30.630859 C 8.8897734 29.928823 8.9550046 29.333315 9.0742188 28.644531 C 9.219921 27.849772 9.3936784 26.696275 10.095703 26.179688 C 10.493078 25.888303 11.008628 25.783523 11.458984 25.611328 C 11.670917 25.531856 11.856428 25.451683 12.068359 25.345703 z M 30.734375 25.357422 C 30.971932 25.621385 31.210135 25.820611 31.513672 25.992188 C 32.015194 26.282522 32.503279 26.414062 33.083984 26.414062 L 33.123047 26.414062 L 33.175781 26.414062 C 33.756487 26.414062 34.245041 26.282526 34.759766 25.992188 C 35.063304 25.820612 35.287835 25.621853 35.525391 25.371094 C 35.73654 25.476692 35.908423 25.555581 36.132812 25.634766 C 36.594724 25.80634 37.096223 25.91279 37.492188 26.203125 C 38.191654 26.71785 38.362609 27.86631 38.507812 28.658203 C 38.626568 29.344503 38.692342 29.937215 38.71875 30.636719 C 38.73196 31.13824 38.482415 31.218116 38.046875 31.363281 C 37.492576 31.574469 37.004066 31.705422 36.410156 31.824219 C 35.275151 32.088145 34.297663 32.207967 33.123047 32.234375 C 31.961635 32.207965 30.984651 32.088145 29.849609 31.824219 C 29.255697 31.705422 28.768674 31.574469 28.201172 31.363281 C 27.778837 31.204911 27.527813 31.13824 27.541016 30.636719 C 27.567426 29.937215 27.633155 29.344503 27.751953 28.658203 C 27.89712 27.86631 28.068075 26.71785 28.767578 26.203125 C 29.150303 25.91279 29.678211 25.80634 30.126953 25.634766 C 30.351306 25.542376 30.523189 25.463015 30.734375 25.357422 z M 14.478516 26.591797 C 14.306317 26.591797 13.288064 26.657823 14.228516 27.982422 L 13.75 30.126953 L 15.207031 30.126953 L 14.730469 27.982422 C 15.670922 26.657823 14.663963 26.591797 14.478516 26.591797 z "></path>
        <path d="M 14.177727,31.397727"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-teleworkplanning"><metadata id="shrs-icon-demarche-teleworkPlanning-metadata3433"> <rdf:rdf> <cc:work rdf:about=""> <dc:format>image/svg+xml</dc:format> <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type> </cc:work> </rdf:rdf> </metadata>  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="shrs-icon-demarche-teleworkPlanning-namedview3429" showgrid="false" inkscape:zoom="5.3636364" inkscape:cx="15.157382" inkscape:cy="49.389393" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-teamplanning"></sodipodi:namedview>  <path style="stroke-width:0.77201229" d="M 35.386719 41.957031 C 34.629923 41.957031 34.011719 42.650596 34.011719 43.603516 L 34.011719 49.839844 C 34.011719 50.792764 34.629923 51.486328 35.386719 51.486328 L 35.869141 51.486328 C 36.625938 51.486328 37.246094 50.792754 37.246094 49.839844 L 37.246094 43.603516 C 37.246094 42.650596 36.625938 41.957031 35.869141 41.957031 L 35.386719 41.957031 z M 52.587891 41.957031 C 51.983135 41.969421 51.211317 42.423826 51.210938 43.603516 C 51.210938 43.603516 51.198687 48.554974 51.210938 49.839844 C 51.222837 51.091244 52.233228 51.465328 52.587891 51.486328 C 52.587891 51.486328 52.909632 51.489328 53.070312 51.486328 C 53.775447 51.470908 54.437312 50.722014 54.445312 49.839844 C 54.445312 49.839834 54.483362 45.318146 54.445312 43.603516 C 54.424563 42.667586 53.838627 41.976531 53.070312 41.957031 C 53.070312 41.957031 53.192648 41.944431 52.587891 41.957031 z M 29.126953 45.335938 C 27.957359 45.335938 26.925781 46.548784 26.925781 48.021484 L 26.925781 74.939453 L 61.433594 74.939453 L 61.394531 48.021484 C 61.429231 46.640414 60.768377 45.287747 59.261719 45.335938 L 55.478516 45.335938 L 55.478516 49.839844 C 55.478515 51.340174 54.347967 52.842562 52.996094 52.851562 C 52.245792 52.857262 53.23146 52.827244 52.5625 52.839844 C 51.209141 52.865164 50.111328 51.602204 50.111328 49.839844 L 50.111328 45.335938 L 38.277344 45.335938 L 38.277344 49.839844 C 38.277344 51.485794 37.176336 52.871094 35.869141 52.871094 L 35.386719 52.871094 C 34.010725 52.871094 32.910156 51.572414 32.910156 49.839844 L 32.910156 45.335938 L 29.126953 45.335938 z M 51.128906 53.611328 C 51.824565 53.616928 52.515588 53.72197 53.183594 53.923828 C 53.851353 54.118998 54.480935 54.436073 55.041016 54.861328 C 55.569385 55.304237 56.02463 55.832977 56.392578 56.425781 C 56.740706 57.078275 56.914336 57.814774 56.894531 58.560547 C 56.917981 59.369535 56.708684 60.16712 56.292969 60.851562 C 55.892253 61.538367 55.241696 62.027609 54.488281 62.207031 C 55.376446 62.416863 56.170945 62.930487 56.744141 63.666016 C 57.269253 64.424333 57.550422 65.33727 57.546875 66.271484 C 57.548175 67.154386 57.35998 68.307627 56.996094 69.105469 C 56.658423 69.810256 56.179599 70.430963 55.591797 70.927734 C 54.976295 71.431542 54.279344 71.819461 53.535156 72.074219 C 52.751657 72.305449 51.943317 72.429817 51.128906 72.441406 C 50.228035 72.442506 49.33107 72.300968 48.470703 72.023438 C 47.718391 71.746223 47.033876 71.302363 46.464844 70.720703 C 45.881432 70.160388 45.435726 69.462496 45.162109 68.6875 C 44.846506 67.804593 44.69293 66.586188 44.710938 65.644531 L 48.070312 65.644531 C 47.994363 66.092781 47.994363 66.55175 48.070312 67 C 48.181976 67.41514 48.368975 68.087382 48.621094 68.429688 C 48.882634 68.7568 49.206926 69.021362 49.574219 69.208984 C 49.987789 69.385469 50.431832 69.47454 50.878906 69.470703 C 51.687456 69.547023 52.489432 69.261972 53.083984 68.6875 C 53.645104 68.179867 53.942876 67.14587 53.886719 66.375 C 53.927959 65.828771 53.804208 65.283719 53.535156 64.8125 C 53.273037 64.464753 52.928045 64.194904 52.533203 64.03125 C 52.118063 63.860514 51.67499 63.77229 51.228516 63.771484 L 49.925781 63.771484 L 49.925781 61.21875 L 51.128906 61.21875 C 51.52515 61.17041 51.91285 61.065388 52.28125 60.90625 C 52.643273 60.755543 52.968185 60.522728 53.234375 60.226562 C 53.467371 59.855404 53.588728 59.421628 53.583984 58.978516 C 53.620384 58.297037 53.342485 57.639072 52.833984 57.207031 C 52.355594 56.793863 51.750901 56.570512 51.128906 56.580078 C 50.350519 56.519938 49.595899 56.874247 49.123047 57.519531 C 48.657296 58.181934 48.425859 58.991509 48.470703 59.810547 L 45.162109 59.810547 C 45.176739 58.939537 45.328395 58.077116 45.613281 57.257812 C 45.894367 56.542451 46.302187 55.888245 46.816406 55.330078 C 47.346846 54.774374 47.977751 54.331301 48.671875 54.027344 C 49.463396 53.750339 50.293697 53.610437 51.128906 53.611328 z M 36.914062 53.613281 C 36.969133 53.613169 37.02502 53.613777 37.080078 53.615234 C 37.831303 53.601284 38.57944 53.726416 39.291016 53.986328 C 39.972707 54.227842 40.605196 54.604849 41.158203 55.097656 C 41.689021 55.602319 42.123625 56.213653 42.435547 56.898438 C 42.73282 57.652014 42.884273 58.464376 42.878906 59.283203 C 42.894256 60.097256 42.759418 60.90571 42.484375 61.664062 C 42.211634 62.304212 41.846948 62.895774 41.404297 63.414062 C 40.955025 63.943677 40.44326 64.404909 39.880859 64.789062 L 38.111328 66.0625 L 36.441406 67.386719 C 35.898937 67.845498 35.433084 68.401265 35.064453 69.029297 L 43.025391 69.029297 L 43.025391 72.205078 L 30.640625 72.205078 C 30.632325 71.252966 30.764401 70.305453 31.033203 69.398438 C 31.331413 68.651971 31.730527 67.95694 32.214844 67.333984 C 32.698854 66.690466 33.2613 66.120243 33.886719 65.638672 L 35.949219 64.048828 L 37.128906 63.201172 L 38.306641 62.248047 C 38.677218 61.851258 38.976911 61.38637 39.191406 60.873047 C 39.431221 60.422568 39.551889 59.907778 39.537109 59.388672 C 39.694245 57.816403 38.495231 56.472623 37.03125 56.580078 C 36.579536 56.556868 36.13153 56.688203 35.751953 56.953125 C 35.412839 57.202502 35.127645 57.52713 34.916016 57.90625 C 34.717482 58.344226 34.570006 58.806725 34.474609 59.283203 C 34.426199 59.776136 34.426199 60.272691 34.474609 60.765625 L 31.230469 60.765625 C 31.174079 59.783681 31.273759 58.799227 31.525391 57.853516 C 31.732672 57.098773 32.049195 56.382532 32.460938 55.732422 C 32.980696 55.03272 33.656783 54.48785 34.425781 54.146484 C 35.214313 53.791118 36.059367 53.609664 36.914062 53.613281 z " id="shrs-icon-demarche-teleworkPlanning-path5976-9-7"></path>  <g id="shrs-icon-demarche-teleworkPlanning-g2048" style="fill-opacity:1"> <path inkscape:connector-curvature="0" id="shrs-icon-demarche-teleworkPlanning-path898" d="M 44,7.5 C 28.053829,7.5011298 13.954203,17.853778 9.178178,33.067913 4.4021529,48.282049 10.055402,64.835525 23.14,73.95 V 46.8 l -7.88,6.44 -3.59,-4.36 11.6,-9.51 V 29.05 h 5.65 v 5.67 L 44.19,22.36 76.33,48.89 72.74,53.25 65.13,47 V 73.75 C 78.086607,64.556969 83.606505,48.037515 78.77915,32.902057 73.951796,17.766598 59.886643,7.4936664 44,7.5 Z" style="fill-opacity:1" sodipodi:nodetypes="cscccccccccccccsc"></path> </g> <path style="fill:none;fill-opacity:1;stroke:none;stroke-width:0.18644068" d="m -53.227852,21.446897 c -3.870673,-0.36402 -9.762748,-1.81726 -10.546778,-2.60129 -0.198001,-0.198 -0.360002,-0.57305 -0.360002,-0.83345 0,-0.95868 0.412054,-4.14257 0.74782,-5.77831 0.798516,-3.8900898 1.636868,-5.0393098 4.382458,-6.0075098 0.921144,-0.32483 1.939748,-0.69325 2.263566,-0.8187 0.54806,-0.21234 0.635411,-0.18714 1.263658,0.36447 2.849321,2.50173 7.801778,2.50173 10.651099,0 0.632473,-0.55532 0.71191,-0.57773 1.263657,-0.35643 0.323818,0.12987 1.332639,0.49172 2.241826,0.80409 2.766558,0.95052 3.596879,2.08423 4.404682,6.0140798 0.460669,2.2411 0.798719,5.40695 0.649768,6.08511 -0.07212,0.32838 -0.318094,0.64774 -0.617109,0.80123 -1.657355,0.85077 -6.790183,1.99559 -10.471764,2.33562 -2.40044,0.2217 -3.437566,0.22012 -5.872881,-0.009 z" id="shrs-icon-demarche-teleworkPlanning-path2034" inkscape:connector-curvature="0"></path> <path style="fill:none;fill-opacity:1;stroke:none;stroke-width:0.18644068" d="m -51.692612,5.6164872 c -2.539199,-0.64056 -4.536536,-2.98825 -5.468289,-6.42749005 -0.45906,-1.69445995 -0.563046,-5.19632995 -0.192184,-6.47204995 0.945783,-3.2533802 3.734404,-4.9615702 7.781591,-4.7666902 3.014135,0.14514 5.064081,1.36208 6.165568,3.6601302 0.531073,1.10799 0.602537,1.4574 0.672987,3.29047 0.179239,4.66369995 -1.793795,8.93609 -4.754322,10.29496 -1.096867,0.50346 -3.08773,0.70261 -4.205351,0.42067 z" id="shrs-icon-demarche-teleworkPlanning-path2036" inkscape:connector-curvature="0"></path> <g id="shrs-icon-demarche-teleworkPlanning-g2041"> <path inkscape:connector-curvature="0" id="shrs-icon-demarche-teleworkPlanning-path2038" d="M 39.712826,87.728284 C 30.886884,86.700377 22.946846,83.41568 16.306778,78.045487 14.09827,76.259344 10.686433,72.726959 9.0050953,70.485819 4.3189553,64.239428 1.2860339,56.684675 0.36684384,48.968658 0.08858012,46.63281 0.08488942,41.303794 0.36000033,39.087302 2.1883267,24.356987 10.633827,12.045279 23.678927,5.0933583 31.808861,0.76080033 42.057177,-0.87229813 51.272148,0.69629978 67.063179,3.3842932 80.278631,14.555023 85.44142,29.578827 c 1.713177,4.985379 2.392021,9.059516 2.392021,14.355932 0,6.941653 -1.362677,12.990941 -4.283798,19.01695 -2.237608,4.615984 -4.55309,7.939013 -8.031467,11.526243 -6.621424,6.828636 -14.671572,11.088416 -24.246028,12.829914 -1.694114,0.308141 -3.063539,0.401153 -6.525424,0.443199 -2.409746,0.02927 -4.675,0.01902 -5.033898,-0.02278 z m 9.881356,-2.057938 C 56.477803,84.81895 63.24836,82.072367 69.077232,77.766743 77.212018,71.757807 83.000469,62.775518 85.106807,52.892691 85.79949,49.642659 86.016282,47.535384 86.018784,44.02798 86.022363,39.021796 85.45088,35.446013 83.897437,30.754466 78.873989,15.583164 66.007557,4.8078709 49.960416,2.3331173 47.358894,1.9319166 41.192273,1.8765791 38.687402,2.2319567 28.028419,3.7441955 18.482313,8.9366284 11.890517,16.807641 6.7167127,22.985483 3.5926253,29.953938 2.3200565,38.155098 c -0.3849881,2.481084 -0.4455723,8.5525 -0.1088345,10.90678 1.4353913,10.035438 5.421335,18.11112 12.311745,24.944092 3.050919,3.025485 6.619403,5.568678 10.659925,7.597128 7.615384,3.823128 15.612378,5.155536 24.41129,4.067248 z" style="fill:none;fill-opacity:1;stroke:none;stroke-width:0.18644068"></path> </g> <g id="shrs-icon-demarche-teleworkPlanning-g2057"> <path inkscape:connector-curvature="0" style="fill-opacity:1" d="M 44,0 A 44,44 0 1 0 88,44 44,44 0 0 0 44,0 Z m 0,86 A 42,42 0 1 1 86,44 42,42 0 0 1 44,86 Z" id="shrs-icon-demarche-teleworkPlanning-path888"></path> </g> </symbol>
	<symbol id="shrs-icon-demarche-teleworkmodality" viewBox="0 0 88 88">
	  <defs id="defs907"></defs>
	  <g id="g902"></g>
	  <g id="g2048" style="fill-opacity:1">
		<path id="path898" d="M 44,7.5 C 28.053829,7.5011298 13.954203,17.853778 9.178178,33.067913 4.4021529,48.282049 10.055402,64.835525 23.14,73.95 V 46.8 l -7.88,6.44 -3.59,-4.36 11.6,-9.51 V 29.05 h 5.65 v 5.67 L 44.19,22.36 76.33,48.89 72.74,53.25 65.13,47 V 73.75 C 78.086607,64.556969 83.606505,48.037515 78.77915,32.902057 73.951796,17.766598 59.886643,7.4936664 44,7.5 Z" style="fill-opacity:1" inkscape:connector-curvature="0" sodipodi:nodetypes="cscccccccccccccsc"></path>
	  </g>
	  <path style="fill:none;fill-opacity:1;stroke:none;stroke-width:0.18644068" d="m 41.484012,78.870628 c -3.870673,-0.364021 -9.762748,-1.81726 -10.546778,-2.60129 -0.198001,-0.198001 -0.360002,-0.573055 -0.360002,-0.833452 0,-0.958685 0.412054,-4.142572 0.74782,-5.778306 0.798516,-3.890097 1.636868,-5.039318 4.382458,-6.007518 0.921144,-0.324831 1.939748,-0.693246 2.263566,-0.8187 0.54806,-0.212332 0.635411,-0.187138 1.263658,0.36447 2.849321,2.501737 7.801778,2.501737 10.651099,0 0.632473,-0.555319 0.71191,-0.577725 1.263657,-0.356429 0.323818,0.129877 1.332639,0.49172 2.241826,0.804094 2.766558,0.950523 3.596879,2.08423 4.404682,6.014083 0.460669,2.241099 0.798719,5.406943 0.649768,6.085108 -0.07212,0.32838 -0.318094,0.647737 -0.617109,0.801231 -1.657355,0.850769 -6.790183,1.99559 -10.471764,2.335613 -2.40044,0.221699 -3.437566,0.220127 -5.872881,-0.0089 z" id="path2034" inkscape:connector-curvature="0"></path>
	  <path style="fill:none;fill-opacity:1;stroke:none;stroke-width:0.18644068" d="m 43.019252,63.040216 c -2.539199,-0.640558 -4.536536,-2.988252 -5.468289,-6.427491 -0.45906,-1.694458 -0.563046,-5.196327 -0.192184,-6.472047 0.945783,-3.253378 3.734404,-4.961575 7.781591,-4.766689 3.014135,0.145141 5.064081,1.362072 6.165568,3.66013 0.531073,1.107989 0.602537,1.457401 0.672987,3.290471 0.179239,4.6637 -1.793795,8.936086 -4.754322,10.294958 -1.096867,0.503459 -3.08773,0.702608 -4.205351,0.420668 z" id="path2036" inkscape:connector-curvature="0"></path>
	  <g id="g2041">
		<path inkscape:connector-curvature="0" id="path2038" d="M 39.712826,87.728284 C 30.886884,86.700377 22.946846,83.41568 16.306778,78.045487 14.09827,76.259344 10.686433,72.726959 9.0050953,70.485819 4.3189553,64.239428 1.2860339,56.684675 0.36684384,48.968658 0.08858012,46.63281 0.08488942,41.303794 0.36000033,39.087302 2.1883267,24.356987 10.633827,12.045279 23.678927,5.0933583 31.808861,0.76080033 42.057177,-0.87229813 51.272148,0.69629978 67.063179,3.3842932 80.278631,14.555023 85.44142,29.578827 c 1.713177,4.985379 2.392021,9.059516 2.392021,14.355932 0,6.941653 -1.362677,12.990941 -4.283798,19.01695 -2.237608,4.615984 -4.55309,7.939013 -8.031467,11.526243 -6.621424,6.828636 -14.671572,11.088416 -24.246028,12.829914 -1.694114,0.308141 -3.063539,0.401153 -6.525424,0.443199 -2.409746,0.02927 -4.675,0.01902 -5.033898,-0.02278 z m 9.881356,-2.057938 C 56.477803,84.81895 63.24836,82.072367 69.077232,77.766743 77.212018,71.757807 83.000469,62.775518 85.106807,52.892691 85.79949,49.642659 86.016282,47.535384 86.018784,44.02798 86.022363,39.021796 85.45088,35.446013 83.897437,30.754466 78.873989,15.583164 66.007557,4.8078709 49.960416,2.3331173 47.358894,1.9319166 41.192273,1.8765791 38.687402,2.2319567 28.028419,3.7441955 18.482313,8.9366284 11.890517,16.807641 6.7167127,22.985483 3.5926253,29.953938 2.3200565,38.155098 c -0.3849881,2.481084 -0.4455723,8.5525 -0.1088345,10.90678 1.4353913,10.035438 5.421335,18.11112 12.311745,24.944092 3.050919,3.025485 6.619403,5.568678 10.659925,7.597128 7.615384,3.823128 15.612378,5.155536 24.41129,4.067248 z" style="fill:none;fill-opacity:1;stroke:none;stroke-width:0.18644068"></path>
	  </g>
	  <g id="g2057">
		<path style="fill-opacity:1" d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z" id="path888"></path>
		<path inkscape:connector-curvature="0" d="m 56.915838,77.038726 a 28.154036,27.226737 0 0 1 -4.114851,1.102804 40.793646,39.450042 0 0 1 -8.245652,1.002538 40.793646,39.450042 0 0 1 -8.245651,-1.002538 28.154036,27.226737 0 0 1 -4.11485,-1.102804 c -1.068587,-0.385584 -1.702561,-0.543681 -1.666675,-1.781435 a 33.345446,32.247161 0 0 1 0.542267,-4.785216 c 0.350879,-1.927969 0.797451,-4.715804 2.527922,-5.972836 0.980865,-0.713349 2.280713,-0.94856 3.421068,-1.391993 a 13.748069,13.295254 0 0 0 1.527121,-0.643941 7.9027472,7.6424576 0 0 0 6.008798,2.548771 7.9027472,7.6424576 0 0 0 6.016774,-2.544915 13.748069,13.295254 0 0 0 1.52712,0.643942 c 1.140356,0.439576 2.424254,0.678643 3.421069,1.380423 1.738445,1.257034 2.177042,4.056438 2.527921,5.972833 a 33.345446,32.247161 0 0 1 0.542268,4.792932 c 0.03588,1.241612 -0.598088,1.41127 -1.674649,1.781435 z M 44.555335,63.280764 c -5.183436,0 -7.053461,-5.012713 -7.543893,-9.146272 -0.598089,-5.089831 1.877998,-8.922627 7.543893,-8.922627 5.665895,0 8.141984,3.832796 7.543894,8.922627 -0.490433,4.125848 -2.360457,9.146272 -7.543894,9.146272 z" id="path1467" style="fill-opacity:1;stroke-width:0.3921046"></path>
	  </g>
	</symbol>
    <symbol viewBox="0 0 48 48" id="shrs-icon-demarche-fin-cdd">
        <path d="M24,1.1A22.9,22.9,0,1,1,1.1,24h0A23,23,0,0,1,24,1.1M24,0A24,24,0,1,0,48,24h0A23.94,23.94,0,0,0,24,0Z"></path>
        <path d="M22.3,18.7H20.9v8H22c2.5,0,3.7-1.3,3.7-4S24.6,18.7,22.3,18.7Z"></path>
        <path d="M35,18.7H33.6v8h1.1c2.5,0,3.7-1.3,3.7-4S37.3,18.7,35,18.7Z"></path>
        <path d="M24,4.1A19.9,19.9,0,1,0,43.9,24h0A19.89,19.89,0,0,0,24,4.1ZM12.6,26.8a10.66,10.66,0,0,0,3.1-.6v2.2a9.74,9.74,0,0,1-3.4.6,5.35,5.35,0,0,1-4.1-1.6,7.08,7.08,0,0,1-1.4-4.7,7.54,7.54,0,0,1,.7-3.3,5.5,5.5,0,0,1,2-2.3,5.32,5.32,0,0,1,3-.7,7.68,7.68,0,0,1,3.6.9l-.8,2.1c-.5-.2-.9-.4-1.4-.6a3.19,3.19,0,0,0-1.4-.3,2.84,2.84,0,0,0-2.3,1.1,4.83,4.83,0,0,0-.8,3.1C9.5,25.5,10.5,26.8,12.6,26.8Zm14.1.4a7,7,0,0,1-5,1.6H18.2V16.6H22a6.73,6.73,0,0,1,4.7,1.6,5.72,5.72,0,0,1,1.7,4.4,5.67,5.67,0,0,1-1.7,4.6Zm7.8,1.6H31V16.6h3.8a6.73,6.73,0,0,1,4.7,1.6,6,6,0,0,1,1.6,4.4,6,6,0,0,1-1.7,4.6,7.24,7.24,0,0,1-4.9,1.6Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-imsick">
        <g>
            <path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z"></path>
            <path d="M80.5,44A36.5,36.5,0,1,0,13.76,64.44V45.84a7.71,7.71,0,0,1,7.78-7.65H22.7V36.35a.83.83,0,0,1,.83-.81h4.61a.83.83,0,0,1,.81.81v1.84h4.09v-2a5.73,5.73,0,0,1,5.78-5.69H49.17a5.73,5.73,0,0,1,5.78,5.69v2H59V36.35a.83.83,0,0,1,.81-.81h4.61a.83.83,0,0,1,.83.81v1.84h1.17a7.71,7.71,0,0,1,7.78,7.65V64.44A36.33,36.33,0,0,0,80.5,44Z"></path>
            <path d="M44,45.54A13.8,13.8,0,1,0,58,59.35,13.91,13.91,0,0,0,44,45.54ZM54.51,55.93l0,6.8a.83.83,0,0,1-.***********,0,0,1-.58.25H48.28v5.34a.81.81,0,0,1-.81.81H40.54a.81.81,0,0,1-.81-.81V63.54l-5.42,0a.82.82,0,0,1-.58-.25.81.81,0,0,1-.23-.56l0-6.8a.83.83,0,0,1,.83-.81h5.42V49.8a.81.81,0,0,1,.81-.81h6.92a.81.81,0,0,1,.81.81v5.34l5.42,0A.81.81,0,0,1,54.51,55.93Z"></path>
            <path d="M51.68,36.14a2.49,2.49,0,0,0-2.5-2.46H38.83a2.49,2.49,0,0,0-2.5,2.46v2H51.68Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-workingtime">
        <path d="M 44,7.5 C 23.8,7.5 7.5,23.8 7.5,44 7.5,54.8 10.457089,61.940644 14.796875,66.396484 14.729962,38.921775 38.467431,32.956576 48.962891,34.789062 48.975219,34.991546 49,35.200091 49,34.794922 c 0,-7.066949 5.899219,-12.595703 13.199219,-12.595703 7.3,0 13.016966,5.903496 13.201172,13.201172 0.131833,5.222834 -3.797907,9.680727 -7.31836,11.570312 0,0 1.537914,1.225191 3.369141,7.748047 1.234097,4.39587 0.75,11.480469 0.75,11.480469 C 74.548358,63.802211 81.899609,50.3 80.099609,38.5 77.399609,20.7 62.1,7.5 44,7.5 Z m -1.712891,29.537109 -0.04687,8.308594 4.949219,0.02734 0.04687,-8.308594 z M 60.016601,47.682617 59.5,48.181641 56.560547,51.117188 60.056641,54.619141 65.9375,48.75 64.844727,47.638672 c -1.946707,0.438377 -3.181085,0.492666 -4.828126,0.04395 z m -34.45996,-2.405273 -3.453125,3.544922 5.953125,5.796875 3.453125,-3.546875 z m -8.410157,18.328125 v 4.949219 h 8.308594 v -4.949219 z m 44,0 v 4.949219 h 8.308594 v -4.949219 z"></path>
        <path d="M44,0C19.7,0,0,19.7,0,44s19.7,44,44,44s44-19.7,44-44S68.3,0,44,0z M44,86C20.8,86,2,67.2,2,44S20.8,2,44,2 s42,18.8,42,42S67.2,86,44,86z"></path>
        <path d="M62.2,24.2c-6.2,0-11.2,5-11.2,11.2s5,11.2,11.2,11.2c6.2,0,11.2-5,11.2-11.2S68.4,24.2,62.2,24.2z M57.5,37.8 V39h-3.4v-1c0-0.2,0.1-0.4,0.3-0.5c0.6-0.5,1.3-1,2.1-1.3c0,0,0,0,0-0.1v-1.4c-0.2-0.1-0.3-0.3-0.3-0.5v-1.5c0-0.7,0.6-1.3,1.3-1.3 h0.3c0.7,0,1.3,0.6,1.3,1.3v1.5c0,0.2-0.1,0.4-0.3,0.5V36l0,0.1c-0.3,0.2-0.6,0.4-0.8,0.6C57.7,37,57.5,37.4,57.5,37.8z M66.4,39 h-3.6l-0.4-1.7c0.7-1,0-1.1-0.2-1.1s-0.9,0.1-0.2,1.1L61.7,39h-3.6v-1.2c0-0.2,0.1-0.5,0.3-0.6c0.8-0.6,1.6-1.1,2.4-1.6 c0,0,0,0,0-0.1v-1.7c-0.2-0.1-0.3-0.4-0.3-0.6v-1.7c0-0.8,0.7-1.5,1.5-1.5h0.4c0,0,0,0,0,0c0.9,0,1.6,0.6,1.6,1.5v1.7 c0,0.3-0.1,0.5-0.4,0.6v1.7c0,0,0,0,0,0.1c0.9,0.4,1.7,1,2.5,1.6c0.2,0.2,0.3,0.4,0.3,0.6V39z M67,39v-1.2c0-0.4-0.2-0.8-0.5-1.1 c-0.3-0.2-0.6-0.4-0.8-0.6v-1.4c-0.2-0.1-0.3-0.3-0.3-0.5v-1.5c0-0.7,0.6-1.3,1.3-1.3H67c0.7,0,1.3,0.6,1.3,1.3v1.5 c0,0.2-0.1,0.4-0.3,0.5V36c0,0,0,0,0,0.1c0,0,0,0,0.1,0c0.7,0.4,1.4,0.8,2.1,1.3c0.2,0.1,0.2,0.3,0.2,0.6v1H67z"></path>
        <g transform="matrix(-0.73686,0.67604538,0.67604538,0.73686,29.588949,-13.461377)">
            <path d="m 33.781923,44.350288 5.47538,-1.174826 c 0.100995,-0.02167 0.118063,0.07358 0.139732,0.17457 l 5.096818,23.754162 13.881455,2.068033 -0.99874,5.475731 c 0,0 -17.467448,-2.540179 -17.763813,-2.645903 L 33.705127,44.469042 c -0.02167,-0.100995 -0.0242,-0.09708 0.0768,-0.118754 z" style="stroke-width:2.31947732"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 48 48" id="shrs-icon-demarche-interviews">
        <path d="M24,1.1A22.9,22.9,0,1,1,1.1,24h0A22.9,22.9,0,0,1,24,1.1M24,0A24,24,0,1,0,48,24h0A23.94,23.94,0,0,0,24,0Z"></path>
        <path d="M24,4.1A19.9,19.9,0,1,0,43.9,24h0A19.89,19.89,0,0,0,24,4.1ZM19.1,6.9H32.3v8.4H29.8v3l-2.4-2.4V10.6H19.1Zm0,4.2h7.8V18H22.3l-3.4,3.4V18H16.4V11.1Zm-6,6.3c2.6,0,3.7,1.8,3.4,4.2-.2,2-1.1,4.3-3.4,4.3s-3.2-2.4-3.4-4.3C9.4,19.2,10.5,17.4,13.1,17.4Zm21.3,0c2.6,0,3.7,1.8,3.4,4.2-.2,1.9-1.1,4.3-3.4,4.3s-3.2-2.4-3.4-4.3C30.7,19.3,31.8,17.4,34.4,17.4Zm-24,8.1a3.11,3.11,0,0,0,.9.7,3.74,3.74,0,0,0,1.8.5h.1a3.74,3.74,0,0,0,1.8-.5,5.5,5.5,0,0,0,.9-.7c.2.1.5.2.7.3a10.41,10.41,0,0,1,1.6.6c.8.6,1,1.9,1.2,2.8a17.85,17.85,0,0,1,.2,2.3c0,.6-.3.7-.8.8a10.55,10.55,0,0,1-1.9.5,23.2,23.2,0,0,1-3.8.5,16.52,16.52,0,0,1-3.8-.5,13.32,13.32,0,0,1-1.9-.5c-.5-.2-.8-.3-.8-.8a17.85,17.85,0,0,1,.2-2.3c.2-.9.4-2.2,1.2-2.8a4.42,4.42,0,0,1,1.6-.6A1.33,1.33,0,0,0,10.4,25.5Zm21.3.1a3.11,3.11,0,0,0,.9.7,3.74,3.74,0,0,0,1.8.5h.1a3.74,3.74,0,0,0,1.8-.5,5.5,5.5,0,0,0,.9-.7,4.88,4.88,0,0,0,.7.3,10.41,10.41,0,0,1,1.6.6c.8.6,1,1.9,1.2,2.8a17.85,17.85,0,0,1,.2,2.3c0,.6-.3.7-.8.8a10.55,10.55,0,0,1-1.9.5,23.2,23.2,0,0,1-3.8.5,15.68,15.68,0,0,1-3.7-.5,13.32,13.32,0,0,1-1.9-.5c-.5-.2-.8-.3-.8-.8a17.85,17.85,0,0,1,.2-2.3c.2-.9.4-2.2,1.2-2.8a3.82,3.82,0,0,1,1.6-.6A4.87,4.87,0,0,1,31.7,25.6ZM13.1,27c-.2,0-1.4.1-.3,1.6L12.3,31H14l-.5-2.4C14.5,27,13.3,27,13.1,27Z"></path>
    </symbol>
    <symbol viewBox="0 0 48 48" id="shrs-icon-demarche-interviewscheduler">
        <path d="M24,1.1A22.9,22.9,0,1,1,1.1,24h0A22.9,22.9,0,0,1,24,1.1M24,0A24,24,0,1,0,48,24h0A23.94,23.94,0,0,0,24,0Z"></path>
        <path d="M24,4.1A19.9,19.9,0,1,0,43.9,24h0A19.89,19.89,0,0,0,24,4.1ZM19.1,6.9H32.3v8.4H29.8v3l-2.4-2.4V10.6H19.1Zm0,4.2h7.8V18H22.3l-3.4,3.4V18H16.4V11.1Zm-6,6.3c2.6,0,3.7,1.8,3.4,4.2-.2,2-1.1,4.3-3.4,4.3s-3.2-2.4-3.4-4.3C9.4,19.2,10.5,17.4,13.1,17.4Zm21.3,0c2.6,0,3.7,1.8,3.4,4.2-.2,1.9-1.1,4.3-3.4,4.3s-3.2-2.4-3.4-4.3C30.7,19.3,31.8,17.4,34.4,17.4Zm-24,8.1a3.11,3.11,0,0,0,.9.7,3.74,3.74,0,0,0,1.8.5h.1a3.74,3.74,0,0,0,1.8-.5,5.5,5.5,0,0,0,.9-.7c.2.1.5.2.7.3a10.41,10.41,0,0,1,1.6.6c.8.6,1,1.9,1.2,2.8a17.85,17.85,0,0,1,.2,2.3c0,.6-.3.7-.8.8a10.55,10.55,0,0,1-1.9.5,23.2,23.2,0,0,1-3.8.5,16.52,16.52,0,0,1-3.8-.5,13.32,13.32,0,0,1-1.9-.5c-.5-.2-.8-.3-.8-.8a17.85,17.85,0,0,1,.2-2.3c.2-.9.4-2.2,1.2-2.8a4.42,4.42,0,0,1,1.6-.6A1.33,1.33,0,0,0,10.4,25.5Zm21.3.1a3.11,3.11,0,0,0,.9.7,3.74,3.74,0,0,0,1.8.5h.1a3.74,3.74,0,0,0,1.8-.5,5.5,5.5,0,0,0,.9-.7,4.88,4.88,0,0,0,.7.3,10.41,10.41,0,0,1,1.6.6c.8.6,1,1.9,1.2,2.8a17.85,17.85,0,0,1,.2,2.3c0,.6-.3.7-.8.8a10.55,10.55,0,0,1-1.9.5,23.2,23.2,0,0,1-3.8.5,15.68,15.68,0,0,1-3.7-.5,13.32,13.32,0,0,1-1.9-.5c-.5-.2-.8-.3-.8-.8a17.85,17.85,0,0,1,.2-2.3c.2-.9.4-2.2,1.2-2.8a3.82,3.82,0,0,1,1.6-.6A4.87,4.87,0,0,1,31.7,25.6ZM13.1,27c-.2,0-1.4.1-.3,1.6L12.3,31H14l-.5-2.4C14.5,27,13.3,27,13.1,27Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-leave">
        <g>
            <path d="M44.34,0a44,44,0,1,0,44,44A44,44,0,0,0,44.34,0Zm0,86a42,42,0,1,1,42-42A42,42,0,0,1,44.34,86Z"></path>
            <path d="M44.34,7A37,37,0,0,0,23.44,74.53C31,51.43,41.18,43.79,41.37,43.6A8.27,8.27,0,0,0,37,41.5a7.31,7.31,0,0,0-5.6,1.4.59.59,0,0,1-.9-.6,6.06,6.06,0,0,1,1.1-2.3,6.47,6.47,0,0,1,6.1-2.7,6.3,6.3,0,0,1,3.8,2.1,14.28,14.28,0,0,0-3.3-3.8,11,11,0,0,0-9.5-2.5.57.57,0,0,1-.6-.9,9.74,9.74,0,0,1,4.3-3.2,10.28,10.28,0,0,1,10,1.4,10.41,10.41,0,0,1,2.2,2.5c0-.1-.1-.2-.1-.3a11.41,11.41,0,0,0-6.4-7.1.77.77,0,0,1,0-1.4,7.85,7.85,0,0,1,4.4-.4,10.25,10.25,0,0,1,8.4,6.8,10.92,10.92,0,0,1,.4,4,10.74,10.74,0,0,1,4-.5,10.37,10.37,0,0,1,8.4,6.7,10.15,10.15,0,0,1,.6,4.4.78.78,0,0,1-1.4.4,11.11,11.11,0,0,0-8.2-4.6h-.3a8.7,8.7,0,0,1,3,1.6A10.28,10.28,0,0,1,61,52a9.65,9.65,0,0,1-2.1,4.8.63.63,0,0,1-1.1-.4,11.49,11.49,0,0,0-4.6-8.6,13.75,13.75,0,0,0-4.5-2.3,6.7,6.7,0,0,1,2.9,3.3,6.9,6.9,0,0,1-1.3,6.6,4.89,4.89,0,0,1-2,1.6.55.55,0,0,1-.8-.7,8,8,0,0,0-2.7-9.5,44.94,44.94,0,0,0-10.3,26.1,44,44,0,0,1,11.2-1.4,38.66,38.66,0,0,1,4.8.3,20.43,20.43,0,0,0-1.1-5.7,2.38,2.38,0,0,0-1.7,1,3.41,3.41,0,0,0-.8,2.3c0,.2-.3.4-.4.2a1.46,1.46,0,0,1-.6-.9,2.88,2.88,0,0,1,.5-2.8A2.6,2.6,0,0,1,48,65a5.59,5.59,0,0,0-2.1.3,5.12,5.12,0,0,0-3.1,2.8c-.1.3-.4.2-.5,0a4,4,0,0,1-.1-2.2,4.61,4.61,0,0,1,2.9-3.3,3.18,3.18,0,0,1,1.4-.2h-.1a4.89,4.89,0,0,0-4,.6c-.2.1-.6-.1-.5-.4a3.27,3.27,0,0,1,.9-1.6,4.16,4.16,0,0,1,4.4-1.4,7.15,7.15,0,0,1,1.5.8,5.48,5.48,0,0,1,.8-1.5,4.21,4.21,0,0,1,4.4-1.5,3,3,0,0,1,1.7.8c.2.1.1.5-.2.5a5.16,5.16,0,0,0-3.6,1.9l-.1.1A3.29,3.29,0,0,1,53,60a4.41,4.41,0,0,1,4.2,1,3.61,3.61,0,0,1,1.2,1.9c0,.2-.2.4-.4.3a5,5,0,0,0-4.2-.4,4.89,4.89,0,0,0-1.9,1,2.67,2.67,0,0,1,1.8-.3,2.71,2.71,0,0,1,2,2,2.16,2.16,0,0,1,.1,1.1.21.21,0,0,1-.4.1,2.77,2.77,0,0,0-2-1.4,3,3,0,0,0-2.1.2,17.69,17.69,0,0,1,2.1,6.6A36.54,36.54,0,0,1,63.8,75.47,37,37,0,0,0,44.34,7Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 87.69 87.69" id="shrs-icon-demarche-leaveplanning">
        <g id="shrs-icon-demarche-leaveplanning-Calque_2" data-name="Calque 2">
            <g id="shrs-icon-demarche-leaveplanning-Calque_1-2" data-name="Calque 1">
                <path d="M43.85,0A43.85,43.85,0,1,0,87.69,43.85,43.85,43.85,0,0,0,43.85,0Zm0,85.7A41.85,41.85,0,1,1,85.7,43.85,41.85,41.85,0,0,1,43.85,85.7Z"></path>
                <path d="M43.86,69.7c-.57,0-3.82.21-.8,4.44l-1.33,6.06c.7,0,1.4.06,2.11.06s1.43,0,2.14-.06l-1.33-6.06C47.69,69.91,44.43,69.7,43.86,69.7Z"></path>
                <path d="M31.37,38.18H56.63V21.79H31.37ZM50.15,24.25a.24.24,0,0,1,.23-.23h3.34a.24.24,0,0,1,.23.23v2.8a.23.23,0,0,1-.23.22H50.39a.23.23,0,0,1-.23-.22Zm0,4.58a.24.24,0,0,1,.23-.23h3.34a.24.24,0,0,1,.23.23v2.79a.23.23,0,0,1-.23.23H50.39a.23.23,0,0,1-.23-.23Zm0,4.56a.23.23,0,0,1,.23-.22h3.34a.23.23,0,0,1,.23.22v2.8a.24.24,0,0,1-.23.23H50.39a.24.24,0,0,1-.23-.23Zm-5.43-9.14A.24.24,0,0,1,45,24h3.34a.24.24,0,0,1,.23.23v2.8a.23.23,0,0,1-.23.22H45a.23.23,0,0,1-.23-.22Zm0,4.58A.24.24,0,0,1,45,28.6h3.34a.24.24,0,0,1,.23.23v2.79a.23.23,0,0,1-.23.23H45a.23.23,0,0,1-.23-.23Zm0,4.56a.23.23,0,0,1,.23-.22h3.34a.23.23,0,0,1,.23.22v2.8a.24.24,0,0,1-.23.23H45a.24.24,0,0,1-.23-.23Zm-5.43-4.56a.24.24,0,0,1,.25-.23h3.32a.24.24,0,0,1,.23.23v2.79a.23.23,0,0,1-.23.23H39.53a.24.24,0,0,1-.25-.23Zm0,4.56a.23.23,0,0,1,.25-.22h3.32a.23.23,0,0,1,.23.22v2.8a.24.24,0,0,1-.23.23H39.53a.24.24,0,0,1-.25-.23Zm-5.43-4.56a.24.24,0,0,1,.25-.23h3.32a.23.23,0,0,1,.23.23v2.79a.22.22,0,0,1-.23.23H34.1a.24.24,0,0,1-.25-.23Zm0,4.56a.23.23,0,0,1,.25-.22h3.32a.22.22,0,0,1,.23.22v2.8a.23.23,0,0,1-.23.23H34.1a.24.24,0,0,1-.25-.23Z"></path>
                <path d="M43.85,7.44A36.41,36.41,0,0,0,18.17,69.66c.65-.35,1.13-.59,1.36-.7a.29.29,0,0,0,.15-.25V62.8a2.77,2.77,0,0,1-1.23-2.3V54.37A5.52,5.52,0,0,1,24,48.85h1.31a5.52,5.52,0,0,1,5.51,5.51V60.5a2.76,2.76,0,0,1-1.23,2.3V68.7s0,.07,0,.1c-1.16.8-2.37,1.69-3.56,2.67a6,6,0,0,0-1.94,3q1.12.72,2.29,1.37a3.45,3.45,0,0,1,1.26-2.4,56.49,56.49,0,0,1,10.25-6.58.33.33,0,0,0,.18-.29V59.59a3.2,3.2,0,0,1-1.45-2.69V49.7a6.48,6.48,0,0,1,6.48-6.48h1.54a6.48,6.48,0,0,1,6.48,6.48v7.2a3.27,3.27,0,0,1-1.45,2.69v6.94a.33.33,0,0,0,.18.29,57.07,57.07,0,0,1,10.25,6.58,3.55,3.55,0,0,1,1.28,2.37q1.16-.64,2.27-1.36a6,6,0,0,0-1.94-3c-1.19-1-2.4-1.87-3.56-2.67,0,0,0-.06,0-.1V62.8a2.77,2.77,0,0,1-1.23-2.3V54.37a5.52,5.52,0,0,1,5.51-5.51h1.31a5.52,5.52,0,0,1,5.51,5.51V60.5A2.76,2.76,0,0,1,68,62.8V68.7a.26.26,0,0,0,.15.25c.23.11.71.35,1.36.7A36.41,36.41,0,0,0,43.85,7.44Zm6.24,6.94V13.5a1.12,1.12,0,0,1,1.14-1.1h.41a1.12,1.12,0,0,1,1.13,1.1v4.19a1.12,1.12,0,0,1-1.13,1.1h-.41a1.12,1.12,0,0,1-1.14-1.1Zm-14.63.29V13.53a1.13,1.13,0,0,1,1.14-1.1H37a1.12,1.12,0,0,1,1.14,1.1v4.19A1.12,1.12,0,0,1,37,18.82H36.6a1.13,1.13,0,0,1-1.14-1.1Zm23,23.51A1.82,1.82,0,0,1,56.63,40H31.37a1.82,1.82,0,0,1-1.85-1.79V16.46a1.82,1.82,0,0,1,1.85-1.79h3.16v3a2,2,0,0,0,2.07,2H37a2,2,0,0,0,2.07-2v-3H49.16v3a2,2,0,0,0,2.07,2h.41c1.13,0,2.05-.61,2.05-1.71V14.67h2.94a1.82,1.82,0,0,1,1.85,1.79Z"></path>
            </g>
       </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-maritalstatus">
        <g>
            <path d="M47,62.34a13.44,13.44,0,0,0-3-8.5,13.47,13.47,0,0,0,0,17A13.44,13.44,0,0,0,47,62.34Z"></path>
            <path d="M44,7.5A36.49,36.49,0,0,0,16.13,67.56,18.15,18.15,0,0,1,44,47.52a18.15,18.15,0,0,1,27.87,20A36.49,36.49,0,0,0,44,7.5Zm8.06,28.25a23.21,23.21,0,0,1-3.88,3.9c-2,1.69-3.44,2.55-4.17,2.55-1.61,0-7-4.77-8.07-6.45a5.88,5.88,0,0,1-1.07-3.65A5.15,5.15,0,0,1,40,27.17,5.61,5.61,0,0,1,44,29.4c.75-.82,2.1-2.24,4-2.24a5.15,5.15,0,0,1,5.08,4.93A5.81,5.81,0,0,1,52.06,35.75Z"></path>
            <path d="M54.48,48.84a13.42,13.42,0,0,0-7,1.94,18.13,18.13,0,0,1,0,23.12,13.5,13.5,0,1,0,7-25.06Z"></path>
            <path d="M36.32,62.34a18.08,18.08,0,0,1,4.16-11.56,13.5,13.5,0,1,0,0,23.12A18.08,18.08,0,0,1,36.32,62.34Z"></path>
            <path d="M37.85,80a36.2,36.2,0,0,0,12.3,0A18.09,18.09,0,0,1,44,77.16,18.09,18.09,0,0,1,37.85,80Z"></path>
            <path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-familycomposition">
       	<g id="g5107">
    		<path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z" id="path5093"></path>
    		<path d="M 44,7.5 C 28.053829,7.5011298 13.954203,17.853778 9.178178,33.067913 4.4021529,48.282049 10.055402,64.835525 23.14,73.95 V 44.8 l -7.88,6.44 -3.59,-4.36 11.6,-9.51 V 27.05 h 5.65 v 5.67 L 44.19,20.36 76.33,46.89 72.74,51.25 65.13,45 V 73.75 C 78.086607,64.556969 83.606505,48.037515 78.77915,32.902057 73.951796,17.766598 59.886643,7.4936664 44,7.5 Z" id="path5103" inkscape:connector-curvature="0" sodipodi:nodetypes="cscccccccccccccsc"></path>
  		</g>
  		<g transform="matrix(0.44735382,0,0,0.44735382,-44.691688,65.318975)" id="g5961">
    		<path inkscape:connector-curvature="0" d="m 189.61208,27.329905 c -4.01153,-0.08449 -17.28718,-1.729278 -20.67936,-2.595974 -3.50744,-0.690721 -6.95736,-1.646804 -10.32,-2.86 -2.68,-1 -4.27,-1.41 -4.18,-4.62 0.14351,-4.164773 0.59811,-8.3129949 1.36,-12.4099999 0.88,-5 2,-12.23 6.34,-15.4900001 2.46,-1.85 5.72,-2.46 8.58,-3.61 1.30925,-0.478731 2.58827,-1.036421 3.83,-1.67 8.12546,8.8049049 22.03454,8.9308982 30.16,0.125993 0.21719,1.925394 2.01677,6.6410886 3.05875,8.8125432 -2.21817,0.8281825 -8.42552,3.8211781 -8.42552,3.8211781 -3.8725,2.85424617 -4.55465,7.7431479 -5.44328,11.1101011 -0.33349,1.9488223 -1.62468,11.7034176 -0.46165,13.1835666 1.8879,2.402676 16.75221,5.111406 16.75221,5.111406 -6.80497,1.042356 -13.70206,1.20838 -20.57115,1.091186 z m 6.4e-4,-41.135974 c -13,0 -17.69,-13 -18.92,-23.72 -1.5,-13.2 4.71,-23.14 18.92,-23.14 14.21,0 20.42,9.94 18.92,23.14 -1.23,10.7 -5.92,23.76 -18.92,23.76 z" id="path5959" sodipodi:nodetypes="ccccccccccccsccccsccc"></path>
  		</g>
  		<g transform="matrix(0.29335033,0,0,0.29335033,43.184481,50.288066)" id="g5961-3">
    		<path inkscape:connector-curvature="0" d="m 813.66,506.64 a 70.61,70.61 0 0 1 -10.32,2.86 83.54,83.54 0 0 1 -41.36,0 70.61,70.61 0 0 1 -10.32,-2.86 c -2.68,-1 -4.27,-1.41 -4.18,-4.62 a 83.63,83.63 0 0 1 1.36,-12.41 c 0.88,-5 2,-12.23 6.34,-15.49 2.46,-1.85 5.72,-2.46 8.58,-3.61 a 34.48,34.48 0 0 0 3.83,-1.67 20.52,20.52 0 0 0 30.16,0 34.48,34.48 0 0 0 3.83,1.67 c 2.86,1.14 6.08,1.76 8.58,3.58 4.36,3.26 5.46,10.52 6.34,15.49 a 83.63,83.63 0 0 1 1.36,12.42 c 0.09,3.24 -1.5,3.68 -4.2,4.64 z m -31,-35.68 c -13,0 -17.69,-13 -18.92,-23.72 -1.5,-13.2 4.71,-23.14 18.92,-23.14 14.21,0 20.42,9.94 18.92,23.14 -1.23,10.7 -5.92,23.76 -18.92,23.76 z" transform="translate(-747.47,-424.1)" id="path5959-2"></path>
  		</g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-mesconges">
        <g>
            <path d="M44.34,0a44,44,0,1,0,44,44A44,44,0,0,0,44.34,0Zm0,86a42,42,0,1,1,42-42A42,42,0,0,1,44.34,86Z"></path>
            <path d="M44.34,7A37,37,0,0,0,23.44,74.53C31,51.43,41.18,43.79,41.37,43.6A8.27,8.27,0,0,0,37,41.5a7.31,7.31,0,0,0-5.6,1.4.59.59,0,0,1-.9-.6,6.06,6.06,0,0,1,1.1-2.3,6.47,6.47,0,0,1,6.1-2.7,6.3,6.3,0,0,1,3.8,2.1,14.28,14.28,0,0,0-3.3-3.8,11,11,0,0,0-9.5-2.5.57.57,0,0,1-.6-.9,9.74,9.74,0,0,1,4.3-3.2,10.28,10.28,0,0,1,10,1.4,10.41,10.41,0,0,1,2.2,2.5c0-.1-.1-.2-.1-.3a11.41,11.41,0,0,0-6.4-7.1.77.77,0,0,1,0-1.4,7.85,7.85,0,0,1,4.4-.4,10.25,10.25,0,0,1,8.4,6.8,10.92,10.92,0,0,1,.4,4,10.74,10.74,0,0,1,4-.5,10.37,10.37,0,0,1,8.4,6.7,10.15,10.15,0,0,1,.6,4.4.78.78,0,0,1-1.4.4,11.11,11.11,0,0,0-8.2-4.6h-.3a8.7,8.7,0,0,1,3,1.6A10.28,10.28,0,0,1,61,52a9.65,9.65,0,0,1-2.1,4.8.63.63,0,0,1-1.1-.4,11.49,11.49,0,0,0-4.6-8.6,13.75,13.75,0,0,0-4.5-2.3,6.7,6.7,0,0,1,2.9,3.3,6.9,6.9,0,0,1-1.3,6.6,4.89,4.89,0,0,1-2,1.6.55.55,0,0,1-.8-.7,8,8,0,0,0-2.7-9.5,44.94,44.94,0,0,0-10.3,26.1,44,44,0,0,1,11.2-1.4,38.66,38.66,0,0,1,4.8.3,20.43,20.43,0,0,0-1.1-5.7,2.38,2.38,0,0,0-1.7,1,3.41,3.41,0,0,0-.8,2.3c0,.2-.3.4-.4.2a1.46,1.46,0,0,1-.6-.9,2.88,2.88,0,0,1,.5-2.8A2.6,2.6,0,0,1,48,65a5.59,5.59,0,0,0-2.1.3,5.12,5.12,0,0,0-3.1,2.8c-.1.3-.4.2-.5,0a4,4,0,0,1-.1-2.2,4.61,4.61,0,0,1,2.9-3.3,3.18,3.18,0,0,1,1.4-.2h-.1a4.89,4.89,0,0,0-4,.6c-.2.1-.6-.1-.5-.4a3.27,3.27,0,0,1,.9-1.6,4.16,4.16,0,0,1,4.4-1.4,7.15,7.15,0,0,1,1.5.8,5.48,5.48,0,0,1,.8-1.5,4.21,4.21,0,0,1,4.4-1.5,3,3,0,0,1,1.7.8c.2.1.1.5-.2.5a5.16,5.16,0,0,0-3.6,1.9l-.1.1A3.29,3.29,0,0,1,53,60a4.41,4.41,0,0,1,4.2,1,3.61,3.61,0,0,1,1.2,1.9c0,.2-.2.4-.4.3a5,5,0,0,0-4.2-.4,4.89,4.89,0,0,0-1.9,1,2.67,2.67,0,0,1,1.8-.3,2.71,2.71,0,0,1,2,2,2.16,2.16,0,0,1,.1,1.1.21.21,0,0,1-.4.1,2.77,2.77,0,0,0-2-1.4,3,3,0,0,0-2.1.2,17.69,17.69,0,0,1,2.1,6.6A36.54,36.54,0,0,1,63.8,75.47,37,37,0,0,0,44.34,7Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-transportmode">
        <g>
            <path d="M34.2,56.8c-1.1,0-2,0.9-2,2c0,1.1,0.9,2,2,2h0c1.1,0,2-0.9,2-2C36.2,57.7,35.3,56.8,34.2,56.8z"></path>
            <path d="M27,46.9c-5.1,0-8.4,1-8.4,2.5l-0.4,3c0,1.4,1.2,2.6,2.6,2.6h12.6c1.4,0,2.5-1.2,2.5-2.6l-0.4-3 C35.4,47.9,32.1,46.9,27,46.9z"></path>
            <path d="M32.8,63.8v-1.6H21.2v1.6c0,0.6-0.5,1.1-1.1,1.1h13.7C33.3,64.9,32.8,64.4,32.8,63.8z"></path>
            <path d="M19.8,60.8L19.8,60.8c1.1,0,2-0.9,2-2c0-1.1-0.9-2-2-2s-2,0.9-2,2C17.8,59.9,18.7,60.8,19.8,60.8z"></path>
            <path d="M44.1,0c-24.3,0-44,19.7-44,44s19.7,44,44,44s44-19.7,44-44S68.4,0,44.1,0L44.1,0z M44.1,86 c-23.2,0-42-18.8-42-42s18.8-42,42-42s42,18.8,42,42S67.3,86,44.1,86L44.1,86z"></path>
            <polygon points="52.5,61.5 48.9,64.9 67.2,64.9 63.5,61.5 "></polygon>
            <path d="M68.7,39.9c0-1.7-1.3-3-3-3H50.3c-1.7,0-3,1.3-3,3v5.3c0,1.7,1.3,3,3,3h15.3c1.7,0,3-1.3,3-3L68.7,39.9z"></path>
            <path d="M49.7,58.1C49.7,58.1,49.7,58.1,49.7,58.1c1.3,0,2.3-1,2.3-2.3s-1-2.3-2.3-2.3c-1.3,0-2.3,1-2.3,2.3 C47.4,57,48.4,58.1,49.7,58.1z"></path>
            <circle cx="66.3" cy="55.8" r="2.3"></circle>
            <path d="M80.2,38.5c-2.7-17.8-18-31-36.1-31C24,7.5,7.6,23.8,7.6,44c0,7.7,2.4,14.9,6.6,20.9h3.2 c-0.6,0-1.1-0.5-1.1-1.1v-8.6l0.8-6.9c0-1.7,3.9-2.8,9.9-2.8c6,0,9.9,1.2,9.9,2.8l0.8,6.9v8.6c0,0.6-0.5,1.1-1.1,1.1h9.5l3.7-3.4 H48c-2.1,0-3.9-1.7-3.9-3.9V37.6c0-2.1,1.7-3.9,3.9-3.9h20.1c0,0,0,0,0,0c2.1,0,3.9,1.7,3.9,3.9v20.1c0,2.1-1.7,3.9-3.9,3.9h-1.8 l3.7,3.4H74C79.2,57.5,81.7,48.2,80.2,38.5z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-myDepositOrAdvanceRequest">
        <g>
            <path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z"></path>
            <path d="M64.47,40.38H22.13a8.12,8.12,0,0,1-7.56,8V61.91a8.12,8.12,0,0,1,7.92,8H64.83s0,0,0-.07A8.13,8.13,0,0,1,73,61.72l.48,0V48.22a7.64,7.64,0,0,1-.84.06A8.12,8.12,0,0,1,64.47,40.38ZM41,62.5a7.35,7.35,0,1,1,7.35-7.35A7.35,7.35,0,0,1,41,62.5Zm11.79-5a2.3,2.3,0,1,1,2.3-2.3A2.3,2.3,0,0,1,52.83,57.45Z"></path>
            <path d="M80.5,44A36.5,36.5,0,1,0,11.61,60.83V39.64a2.23,2.23,0,0,1,2.22-2.22H74.17a2.23,2.23,0,0,1,2.22,2.22V60.83A36.33,36.33,0,0,0,80.5,44Z"></path>
            <path d="M23.82,74.41H64.18c.73-.49,1.44-1,2.14-1.54H21.69C22.38,73.41,23.09,73.93,23.82,74.41Z"></path>
            <path d="M31.44,78.28H56.56a36.26,36.26,0,0,0,3.78-1.65H27.65A36.26,36.26,0,0,0,31.44,78.28Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-mymatrimonialstatus">
        <g>
            <path d="M47,62.34a13.44,13.44,0,0,0-3-8.5,13.47,13.47,0,0,0,0,17A13.44,13.44,0,0,0,47,62.34Z"></path>
            <path d="M44,7.5A36.49,36.49,0,0,0,16.13,67.56,18.15,18.15,0,0,1,44,47.52a18.15,18.15,0,0,1,27.87,20A36.49,36.49,0,0,0,44,7.5Zm8.06,28.25a23.21,23.21,0,0,1-3.88,3.9c-2,1.69-3.44,2.55-4.17,2.55-1.61,0-7-4.77-8.07-6.45a5.88,5.88,0,0,1-1.07-3.65A5.15,5.15,0,0,1,40,27.17,5.61,5.61,0,0,1,44,29.4c.75-.82,2.1-2.24,4-2.24a5.15,5.15,0,0,1,5.08,4.93A5.81,5.81,0,0,1,52.06,35.75Z"></path>
            <path d="M54.48,48.84a13.42,13.42,0,0,0-7,1.94,18.13,18.13,0,0,1,0,23.12,13.5,13.5,0,1,0,7-25.06Z"></path>
            <path d="M36.32,62.34a18.08,18.08,0,0,1,4.16-11.56,13.5,13.5,0,1,0,0,23.12A18.08,18.08,0,0,1,36.32,62.34Z"></path>
            <path d="M37.85,80a36.2,36.2,0,0,0,12.3,0A18.09,18.09,0,0,1,44,77.16,18.09,18.09,0,0,1,37.85,80Z"></path>
            <path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-relocation">
        <g>
            <path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z"></path>
            <rect x="44.71" y="62.81" width="5.78" height="5.4"></rect>
            <rect x="44.71" y="68.92" width="5.78" height="5.38"></rect>
            <path d="M55.66,49.26H51.72v4.57l-7.53-6.26L26.25,62.33V75.89q1.14.64,2.34,1.19V64.25L44.19,51.4l15.6,12.91v12.6c.8-.38,1.57-.8,2.34-1.24V62.38l-6.47-5.32Z"></path>
            <rect x="38.18" y="62.81" width="5.78" height="5.4"></rect>
            <path d="M44,7.5A36.5,36.5,0,0,0,23.14,73.95V52.8l-7.88,6.44-3.59-4.36,11.6-9.51V35.05h5.65v5.67L44.19,28.36,76.33,54.89l-3.59,4.36L65.13,53V73.75A36.49,36.49,0,0,0,44,7.5Z"></path>
            <rect x="38.18" y="68.92" width="5.78" height="5.38"></rect>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-employeetermination">
        <g>
           <path style="stroke-width:0.62393934" d="m 44.437534,6.9091294 c -14.76285,0 -28.07152,8.8923936 -33.7207,22.5312496 -4.9963904,12.062817 -3.1264004,25.733808 4.59375,35.955078 0.095,-0.670655 0.20305,-1.339402 0.32422,-2.005859 0.55035,-3.112386 1.25058,-7.611351 3.96484,-9.640626 1.53849,-1.151581 3.57658,-1.532197 5.36523,-2.248046 0.8192,-0.297801 1.61951,-0.644792 2.39649,-1.039063 2.3941,2.66539 5.83215,4.166015 9.42383,4.113281 2.4397,0.03669 6.05548,-1.336212 8.10813,-2.649157 0.4168,4.714585 0.20809,5.450998 1.58763,8.696977 3.68467,5.713851 7.71418,8.31957 12.18155,8.999352 0.20495,1.50366 0.0641,0.305745 0.13636,1.491891 0.0563,2.004376 -0.93641,2.279375 -2.625,2.876953 -2.10333,0.754895 -4.26123,1.349705 -6.45508,1.779297 -4.24457,0.976543 -8.57825,1.51907 -12.93359,1.619141 -2.36715,-0.06487 -4.72886,-0.260483 -7.07422,-0.585938 8.9508,3.945649 19.31025,4.213441 28.69531,0.326172 13.638859,-5.649181 22.531249,-18.957847 22.531249,-33.720703 0,-20.158393 -16.341599,-36.4999996 -36.499999,-36.4999996 z m -7.65234,15.7031246 c 8.88701,0 12.77014,6.187602 11.83203,14.404297 -0.76925,6.660503 -3.70176,14.763672 -11.83203,14.763671 -8.13027,0 -11.0628,-8.090719 -11.83204,-14.763671 -0.93811,-8.216695 2.94503,-14.404297 11.83204,-14.404297 z m 25.5957,15.193359 c 8.15854,-1.66e-4 14.772224,6.697468 14.771483,14.958985 -0.0012,13.326349 -15.911593,19.996713 -25.215833,10.574371 -9.30424,-9.422343 -2.71589,-25.533088 10.44435,-25.533356 z m -7.45703,5.470703 c -0.41506,-0.0069 -0.81524,0.156504 -1.10938,0.453125 l -0.25586,0.279297 c -0.59561,0.606737 -0.59561,1.586623 0,2.19336 l 6.41992,6.501953 -6.40039,6.501953 c -0.61646,0.590165 -0.61646,1.583663 0,2.173828 l 0.2168,0.357422 c 0.59917,0.603122 1.56685,0.603122 2.16602,0 l 6.41992,-6.480469 6.44141,6.441406 c 0.58971,0.625922 1.5763,0.625922 2.16601,0 l 0.25586,-0.259765 c 0.593612,-0.599947 0.593612,-1.573881 0,-2.173828 l -6.44141,-6.521485 6.42188,-6.501953 c 0.597642,-0.6135 0.597642,-1.599391 0,-2.212891 l -0.27539,-0.259765 c -0.59266,-0.60226 -1.55578,-0.60226 -2.14844,0 l -6.41992,6.501953 -6.40039,-6.541016 c -0.28119,-0.283577 -0.65985,-0.445959 -1.05664,-0.453125 z" id="path3627" inkscape:connector-curvature="0" sodipodi:nodetypes="ssccccccccccccccsssscscssssscccccccccccccccccccccc"></path>
           <path inkscape:connector-curvature="0" d="M 44.228624,1.8257137 A 42,42 0 1 1 2.2286236,43.825713 42,42 0 0 1 44.228624,1.8257137 m 0,-2.00000003 A 44,44 0 1 0 88.228626,43.825713 44,44 0 0 0 44.228624,-0.17428633 Z" id="path11713-9"></path>
        </g>
      
    </symbol>
    
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-mymaternity">
        <g>
       <path style="stroke-width:0.62393934" d="m 44.4375,6.9082031 c -14.76285,0 -28.071523,8.8923939 -33.720703,22.5312499 -4.9963906,12.062817 -3.1264006,25.733808 4.59375,35.955078 0.095,-0.670655 0.203049,-1.339402 0.324219,-2.005859 0.55035,-3.112386 1.250583,-7.61135 3.964843,-9.640625 1.53849,-1.151581 3.576585,-1.532198 5.365235,-2.248047 0.8192,-0.297801 1.619504,-0.644792 2.396484,-1.039062 2.3941,2.66539 5.832148,4.166015 9.423828,4.113281 2.4397,0.03669 5.957116,-1.138227 8.009766,-2.451172 0.252008,3.497066 0.649838,6.683604 2.478516,9.992187 1.820261,3.010462 5.616216,6.578991 11.388671,7.771485 0.20495,1.50366 0.06446,0.04042 0.136719,1.226562 0.0563,2.004376 -0.93641,2.279375 -2.625,2.876953 -2.10333,0.754895 -4.261228,1.349705 -6.455078,1.779297 -4.24457,0.976543 -8.578254,1.51907 -12.933594,1.619141 -2.36715,-0.06487 -4.728859,-0.260483 -7.074218,-0.585938 8.9508,3.945649 19.310252,4.213441 28.695312,0.326172 C 72.045109,71.479725 80.9375,58.171059 80.9375,43.408203 80.9375,23.24981 64.5959,6.9082031 44.4375,6.9082031 Z M 36.785156,22.611328 c 4.443505,0 6.703574,1.640327 8.667969,4.121094 4.093107,5.827112 2.177627,16.129167 0.173828,20.167969 -1.370625,3.019673 -4.776662,4.878906 -8.841797,4.878906 -4.065135,0 -6.159022,-2.359879 -8.1875,-5.089844 -2.24623,-3.902431 -3.998466,-14.522581 -0.480468,-19.957031 1.777957,-2.387546 4.224463,-4.121094 8.667968,-4.121094 z m 25.595703,15.19336 c 8.15854,-1.67e-4 14.772226,6.697467 14.771485,14.958984 C 77.151144,66.090021 61.24174,72.762186 51.9375,63.339844 42.63326,53.917501 49.220619,37.804955 62.380859,37.804688 Z M 62.381,38.354 c -3.089273,-2.34e-4 -4.576023,1.903362 -4.576,4.374781 -2.3e-5,2.471419 1.486586,4.375453 4.575859,4.375219 3.089273,2.34e-4 4.611163,-1.9038 4.611141,-4.375219 2.2e-5,-2.471419 -1.521727,-4.375015 -4.611,-4.374781 z m 0,16.383 c -2.02479,0.04655 -5.120532,-0.912137 -5.120532,-0.912137 -0.0076,0.940045 0.265573,2.971924 0.595913,3.808894 0.626555,1.244716 1.641608,2.963496 4.524619,2.96338 2.883011,1.16e-4 3.860116,-1.685599 4.525,-2.963274 0.361985,-0.83697 0.650163,-2.620712 0.596,-3.809 0,0 -2.723329,1.028603 -5.121,0.912137 z" id="path3627" inkscape:connector-curvature="0" sodipodi:nodetypes="ssccccccccccccccssssccsccsssssccccccscccsc"></path>
  <path inkscape:connector-curvature="0" d="M 44.228624,1.8257137 A 42,42 0 1 1 2.2286236,43.825713 42,42 0 0 1 44.228624,1.8257137 m 0,-2.00000003 A 44,44 0 1 0 88.228626,43.825713 44,44 0 0 0 44.228624,-0.17428633 Z" id="path11713-9"></path>
  <path inkscape:connector-curvature="0" d="m 54.998571,45.056028 c -0.893117,-0.797944 -2.297918,-0.958694 -3.352132,0.394 -0.964286,1.386339 -0.118742,2.794293 0.5339,3.412 6.051685,5.727748 14.631015,5.770804 20.29156,0 0.803621,-0.819276 1.454357,-2.219914 0.437419,-3.412 -0.954586,-1.118997 -2.537409,-1.139623 -3.351627,-0.394 -5.451136,4.991899 -8.895264,5.060291 -14.55912,0 z" id="path3903" style="stroke-width:0.7652238" sodipodi:nodetypes="scsssss"></path>
  <path inkscape:connector-curvature="0" d="m 66.220441,64.373739 c 0.41222,1.149905 1.586724,2.016104 3.206966,1.386391 1.556219,-0.705252 1.578258,-2.392267 1.218933,-3.234449 -1.372424,-3.216672 -1.48502,-3.650903 -2.48734,-4.537942 -0.466511,1.416467 -1.586584,2.778233 -2.912,3.720096 0.389542,0.741335 0.603228,1.63318 0.973441,2.665904 z" id="path3903-2" style="stroke-width:0.7787317" sodipodi:nodetypes="scsccs"></path>
  <path inkscape:connector-curvature="0" d="m 58.460928,64.258739 c -0.41222,1.149905 -1.586724,2.016104 -3.206966,1.386391 -1.556219,-0.705252 -1.578258,-2.392267 -1.218933,-3.234449 1.372424,-3.216672 1.538924,-3.527869 2.541244,-4.414908 0.419902,1.556298 1.398505,2.72677 2.956972,3.761854 -0.389542,0.741335 -0.702104,1.468388 -1.072317,2.501112 z" id="path3903-2-5" style="stroke-width:0.7787317" sodipodi:nodetypes="scsccs"></path>
        </g>
    </symbol>
    
    
    
    
    
    
    
	 <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-addresscorrection">        
        <path d="M44,7.07a36.93,36.93,0,0,0-2.45,73.77C37.23,74.56,28.41,61,26.05,52.31a17.76,17.76,0,0,1-.72-5A18.42,18.42,0,0,1,44,29.13,18.42,18.42,0,0,1,62.67,47.29c0,.35,0,.7,0,1,.19,7.91-11.21,25.18-16.34,32.52A36.93,36.93,0,0,0,44,7.07Z"></path><ellipse cx="44.13" cy="46.82" rx="11.66" ry="11.28"></ellipse><path d="M44,.15A43.85,43.85,0,1,0,87.85,44,43.85,43.85,0,0,0,44,.15Zm0,85.7A41.85,41.85,0,1,1,85.85,44,41.84,41.84,0,0,1,44,85.85Z"></path>
    </symbol>
	 <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-changephoto">        
        <path d="M44,.15A43.85,43.85,0,1,0,87.85,44,43.85,43.85,0,0,0,44,.15Zm0,85.7A41.85,41.85,0,1,1,85.85,44,41.84,41.84,0,0,1,44,85.85Z"></path><path d="M14.68,47.79H32.55a13.42,13.42,0,1,0,22.9,0H73.32V65.7a36.48,36.48,0,1,0-58.64,0ZM44,65.48a10.72,10.72,0,0,1-5.85-19.71A9.81,9.81,0,0,1,39.57,45a10.6,10.6,0,0,1,8.86,0,9.81,9.81,0,0,1,1.42.78,10.8,10.8,0,0,1,2.3,2A10.72,10.72,0,0,1,44,65.48ZM14.68,41.22a5.63,5.63,0,0,1,5.63-5.63H33.06l.83-5.31a2.34,2.34,0,0,1,2.38-1.73H51.73a2.34,2.34,0,0,1,2.38,1.73l.83,5.31H67.69a5.63,5.63,0,0,1,5.63,5.63v4.55H54c-.25-.27-.5-.53-.77-.78a13.34,13.34,0,0,0-18.36,0c-.27.25-.52.51-.77.78H14.68Z"></path>
    </symbol>
	<symbol viewBox="0 0 88 88" id="shrs-icon-demarche-mesCoordonneesBancaires">        
        <path d="M44,.15A43.85,43.85,0,1,0,87.85,44,43.85,43.85,0,0,0,44,.15Zm0,85.7A41.85,41.85,0,1,1,85.85,44,41.84,41.84,0,0,1,44,85.85Z"></path><path d="M44,7.07a36.89,36.89,0,0,0-36.42,43,58.94,58.94,0,0,0,8.64-2.3c1.86-.69,7.59-1.85,9.68-1.34,4,1,8.18,4.52,11.77,6.94,4.27,2.87.3,6.41-2,5.2C34.44,58,30.8,55.84,29,55c-1.42-.7-2.28-.89-2.81.73,1,.65,8.78,5.56,10.61,5.43,2.11-.15,8.56-1.58,15.41-3.63,5.53-1.65,7.5,3.53,2.72,5.73-5.73,2.63-16,6.07-19.42,6.34s-17.95-3.2-17.95-3.2l-3-.14A36.93,36.93,0,1,0,44,7.07ZM36,46.94a5.42,5.42,0,1,1,5.42-5.42A5.42,5.42,0,0,1,36,46.94Zm11.88,7.5A5.41,5.41,0,1,1,53.24,49,5.42,5.42,0,0,1,47.83,54.44ZM48.77,35a5.41,5.41,0,1,1,5.41-5.41A5.41,5.41,0,0,1,48.77,35Z"></path>
    </symbol>
	<symbol viewBox="0 0 88 88" id="shrs-icon-flag">        
        <path d="M19.84,6.5H12.22V77.83a3.67,3.67,0,0,0,3.67,3.67h0a4,4,0,0,0,3.95-4Z"></path><polygon points="75.78 6.5 22.05 6.5 22.05 49.47 75.78 49.47 62.35 27.98 75.78 6.5"></polygon>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-teamplanning">
        <path d="M80.1,38.5c-2.7-17.8-18-31-36.1-31C23.8,7.5,7.5,23.8,7.5,44c0,10.8,4.7,20.7,12.4,27.5V40.9c0,0,0,0,0,0 c0-1.7,1.5-3.1,3.2-3.1h5.5V43c0,2,1.6,3.5,3.6,3.5h0.7c0,0,0,0,0,0c1.9,0,3.5-1.6,3.5-3.5v-5.2h12.8C49.1,37,49,36.2,49,35.4 c0-7.3,5.9-13.2,13.2-13.2c7.3,0,13.2,5.9,13.2,13.2c0,4.3-2.1,8.2-5.4,10.6v23.5C77.8,61.6,81.9,50.3,80.1,38.5z M34.9,43 c0,1.1-0.9,1.9-2,1.9h-0.7c-1.1,0-2-0.8-2-1.9v-7.2c0-1.1,0.9-1.9,2-1.9h0.7c1.1,0,2,0.8,2,1.9V43z"></path>
        <path d="M44,0C19.7,0,0,19.7,0,44s19.7,44,44,44s44-19.7,44-44S68.3,0,44,0z M44,86C20.8,86,2,67.2,2,44S20.8,2,44,2 s42,18.8,42,42S67.2,86,44,86z"></path>
        <path d="M23.1,50.1V74c2.7,1.9,5.7,3.4,8.9,4.5h24c4-1.4,7.7-3.4,10.9-6V50.1H23.1z M55.6,54.3c0-0.2,0.2-0.4,0.4-0.4 h5.8c0.2,0,0.4,0.2,0.4,0.4v4.9c0,0.2-0.2,0.4-0.4,0.4H56c-0.2,0-0.4-0.2-0.4-0.4V54.3z M34,75c0,0.2-0.2,0.4-0.4,0.4h-5.8 c0,0,0,0,0,0c-0.2,0-0.4-0.2-0.4-0.4v-4.8c0,0,0,0,0,0c0-0.2,0.2-0.4,0.4-0.4h5.7c0,0,0,0,0,0c0.2,0,0.4,0.2,0.4,0.4V75z M34,67.1 C34,67.1,34,67.1,34,67.1c0,0.3-0.2,0.4-0.4,0.4h-5.8c0,0,0,0,0,0c-0.2,0-0.4-0.2-0.4-0.4v-4.8c0,0,0,0,0,0c0-0.2,0.2-0.4,0.4-0.4 h5.8c0,0,0,0,0,0c0.2,0,0.4,0.2,0.4,0.4V67.1z M43.4,75c0,0.2-0.2,0.4-0.4,0.4h-5.7c0,0,0,0,0,0c-0.2,0-0.4-0.2-0.4-0.4v-4.8 c0,0,0,0,0,0c0-0.2,0.2-0.4,0.4-0.4H43c0.2,0,0.4,0.2,0.4,0.4V75z M43.4,67.1C43.4,67.1,43.4,67.1,43.4,67.1c0,0.2-0.2,0.4-0.4,0.4 h-5.7c0,0,0,0,0,0c-0.2,0-0.4-0.2-0.4-0.4v-4.8c0,0,0,0,0,0c0-0.2,0.2-0.4,0.4-0.4H43c0.2,0,0.4,0.2,0.4,0.4V67.1z M52.8,75 c0,0.2-0.2,0.4-0.4,0.4h-5.8c-0.2,0-0.4-0.2-0.4-0.4v-4.8c0-0.2,0.2-0.4,0.4-0.4h5.8c0.2,0,0.4,0.2,0.4,0.4V75z M52.8,67.1 C52.8,67.1,52.8,67.1,52.8,67.1c0,0.2-0.2,0.4-0.4,0.4h-5.8c0,0,0,0,0,0c-0.2,0-0.4-0.2-0.4-0.4v-4.8c0-0.2,0.2-0.4,0.4-0.4h5.8 c0.2,0,0.4,0.2,0.4,0.4V67.1z M52.8,59.2c0,0.2-0.2,0.4-0.4,0.4h-5.8c-0.2,0-0.4-0.2-0.4-0.4v-4.9c0-0.2,0.2-0.4,0.4-0.4h5.8 c0.2,0,0.4,0.2,0.4,0.4V59.2z M62.2,75c0,0.2-0.2,0.4-0.4,0.4h-5.8c-0.2,0-0.4-0.2-0.4-0.4l0-4.8c0-0.2,0.2-0.4,0.4-0.4h5.8 c0.2,0,0.4,0.2,0.4,0.4V75z M62.2,67.1C62.3,67.1,62.3,67.1,62.2,67.1c0,0.2-0.2,0.4-0.4,0.4h-5.8c0,0,0,0,0,0 c-0.2,0-0.4-0.2-0.4-0.4l0-4.8c0-0.2,0.2-0.4,0.4-0.4h5.8c0.2,0,0.4,0.2,0.4,0.4V67.1z"></path>
        <path d="M62.2,24.2c-6.2,0-11.2,5-11.2,11.2s5,11.2,11.2,11.2c6.2,0,11.2-5,11.2-11.2S68.4,24.2,62.2,24.2z M57.5,37.8 V39h-3.4v-1c0-0.2,0.1-0.4,0.3-0.5c0.6-0.5,1.3-1,2.1-1.3c0,0,0,0,0-0.1v-1.4c-0.2-0.1-0.3-0.3-0.3-0.5v-1.5c0-0.7,0.6-1.3,1.3-1.3 h0.3c0.7,0,1.3,0.6,1.3,1.3v1.5c0,0.2-0.1,0.4-0.3,0.5V36l0,0.1c-0.3,0.2-0.6,0.4-0.8,0.6C57.7,37,57.5,37.4,57.5,37.8z M66.4,39 h-3.6l-0.4-1.7c0.7-1,0-1.1-0.2-1.1s-0.9,0.1-0.2,1.1L61.7,39h-3.6v-1.2c0-0.2,0.1-0.5,0.3-0.6c0.8-0.6,1.6-1.1,2.4-1.6 c0,0,0,0,0-0.1v-1.7c-0.2-0.1-0.3-0.4-0.3-0.6v-1.7c0-0.8,0.7-1.5,1.5-1.5h0.4c0,0,0,0,0,0c0.9,0,1.6,0.6,1.6,1.5v1.7 c0,0.3-0.1,0.5-0.4,0.6v1.7c0,0,0,0,0,0.1c0.9,0.4,1.7,1,2.5,1.6c0.2,0.2,0.3,0.4,0.3,0.6V39z M67,39v-1.2c0-0.4-0.2-0.8-0.5-1.1 c-0.3-0.2-0.6-0.4-0.8-0.6v-1.4c-0.2-0.1-0.3-0.3-0.3-0.5v-1.5c0-0.7,0.6-1.3,1.3-1.3H67c0.7,0,1.3,0.6,1.3,1.3v1.5 c0,0.2-0.1,0.4-0.3,0.5V36c0,0,0,0,0,0.1c0,0,0,0,0.1,0c0.7,0.4,1.4,0.8,2.1,1.3c0.2,0.1,0.2,0.3,0.2,0.6v1H67z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-diagram">
        <path d="M83.55,88H7.35a5.18,5.18,0,0,1-5.15-5.15V2.34c0-3.12,6.2-3.12,6.2,0V81.8H83.55C86.54,81.8,86.54,88,83.55,88ZM80.16,78H60.59A1.57,1.57,0,0,1,59,76.39V21.3a1.56,1.56,0,0,1,1.54-1.54H80.16A1.56,1.56,0,0,1,81.7,21.3V76.39A1.57,1.57,0,0,1,80.16,78Zm-26.4,0h-16a1.57,1.57,0,0,1-1.54-1.58V8.9A1.56,1.56,0,0,1,37.8,7.36h16A1.56,1.56,0,0,1,55.31,8.9V76.39A1.57,1.57,0,0,1,53.77,78ZM30.93,78H13.56A1.57,1.57,0,0,1,12,76.39V36.53A1.56,1.56,0,0,1,13.56,35H30.93a1.57,1.57,0,0,1,1.58,1.54V76.39A1.58,1.58,0,0,1,30.93,78Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-document">
        <path d="M74.42,0H13.58a2.91,2.91,0,0,0-2.93,2.89V85.11A2.91,2.91,0,0,0,13.58,88H74.42a2.91,2.91,0,0,0,2.93-2.89V2.89A2.91,2.91,0,0,0,74.42,0ZM71.49,24.72V82.22h-55V5.78h55V24.72Z"></path>
        <path d="M62.86,47.69H25.14a2.21,2.21,0,0,0-2.22,2.22,2.24,2.24,0,0,0,2.22,2.22H62.86a2.24,2.24,0,0,0,2.22-2.22A2.21,2.21,0,0,0,62.86,47.69Z"></path>
        <path d="M62.86,35.88H25.14a2.2,2.2,0,1,0,0,4.4H62.86A2.2,2.2,0,1,0,62.86,35.88Z"></path>
        <path d="M62.86,12.78H25.14a2.2,2.2,0,1,0,0,4.4H62.86A2.2,2.2,0,1,0,62.86,12.78Z"></path>
        <path d="M62.86,24.6H25.14a2.21,2.21,0,0,0-2.22,2.22A2.24,2.24,0,0,0,25.14,29H62.86a2.24,2.24,0,0,0,2.22-2.22A2.21,2.21,0,0,0,62.86,24.6Z"></path>
    </symbol>
    <symbol viewBox="0 0 64 64" id="shrs-icon-download">
        <g>
            <g>
                <path d="M20.44,36.75,31.13,49.6a1.13,1.13,0,0,0,1.74,0L43.56,36.75a1.24,1.24,0,0,0-.87-2.1H38.21V15.22A1.24,1.24,0,0,0,37,14H27a1.24,1.24,0,0,0-1.23,1.23V34.65H21.31A1.24,1.24,0,0,0,20.44,36.75Z"></path>
                <path d="M32,62A30,30,0,1,0,2,32,30,30,0,0,0,32,62ZM32,6.8A25.2,25.2,0,1,1,6.8,32,25.2,25.2,0,0,1,32,6.8Z"></path>
            </g>
        </g>
    </symbol>

    <symbol viewBox="0 0 88 88" id="shrs-icon-download-nocircle">
        <g>
             <path d="M 12.92851,52.025987 42.480595,86.98816 c 1.0902,1.32907 3.10765,1.32907 4.19784,0 l 29.50306,-34.963331 c 3.57003,-4.090612 3.77188,-10.947658 -2.42461,-10.82152 H 59.561445 V 3.1756277 c -0.0254,-1.62238 -1.31389,-2.93509892 -2.91918,-2.97415082 H 32.516771 c -1.633404,0.01317 -2.954316,1.34800982 -2.967435,2.99852882 V 41.204509 l -14.194784,-0.0012 c -6.4856333,0.04917 -6.6503183,5.61645 -2.426042,10.822678 z"></path>
        </g>
    </symbol>

    <symbol viewBox="0 0 88 88" id="shrs-icon-edit">
        <path d="M86.16,25.56L53,58.37a7.4,7.4,0,0,1-3.23,1.89L40.94,62.9c-0.13,0-.21,0-0.29,0a1,1,0,0,1-.75-0.34,1.07,1.07,0,0,1-.29-1l2.64-8.76a7.76,7.76,0,0,1,1.93-3.18L77.31,16.8a6.22,6.22,0,0,1,8.84,0A6.09,6.09,0,0,1,88,21.16,6.26,6.26,0,0,1,86.16,25.56ZM36.08,52.67H14.5a2.22,2.22,0,1,1,0-4.44H37.8a12.6,12.6,0,0,0-1.26,2.89ZM14.5,64.49a2.21,2.21,0,0,1-2.22-2.22A2.24,2.24,0,0,1,14.5,60H33.86a6.72,6.72,0,0,0,.34,4.44H14.5Zm0-23.63a2.22,2.22,0,0,1,0-4.44H49.07l-4.48,4.44H14.5ZM52.21,17.18H14.5a2.2,2.2,0,1,1,0-4.4H52.21A2.2,2.2,0,1,1,52.21,17.18Zm0,11.86H14.5a2.24,2.24,0,0,1-2.22-2.22A2.21,2.21,0,0,1,14.5,24.6H52.21a2.21,2.21,0,0,1,2.22,2.22A2.24,2.24,0,0,1,52.21,29ZM60.85,5.78h-55V82.22h55V59l5.87-5.78V85.11A2.91,2.91,0,0,1,63.78,88H2.93A2.91,2.91,0,0,1,0,85.11V2.89A2.91,2.91,0,0,1,2.93,0H63.78a2.91,2.91,0,0,1,2.93,2.89v16l-5.87,5.78V5.78Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-envelop">
        <path d="M82.29,9.71H5.75a5.7,5.7,0,0,0-4.08,1.6A5.3,5.3,0,0,0,0,15.26V72.85a5.2,5.2,0,0,0,1.67,3.88,5.76,5.76,0,0,0,4.08,1.56H82.29a5.74,5.74,0,0,0,4-1.56A5.33,5.33,0,0,0,88,72.85V15.26A5.58,5.58,0,0,0,82.29,9.71Zm0.48,63.13a0.41,0.41,0,0,1-.48.44H5.75a0.39,0.39,0,0,1-.48-0.44v-50L42.11,58.35A2.51,2.51,0,0,0,44,59.1h0.07a2.64,2.64,0,0,0,2-.75L82.76,22.81v50h0Zm0-57.15L44.05,53.12,5.27,15.7V15.26a0.44,0.44,0,0,1,.48-0.54H82.29a0.45,0.45,0,0,1,.48.54V15.7h0Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-eye">
        <path d="M44,15.25A48.31,48.31,0,0,0,.18,43.2a1.89,1.89,0,0,0,0,1.6,48.33,48.33,0,0,0,87.65,0,1.89,1.89,0,0,0,0-1.6A48.31,48.31,0,0,0,44,15.25Zm0,48.59A19.83,19.83,0,1,1,63.83,44,19.83,19.83,0,0,1,44,63.84Z"></path>
        <circle cx="44" cy="44" r="12.7"></circle>
    </symbol>
    <symbol viewBox="0 0 86.17 90" id="shrs-icon-file-doc">
        <path d="M84.1,33h-8v-12s0-.06,0-.1a2.1,2.1,0,0,0,0-.27,1.92,1.92,0,0,0-.06-.2c0-.05,0-.11,0-.16a1.91,1.91,0,0,0-.11-.22L75.8,20a1.88,1.88,0,0,0-.24-.3L56.41.56l0,0a2,2,0,0,0-.27-.22L56,.24,55.79.15l-.2-.06-.15,0a2.07,2.07,0,0,0-.38,0H12a1.91,1.91,0,0,0-1.91,1.91V33h-8A2.06,2.06,0,0,0,0,35.1V64.47a2.07,2.07,0,0,0,2.08,2.07h8V88.08A1.91,1.91,0,0,0,12,90H74.2a1.91,1.91,0,0,0,1.92-1.92V66.54h8a2.06,2.06,0,0,0,2.07-2.07V35.1A2.07,2.07,0,0,0,84.1,33ZM67.23,55.1a8.4,8.4,0,0,0,2.93-.54l1.2-.53,1,5.44-.66.36a11.46,11.46,0,0,1-5.06,1c-6.28,0-10.33-4.2-10.33-10.7,0-6.76,4.31-11.3,10.73-11.3a10.6,10.6,0,0,1,4.88,1l.68.38L71.3,45.63l-1.16-.53a7,7,0,0,0-2.93-.62c-2.94,0-4.62,2-4.62,5.35S64.28,55.1,67.23,55.1ZM57,6.54,69.58,19.15H57ZM13.88,3.83H53.14V21.06A1.91,1.91,0,0,0,55.05,23H72.29V33H13.88Zm39.77,46a11.68,11.68,0,0,1-2.59,8,9,9,0,0,1-7,3,9,9,0,0,1-7.06-3,11.51,11.51,0,0,1-2.6-7.9,14.2,14.2,0,0,1,.89-5.32,10.26,10.26,0,0,1,1.83-2.94,8,8,0,0,1,2.64-2A10.09,10.09,0,0,1,44,38.79a9,9,0,0,1,7,3A11.7,11.7,0,0,1,53.65,49.81Zm-22-.51c0,4.65-1.81,7.29-3.32,8.69-2.06,1.86-5.08,2.81-9,2.81a36.4,36.4,0,0,1-4.88-.3l-.9-.13v-21l.87-.15a35.7,35.7,0,0,1,5.79-.42c3.59,0,6.06.72,8,2.33C30.5,42.92,31.69,45.75,31.69,49.3Zm40.6,36.87H13.88V66.54h58.4Z"></path>
        <path d="M44,44.35a3.2,3.2,0,0,0-2.65,1.22,6.66,6.66,0,0,0-1.08,4.21A6.56,6.56,0,0,0,41.42,54,3.19,3.19,0,0,0,44,55.22,3.13,3.13,0,0,0,46.63,54a6.7,6.7,0,0,0,1.1-4.26,6.63,6.63,0,0,0-1.06-4.17A3.17,3.17,0,0,0,44,44.35Z"></path>
        <path d="M20.68,44.29c-.31,0-.57,0-.79,0V55.23h.43a4.68,4.68,0,0,0,3.59-1.34,6.41,6.41,0,0,0,1.36-4.45,5.41,5.41,0,0,0-1.29-4A4.47,4.47,0,0,0,20.68,44.29Z"></path>
        <text x="0" y="115" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">Created by To Uyen</text>
        <text x="0" y="120" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">from the Noun Project</text>
    </symbol>
    <symbol viewBox="0 0 86.17 90" id="shrs-icon-file-docx">
        <path d="M34.43,44.84A2.91,2.91,0,0,0,32,46a6.07,6.07,0,0,0-1,3.82,6,6,0,0,0,1,3.81,2.89,2.89,0,0,0,2.38,1.14,2.84,2.84,0,0,0,2.35-1.13,6.09,6.09,0,0,0,1-3.87,6,6,0,0,0-1-3.79A2.88,2.88,0,0,0,34.43,44.84Z"></path>
        <path d="M13.59,54.74a4.26,4.26,0,0,0,3.27-1.23,5.82,5.82,0,0,0,1.24-4,4.93,4.93,0,0,0-1.18-3.62,4.08,4.08,0,0,0-3-1.07l-.72,0v9.93h.4Z"></path>
        <path d="M84.1,33h-8v-12s0-.06,0-.1a2.1,2.1,0,0,0,0-.27,1.92,1.92,0,0,0-.06-.2c0-.05,0-.11,0-.16a1.91,1.91,0,0,0-.11-.22L75.8,20a1.88,1.88,0,0,0-.24-.3L56.41.56l0,0a2,2,0,0,0-.27-.22L56,.24,55.79.15l-.2-.06-.15,0a2.07,2.07,0,0,0-.38,0H12a1.91,1.91,0,0,0-1.91,1.91V33h-8A2.06,2.06,0,0,0,0,35.1V64.47a2.07,2.07,0,0,0,2.08,2.07h8V88.08A1.91,1.91,0,0,0,12,90H74.2a1.91,1.91,0,0,0,1.92-1.92V66.54h8a2.06,2.06,0,0,0,2.07-2.07V35.1A2.07,2.07,0,0,0,84.1,33ZM57,6.54,69.58,19.15H57ZM13.88,3.83H53.14V21.06A1.91,1.91,0,0,0,55.05,23H72.29V33H13.88ZM54.77,54.62a7.55,7.55,0,0,0,2.66-.5l1.1-.47.88,4.94-.6.32a10.26,10.26,0,0,1-4.59.87c-5.71,0-9.4-3.81-9.4-9.73,0-6.15,3.92-10.27,9.75-10.27a9.65,9.65,0,0,1,4.44.9l.62.35-1.17,5-1-.48A6.4,6.4,0,0,0,54.75,45c-2.67,0-4.2,1.77-4.2,4.87S52.09,54.62,54.77,54.62Zm-11.6-4.81a10.64,10.64,0,0,1-2.35,7.26,8.15,8.15,0,0,1-6.39,2.72A8.19,8.19,0,0,1,28,57.08a10.49,10.49,0,0,1-2.36-7.19,13,13,0,0,1,.8-4.83,9.41,9.41,0,0,1,1.67-2.68,7.27,7.27,0,0,1,2.4-1.81,9.19,9.19,0,0,1,3.86-.78,8.19,8.19,0,0,1,6.41,2.72A10.64,10.64,0,0,1,43.18,49.81ZM8.28,59.52l-.82-.12V40.31l.79-.14a33.19,33.19,0,0,1,5.27-.38c3.26,0,5.51.65,7.29,2.12,2,1.64,3.11,4.22,3.11,7.44,0,4.23-1.64,6.63-3,7.9-1.87,1.69-4.62,2.55-8.18,2.55A33,33,0,0,1,8.28,59.52Zm64,26.65H13.88V66.54h58.4Zm-.15-26.81-2.73-4.63-2.73,4.63H60.15l6-9.94-5.47-9.2h6.44l2.38,4.15,2.33-4.15h6.4l-5.48,9.32,6,9.83Z"></path>
        <text x="0" y="115" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">Created by To Uyen</text>
        <text x="0" y="120" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">from the Noun Project</text>
    </symbol>
    <symbol viewBox="0 0 86.17 90" id="shrs-icon-file-jpeg">
        <path d="M33.56,44.77a15.75,15.75,0,0,0-2.41-.11h-.83v3.21h1.06a8.26,8.26,0,0,0,2.5-.22,1.54,1.54,0,0,0,.7-.54,1.48,1.48,0,0,0,.25-.86,1.4,1.4,0,0,0-.34-1A1.53,1.53,0,0,0,33.56,44.77Z"></path>
        <path d="M84.1,33h-8v-12s0-.06,0-.1a2.1,2.1,0,0,0,0-.27,1.92,1.92,0,0,0-.06-.2c0-.05,0-.11,0-.16a1.91,1.91,0,0,0-.11-.22L75.8,20a1.88,1.88,0,0,0-.24-.3L56.41.56l0,0a2,2,0,0,0-.27-.22L56,.24,55.79.15l-.2-.06-.15,0a2.07,2.07,0,0,0-.38,0H12a1.92,1.92,0,0,0-1.92,1.91V33h-8A2.06,2.06,0,0,0,0,35.1V64.47a2.07,2.07,0,0,0,2.08,2.07h8V88.08A1.91,1.91,0,0,0,12,90H74.2a1.91,1.91,0,0,0,1.92-1.92V66.54h8a2.06,2.06,0,0,0,2.07-2.07V35.1A2.07,2.07,0,0,0,84.1,33ZM57,6.54,69.58,19.15H57ZM13.88,3.83H53.14V21.06A1.91,1.91,0,0,0,55.05,23H72.29V33H13.88ZM56.31,47.36v4.27h-8v2.65h8.85v5H42.83V39.93H56.91v5H48.36v2.46Zm-16-1.16a6.58,6.58,0,0,1-.69,3.13,5.69,5.69,0,0,1-1.77,2,6.12,6.12,0,0,1-2.16,1,20.56,20.56,0,0,1-3.88.27H30.32v6.62H24.77V39.93h6.81a20.86,20.86,0,0,1,4.46.3,5.5,5.5,0,0,1,3,2A6.27,6.27,0,0,1,40.27,46.21ZM9.33,52.69v-.9l5-.62,0,1.1a3.85,3.85,0,0,0,.27,1.6.67.67,0,0,0,.67.4c.34,0,.56-.07.65-.21a4.75,4.75,0,0,0,.26-2.16v-12h5.16V51.69a12.57,12.57,0,0,1-.39,3.54,5.57,5.57,0,0,1-2,2.9,6.16,6.16,0,0,1-3.77,1.1,5.5,5.5,0,0,1-4.32-1.75A7.08,7.08,0,0,1,9.33,52.69Zm63,33.48H13.88V66.54h58.4Zm4.56-29.69-.28.3a9.66,9.66,0,0,1-3.27,2.11,10.89,10.89,0,0,1-4.29.9,9.22,9.22,0,0,1-4.9-1.29,8.1,8.1,0,0,1-3.2-3.73,12.43,12.43,0,0,1-1-5A11.8,11.8,0,0,1,61,44.45a8.4,8.4,0,0,1,3.47-3.67,8.7,8.7,0,0,1,4.31-1,8.14,8.14,0,0,1,5.28,1.59,7.14,7.14,0,0,1,2.45,4.09l-5.23,1.12a2.6,2.6,0,0,0-.83-1.23,2.57,2.57,0,0,0-1.68-.51,3.17,3.17,0,0,0-2.57,1.09,5.56,5.56,0,0,0-1,3.65,6.29,6.29,0,0,0,1,4,3,3,0,0,0,2.51,1.16,4.18,4.18,0,0,0,1.62-.35,6.49,6.49,0,0,0,1.11-.6v-.66H67.85v-5h9Z"></path>
        <text x="0" y="115" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">Created by To Uyen</text>
        <text x="0" y="120" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">from the Noun Project</text>
    </symbol>
    <symbol viewBox="0 0 86.2 90" id="shrs-icon-file-jpg">
        <path d="M84.1,33h-8V20.6c0-.1,0-.1-.1-.2v-.2c0-.1-.1-.1-.1-.2h-.1c-.1-.1-.1-.2-.2-.3L56.4.6h0c-.1-.1-.2-.2-.3-.2L55.9.3,55.7.2,55.5.1h-.2c0-.1-.1-.1-.2-.1H12a2,2,0,0,0-1.9,1.9V33h-8A2,2,0,0,0,0,34.9S0,35,0,35V64.5a2.11,2.11,0,0,0,2.1,2.1h8V88.1A1.9,1.9,0,0,0,12,90H74.2a2,2,0,0,0,1.9-1.9V66.5h8a2.09,2.09,0,0,0,2.1-2h0V35.1A2.11,2.11,0,0,0,84.1,33ZM57,6.5,69.6,19.1H57ZM13.9,3.8H53.2V21A1.84,1.84,0,0,0,55,22.9H72.4v10H13.9ZM59.3,53.9A3.68,3.68,0,0,0,62,55.2a3.92,3.92,0,0,0,1.8-.4,7.46,7.46,0,0,0,1.2-.7v-.7H61V48h9.9v9.2l-.3.3A9.67,9.67,0,0,1,67,59.8a12.78,12.78,0,0,1-4.7,1,11.43,11.43,0,0,1-5.4-1.4,8.72,8.72,0,0,1-3.5-4.1A13.92,13.92,0,0,1,53.6,44a9.18,9.18,0,0,1,3.8-4,10.12,10.12,0,0,1,10.4.5A7.77,7.77,0,0,1,70.5,45l-5.7,1.2a2.59,2.59,0,0,0-2.7-1.9,3.33,3.33,0,0,0-2.8,1.2,6.4,6.4,0,0,0-1.1,4A7.43,7.43,0,0,0,59.3,53.9Zm-9-7.8a7.73,7.73,0,0,1-.8,3.4,5.56,5.56,0,0,1-2.1,2.2,8.49,8.49,0,0,1-2.6,1.1,35.53,35.53,0,0,1-4.6.3H38.5v7.3H32.1V39.2H40a23.27,23.27,0,0,1,5.3.3A6.32,6.32,0,0,1,50.3,46.1ZM28.9,38.8V52.2a18.1,18.1,0,0,1-.4,4,6.43,6.43,0,0,1-2.3,3.3,6.72,6.72,0,0,1-4.3,1.2,6.21,6.21,0,0,1-4.9-2,8.11,8.11,0,0,1-1.7-5.4v-1l5.6-.7v1.2a5.23,5.23,0,0,0,.3,1.8.89.89,0,0,0,.8.5c.6,0,.7-.2.8-.3a5.07,5.07,0,0,0,.4-2.4V38.8ZM72.3,86.2H13.9V66.5H72.3Z"></path>
        <path d="M42.3,44.5a28.1,28.1,0,0,0-2.9-.1h-1v3.5h1.3a10.12,10.12,0,0,0,3-.2,1.56,1.56,0,0,0,.8-.6,1.61,1.61,0,0,0,.3-.9,1.5,1.5,0,0,0-.4-1.1A1.68,1.68,0,0,0,42.3,44.5Z"></path>
        <text x="0" y="115" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">Created by To Uyen</text>
        <text x="0" y="120" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">from the Noun Project</text>
    </symbol>
    <symbol viewBox="0 0 86.17 90" id="shrs-icon-file-pdf">
        <path d="M42.58,44.3c-.31,0-.57,0-.79,0V55.21h.44a4.64,4.64,0,0,0,3.58-1.34,6.37,6.37,0,0,0,1.36-4.43,5.42,5.42,0,0,0-1.29-4A4.47,4.47,0,0,0,42.58,44.3Z"></path>
        <path d="M84.1,33h-8v-12s0-.06,0-.1a2.12,2.12,0,0,0,0-.27,1.92,1.92,0,0,0-.06-.2c0-.05,0-.11,0-.16a1.82,1.82,0,0,0-.11-.21L75.8,20a1.88,1.88,0,0,0-.24-.3L56.41.56l0,0a1.88,1.88,0,0,0-.27-.22L56,.24,55.79.15l-.2-.06-.15,0a1.83,1.83,0,0,0-.38,0H12a1.91,1.91,0,0,0-1.91,1.91V33h-8A2.06,2.06,0,0,0,0,35.1V64.47a2.07,2.07,0,0,0,2.08,2.07h8V88.08A1.91,1.91,0,0,0,12,90H74.2a1.91,1.91,0,0,0,1.92-1.92V66.54h8a2.06,2.06,0,0,0,2.07-2.07V35.1A2.07,2.07,0,0,0,84.1,33ZM72.65,47.34v5H64.25v8.27H57.72V39h16v5.53H64.25v2.86h8.41ZM57,6.54,69.58,19.15H57ZM13.88,3.83H53.14V21.06A1.92,1.92,0,0,0,55.05,23H72.29V33H13.88ZM53.55,49.29c0,4.64-1.8,7.27-3.31,8.66-2.05,1.85-5.07,2.79-9,2.79a35.92,35.92,0,0,1-4.86-.3l-.9-.13V39.4l.87-.16a36.16,36.16,0,0,1,5.77-.42c3.57,0,6,.72,8,2.32C52.37,43,53.55,45.77,53.55,49.29Zm-22-3.3a6.94,6.94,0,0,1-.85,3.53,6.53,6.53,0,0,1-2.18,2.28,8,8,0,0,1-2.65,1.09,28.19,28.19,0,0,1-4.76.29H19.31v7.44H12.5V39h8.37a28.4,28.4,0,0,1,5.49.34A6.76,6.76,0,0,1,30,41.56,6.63,6.63,0,0,1,31.52,46ZM72.29,86.17H13.88V66.54h58.4Z"></path>
        <path d="M23.29,44.39a22.34,22.34,0,0,0-3-.12h-1v3.6h1.31a11.7,11.7,0,0,0,3.06-.24,1.87,1.87,0,0,0,.87-.62,1.55,1.55,0,0,0,.31-1,1.5,1.5,0,0,0-.41-1.1A2,2,0,0,0,23.29,44.39Z"></path>
        <text x="0" y="115" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">Created by To Uyen</text>
        <text x="0" y="120" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">from the Noun Project</text>
    </symbol>
    <symbol viewBox="0 0 86.17 90" id="shrs-icon-file-ppt">
        <path d="M23.8,44.71A21.54,21.54,0,0,0,21,44.6H20v3.46h1.26a11.11,11.11,0,0,0,3-.22,1.83,1.83,0,0,0,.83-.59,1.54,1.54,0,0,0,.3-.93,1.43,1.43,0,0,0-.4-1.06A1.92,1.92,0,0,0,23.8,44.71Z"></path>
        <path d="M45.11,44.71a21.61,21.61,0,0,0-2.85-.11h-1v3.46h1.26a11.15,11.15,0,0,0,3-.22,1.86,1.86,0,0,0,.83-.59,1.53,1.53,0,0,0,.29-.93,1.42,1.42,0,0,0-.4-1.06A1.89,1.89,0,0,0,45.11,44.71Z"></path>
        <path d="M84.1,33h-8v-12s0-.06,0-.1a2.1,2.1,0,0,0,0-.27,1.92,1.92,0,0,0-.06-.2c0-.05,0-.11,0-.16a1.91,1.91,0,0,0-.11-.22L75.8,20a1.88,1.88,0,0,0-.24-.3L56.41.56l0,0a2,2,0,0,0-.27-.22L56,.24,55.79.15l-.2-.06-.15,0a2.07,2.07,0,0,0-.38,0H12a1.91,1.91,0,0,0-1.91,1.91V33h-8A2.06,2.06,0,0,0,0,35.1V64.47a2.07,2.07,0,0,0,2.08,2.07h8V88.08A1.91,1.91,0,0,0,12,90H74.2a1.91,1.91,0,0,0,1.92-1.92V66.54h8a2.06,2.06,0,0,0,2.07-2.07V35.1A2.07,2.07,0,0,0,84.1,33Zm-29,12.12v-5.8H73.27v5.8H67.63V60H60.78V45.15ZM57,6.54,69.58,19.15H57ZM13.88,3.83H53.14V21.06A1.91,1.91,0,0,0,55.05,23H72.29V33H13.88ZM53,46.26a6.64,6.64,0,0,1-.82,3.39,6.25,6.25,0,0,1-2.1,2.2,7.71,7.71,0,0,1-2.56,1.05,27.6,27.6,0,0,1-4.59.28h-1.7v7.16H35V39.47h7.8a27.15,27.15,0,0,1,5.28.33A6.53,6.53,0,0,1,51.62,42,6.39,6.39,0,0,1,53,46.26Zm-21.31,0a6.67,6.67,0,0,1-.81,3.39,6.3,6.3,0,0,1-2.11,2.2,7.69,7.69,0,0,1-2.56,1.05,27.54,27.54,0,0,1-4.58.28H20v7.16h-6.3V39.47h7.79a27.13,27.13,0,0,1,5.28.33A6.52,6.52,0,0,1,30.31,42,6.39,6.39,0,0,1,31.73,46.26ZM72.29,86.17H13.88V66.54h58.4Z"></path>
        <text x="0" y="115" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">Created by To Uyen</text>
        <text x="0" y="120" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">from the Noun Project</text>
    </symbol>
    <symbol viewBox="0 0 86.17 90" id="shrs-icon-file-pptx">
        <path d="M84.1,33h-8v-12s0-.06,0-.1a2.1,2.1,0,0,0,0-.27,1.92,1.92,0,0,0-.06-.2c0-.05,0-.11,0-.16a1.91,1.91,0,0,0-.11-.22L75.8,20a1.88,1.88,0,0,0-.24-.3L56.41.56l0,0a2,2,0,0,0-.27-.22L56,.24,55.79.15l-.2-.06-.15,0a2.07,2.07,0,0,0-.38,0H12a1.92,1.92,0,0,0-1.92,1.91V33h-8A2.06,2.06,0,0,0,0,35.1V64.47a2.07,2.07,0,0,0,2.08,2.07h8V88.08A1.91,1.91,0,0,0,12,90H74.2a1.91,1.91,0,0,0,1.92-1.92V66.54h8a2.06,2.06,0,0,0,2.07-2.07V35.1A2.07,2.07,0,0,0,84.1,33ZM57,6.54,69.58,19.15H57ZM13.88,3.83H53.14V21.06A1.91,1.91,0,0,0,55.05,23H72.29V33H13.88ZM42.64,45V39.79H58.12V45H53.29V59.79H47.48V45ZM40.8,46.37a7.35,7.35,0,0,1-.66,3.24,5.86,5.86,0,0,1-1.72,2.15,5.7,5.7,0,0,1-2.12,1,17.78,17.78,0,0,1-3.68.28h-1v6.7H25.93v-20h6.54a16.13,16.13,0,0,1,4.23.31,5.38,5.38,0,0,1,2.95,2.17A7,7,0,0,1,40.8,46.37ZM9,59.79v-20h6.54a16.13,16.13,0,0,1,4.23.31,5.39,5.39,0,0,1,2.95,2.17,7,7,0,0,1,1.15,4.1,7.35,7.35,0,0,1-.66,3.24,5.81,5.81,0,0,1-1.71,2.15,5.7,5.7,0,0,1-2.13,1,17.8,17.8,0,0,1-3.67.28h-1v6.7ZM72.29,86.17H13.88V66.54h58.4ZM70.67,59.66l-2.94-5-3,5H58.3L64.47,49.4l-5.64-9.48H65.2l2.58,4.51,2.53-4.51h6.33L71,49.53l6.18,10.13Z"></path>
        <path d="M34.23,45.13a12.56,12.56,0,0,0-2.16-.1h-.48v2.81h.69a6.89,6.89,0,0,0,2.17-.19,1.1,1.1,0,0,0,.52-.44,1.46,1.46,0,0,0,.2-.79,1.35,1.35,0,0,0-.26-.89A1.13,1.13,0,0,0,34.23,45.13Z"></path>
        <path d="M17.52,47.65a1.08,1.08,0,0,0,.52-.44,1.45,1.45,0,0,0,.2-.79,1.34,1.34,0,0,0-.27-.89,1.11,1.11,0,0,0-.68-.4,12.56,12.56,0,0,0-2.16-.1h-.48v2.81h.69A6.85,6.85,0,0,0,17.52,47.65Z"></path>
        <text x="0" y="115" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">Created by To Uyen</text>
        <text x="0" y="120" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">from the Noun Project</text>
    </symbol>
    <symbol viewBox="0 0 86.17 90" id="shrs-icon-file-zip">
        <path d="M61.41,44.86a15.25,15.25,0,0,0-2.46-.11H58.1V48h1.09a8.47,8.47,0,0,0,2.54-.21,1.57,1.57,0,0,0,.72-.55,1.54,1.54,0,0,0,.25-.88,1.43,1.43,0,0,0-.35-1A1.63,1.63,0,0,0,61.41,44.86Z"></path>
        <path d="M84.1,33h-8v-12s0-.06,0-.1a2.12,2.12,0,0,0,0-.27,1.92,1.92,0,0,0-.06-.2c0-.05,0-.11,0-.16a1.82,1.82,0,0,0-.11-.21L75.8,20a1.88,1.88,0,0,0-.24-.3L56.41.56l0,0a1.88,1.88,0,0,0-.27-.22L56,.24,55.79.15l-.2-.06-.15,0a1.83,1.83,0,0,0-.38,0H12a1.91,1.91,0,0,0-1.91,1.91V33h-8A2.06,2.06,0,0,0,0,35.1V64.47a2.07,2.07,0,0,0,2.08,2.07h8V88.08A1.91,1.91,0,0,0,12,90H74.2a1.91,1.91,0,0,0,1.92-1.92V66.54h8a2.06,2.06,0,0,0,2.07-2.07V35.1A2.07,2.07,0,0,0,84.1,33ZM57,6.54,69.58,19.15H57ZM13.88,3.83H53.14V21.06A1.92,1.92,0,0,0,55.05,23H72.29V33H13.88ZM69.42,45.94a7.38,7.38,0,0,1-.79,3.55,6.51,6.51,0,0,1-2,2.31,7,7,0,0,1-2.46,1.09,23.85,23.85,0,0,1-4.39.3H58.14v7.5H51.86V38.83h7.72a23.63,23.63,0,0,1,5.05.33,6.21,6.21,0,0,1,3.41,2.3A7.1,7.1,0,0,1,69.42,45.94ZM46.33,38.83V60.69H39.79V38.83ZM18,44.09v-5.2H35.41v4.29l-10,12.25H35.84v5.31H16.75v-5l9.47-11.66H18ZM72.29,86.17H13.88V66.54h58.4Z"></path>
        <text x="0" y="115" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">Created by To Uyen</text>
        <text x="0" y="120" fill="#000000" font-size="5px" font-weight="bold" font-family="'Helvetica Neue', Helvetica, Arial-Unicode, Arial, Sans-serif">from the Noun Project</text>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-folder">
        <path d="M83.94,30.64H4.06c-4.1,0-4.2,2-4,4.4l3.6,40c0.2,2.4.8,4.4,4.8,4.4H79.54c4.1,0,4.6-2,4.8-4.4l3.6-40C88.15,32.64,87.95,30.64,83.94,30.64ZM81,20.93c-0.5-2-2.9-3.6-5.41-3.6h-30A12.57,12.57,0,0,1,38,14.22l-2.6-2.6a12.33,12.33,0,0,0-7.61-3.1H13.47a5.14,5.14,0,0,0-4.91,4.4L7.27,24.43H81.84Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-gear">
        <g>
            <path d="M87.72,39.11a2.86,2.86,0,0,0-2.83-2.17A9.72,9.72,0,0,1,78.25,20a2.44,2.44,0,0,0,.27-3.32,43.54,43.54,0,0,0-7-7,2.45,2.45,0,0,0-3.34.27,10.14,10.14,0,0,1-11,2.47,9.77,9.77,0,0,1-5.94-9.55A2.44,2.44,0,0,0,49.12.29a43.93,43.93,0,0,0-9.9,0A2.45,2.45,0,0,0,37,2.78a9.79,9.79,0,0,1-6,9.39A10.17,10.17,0,0,1,20.13,9.69a2.45,2.45,0,0,0-3.32-.28,43.7,43.7,0,0,0-7.12,7,2.45,2.45,0,0,0,.26,3.34,9.75,9.75,0,0,1,2.46,11,10.17,10.17,0,0,1-9.57,5.95A2.39,2.39,0,0,0,.29,38.88a44.08,44.08,0,0,0,0,10,2.91,2.91,0,0,0,2.89,2.16,9.65,9.65,0,0,1,9.05,6A9.78,9.78,0,0,1,9.75,68a2.45,2.45,0,0,0-.27,3.32,43.7,43.7,0,0,0,7,7,2.45,2.45,0,0,0,3.35-.27,10.13,10.13,0,0,1,11-2.47,9.76,9.76,0,0,1,6,9.55,2.44,2.44,0,0,0,2.16,2.57,43.79,43.79,0,0,0,9.9,0A2.45,2.45,0,0,0,51,85.21a9.78,9.78,0,0,1,6-9.38,10.15,10.15,0,0,1,10.9,2.48,2.46,2.46,0,0,0,3.32.28,43.81,43.81,0,0,0,7.12-7A2.44,2.44,0,0,0,78,68.21a9.75,9.75,0,0,1-2.47-11,9.85,9.85,0,0,1,9-6h.54a2.45,2.45,0,0,0,2.58-2.16A44,44,0,0,0,87.72,39.11ZM44.07,58.77A14.68,14.68,0,1,1,58.75,44.09,14.69,14.69,0,0,1,44.07,58.77Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-gestionnaire">
        <path d="M44,46.86c13,0,17.69-13,18.92-23.72C64.44,9.94,58.23,0,44,0S23.6,9.94,25.1,23.14C26.33,33.84,31,46.86,44,46.86Z"></path>
        <path d="M77.86,65.52c-.88-5-2-12.23-6.34-15.49-2.46-1.85-5.72-2.46-8.58-3.61a34.48,34.48,0,0,1-3.83-1.67A19.82,19.82,0,0,1,44,51.35a19.82,19.82,0,0,1-15.09-6.6,34.48,34.48,0,0,1-3.83,1.67C22.24,47.56,19,48.18,16.52,50c-4.36,3.26-5.46,10.52-6.34,15.49A83.63,83.63,0,0,0,8.82,77.92c-.09,3.21,1.5,3.65,4.18,4.62A70.62,70.62,0,0,0,23.34,85.4,102.32,102.32,0,0,0,44,88,102.32,102.32,0,0,0,64.7,85.4,70.62,70.62,0,0,0,75,82.54c2.68-1,4.27-1.41,4.18-4.62A83.63,83.63,0,0,0,77.86,65.52ZM39.51,74.79l3-13.48c-5.93-8.29.44-8.7,1.56-8.7s7.49.41,1.56,8.7l3,13.48Z"></path>
    </symbol>
    <symbol viewBox="0 0 64 64" id="shrs-icon-gp-col">
        <path d="M32 0a32 32 0 1 0 32 32A32 32 0 0 0 32 0zm0 62.545A30.545 30.545 0 1 1 62.545 32 30.545 30.545 0 0 1 32 62.545z"></path>
        <path d="M32 5.385A26.615 26.615 0 0 0 5.385 32 26.615 26.615 0 0 0 32 58.615 26.615 26.615 0 0 0 58.615 32 26.615 26.615 0 0 0 32 5.385zm.97 10.465c1.263 0 2.46.125 3.585.377 1.126.25 2.182.63 3.17 1.134.987.506 1.86 1.07 2.615 1.695.756.625 1.396 1.31 1.922 2.056.525.74.92 1.46 1.18 2.16.264.7.396 1.38.396 2.04 0 .725-.254 1.363-.766 1.917-.51.554-1.13.832-1.853.832-.81 0-1.423-.194-1.835-.578-.398-.383-.845-1.043-1.342-1.98-.412-.774-.86-1.45-1.348-2.03-.486-.578-1.01-1.06-1.57-1.443-.554-.384-1.174-.672-1.86-.864-.685-.192-1.437-.29-2.253-.29-1.3 0-2.467.247-3.504.74-1.037.495-1.943 1.237-2.717 2.224-.766.987-1.342 2.18-1.726 3.584-.383 1.402-.574 3.014-.574 4.832 0 1.214.086 2.328.256 3.34s.425 1.922.766 2.732c.34.803.753 1.503 1.236 2.104.483.6 1.037 1.1 1.662 1.498.625.397 1.304.695 2.035.894.732.2 1.516.3 2.354.3.908 0 1.746-.113 2.513-.337.766-.224 1.462-.56 2.087-1.008.632-.455 1.187-1.014 1.663-1.678.475-.664.873-1.433 1.193-2.307.27-.824.604-1.49 1.002-2.002.2-.263.457-.46.777-.592.32-.13.7-.196 1.14-.196.754 0 1.4.263 1.94.79.54.525.81 1.177.81 1.958 0 .994-.25 2.075-.747 3.24-.483 1.15-1.25 2.286-2.3 3.408-.527.554-1.125 1.057-1.796 1.508-.67.45-1.416.85-2.232 1.2-.817.347-1.7.61-2.653.783-.95.174-1.97.26-3.056.26-1.647 0-3.146-.156-4.495-.468-1.35-.313-2.578-.796-3.686-1.45-1.095-.667-2.105-1.54-3.028-2.62-.824-.98-1.527-2.075-2.11-3.282-.582-1.222-1.022-2.52-1.32-3.898-.284-1.378-.426-2.84-.426-4.39 0-1.256.09-2.448.272-3.573.18-1.127.452-2.187.814-3.18.37-.995.816-1.917 1.342-2.767.526-.85 1.13-1.625 1.812-2.328.682-.71 1.422-1.335 2.22-1.875.8-.54 1.657-.994 2.573-1.363.916-.37 1.863-.646 2.84-.83.976-.185 1.982-.277 3.02-.277z"></path>
    </symbol>
    <symbol viewBox="0 0 64 64" id="shrs-icon-gp-man">
        <path d="M32 0a32 32 0 1 0 32 32A32 32 0 0 0 32 0zm0 62.545A30.545 30.545 0 1 1 62.545 32 30.545 30.545 0 0 1 32 62.545z"></path>
        <path d="M32 5.385A26.615 26.615 0 0 0 5.385 32 26.615 26.615 0 0 0 32 58.615 26.615 26.615 0 0 0 58.615 32 26.615 26.615 0 0 0 32 5.385zm-11.123 10.73h1.96c1.18 0 2.032.107 2.558.32.54.214.937.597 1.193 1.15.256.555.546 1.456.873 2.706L32 37.4l4.54-17.11c.326-1.25.616-2.15.872-2.704.256-.554.646-.937 1.172-1.15.54-.214 1.4-.32 2.578-.32h1.96c1.208 0 2.166.24 2.876.724.355.234.62.58.8 1.04.177.457.266 1.026.266 1.708v24.717c0 .596-.068 1.118-.203 1.566-.134.45-.336.822-.606 1.12-.263.3-.573.523-.928.672-.355.15-.756.223-1.203.223-.838 0-1.527-.298-2.066-.895-.27-.298-.473-.67-.608-1.12-.135-.447-.202-.97-.202-1.565V22.742l-5.008 19.9c-.326 1.293-.597 2.245-.81 2.856-.2.597-.575 1.144-1.13 1.64-.553.498-1.32.747-2.3.747-.74 0-1.364-.164-1.875-.49-.51-.313-.91-.718-1.193-1.215-.284-.497-.512-1.044-.682-1.64-.156-.612-.32-1.244-.49-1.897l-5.008-19.9v21.562c0 .596-.067 1.118-.2 1.566-.136.45-.34.822-.61 1.12-.26.3-.57.523-.92.672-.352.15-.748.223-1.188.223-.852 0-1.548-.293-2.088-.875-.27-.3-.472-.673-.607-1.123-.136-.45-.204-.98-.204-1.582V19.588c0-.682.09-1.25.267-1.71.178-.457.444-.804.8-1.038.71-.483 1.667-.725 2.874-.725z" fill-rule="evenodd"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-hammer">
        <path d="M28.47,43,2.3,69.16A8,8,0,0,0,13.61,80.47L39.78,54.3ZM39.78,54.3"></path>
        <path d="M50.8,9.47c-7.12-5.56-16.65-5.73-22.29-.08l-1.87,1.87a2.6,2.6,0,0,0,2,4.46c2.65-.18,7.24,2,10.95,5l-1.77,1.77a2.63,2.63,0,0,0,0,3.73,5.28,5.28,0,0,1,0,7.47L32.2,39.26,43.51,50.57,49.12,45a5.28,5.28,0,0,1,7.47,0,2.63,2.63,0,0,0,3.73,0l1.82-1.82,1.78,1.78a5.35,5.35,0,0,0,0,7.47l3.74,3.74a5.41,5.41,0,0,0,7.57,0L86.46,44.91a5.41,5.41,0,0,0,0-7.58l-3.74-3.74a5.35,5.35,0,0,0-7.47,0l-1.78-1.78L75.28,30a2.81,2.81,0,0,0-.05-3.87L56.53,7.42a2.66,2.66,0,0,0-3.75.07l-2,2Zm0,0"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-home">
        <g>
            <path d="M86.9,43a4.2,4.2,0,0,0-.34-6L47.23,2.55a4.9,4.9,0,0,0-6.39.07L1.37,38.8a4.17,4.17,0,0,0-.19,6l1,1a4.31,4.31,0,0,0,5.92.45L11,43.64V82.3a4.29,4.29,0,0,0,4.29,4.29H30.71A4.29,4.29,0,0,0,35,82.3V55.25H54.63V82.3a4.06,4.06,0,0,0,4,4.29H75a4.29,4.29,0,0,0,4.29-4.29V44.18l1.82,1.6c1,.88,3.11.17,4.71-1.58Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-info">
        <path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm9.17,68.2c-2.27.89-4.07,1.56-5.42,2a14.09,14.09,0,0,1-4.69.71,9.33,9.33,0,0,1-6.4-2,6.44,6.44,0,0,1-2.27-5.09,19.32,19.32,0,0,1,.16-2.45,28.68,28.68,0,0,1,.54-2.82l2.84-10c.25-1,.47-1.87.63-2.71a12.66,12.66,0,0,0,.26-2.36,3.62,3.62,0,0,0-.8-2.67A4.42,4.42,0,0,0,35,40a8.27,8.27,0,0,0-2.26.33c-.76.24-1.42.46-2,.66L31.52,38c1.85-.76,3.64-1.4,5.33-1.95a15.56,15.56,0,0,1,4.8-.82,9.14,9.14,0,0,1,6.29,2,6.54,6.54,0,0,1,2.22,5.13c0,.44-.06,1.2-.15,2.29a15.65,15.65,0,0,1-.56,3l-2.82,10c-.24.8-.44,1.73-.64,2.75a14.44,14.44,0,0,0-.27,2.33,3.38,3.38,0,0,0,.89,2.71,4.93,4.93,0,0,0,3.09.73A9,9,0,0,0,52,65.74a13.41,13.41,0,0,0,1.89-.63Zm-.49-40.52a7.11,7.11,0,0,1-9.53,0,5.89,5.89,0,0,1-2-4.46,6,6,0,0,1,2-4.46,7.11,7.11,0,0,1,9.53,0,5.93,5.93,0,0,1,2,4.46,5.87,5.87,0,0,1-2,4.46Zm0,0"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-linkedin">
        <path d="M88,53.45v32.2H69.1V55.55c0-7.5-2.7-12.7-9.6-12.7a10.2,10.2,0,0,0-9.6,6.8,12.84,12.84,0,0,0-.6,4.6v31.4H30.4s0.2-51,0-56.3H49.3v8a0.35,0.35,0,0,0-.1.2h0.1v-0.2c2.5-3.8,7-9.3,17-9.3C78.7,28.15,88,36.15,88,53.45ZM10.7,2.35C4.2,2.35,0,6.55,0,12s4.1,9.7,10.4,9.7h0.1c6.6,0,10.7-4.4,10.7-9.7C21.1,6.55,17.1,2.35,10.7,2.35ZM1.1,85.65H20V29.35H1.1v56.3Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-navigation">
        <g>
            <path d="M44,39.16A4.84,4.84,0,1,0,48.84,44,4.88,4.88,0,0,0,44,39.16ZM44,0A44,44,0,1,0,88,44,44.13,44.13,0,0,0,44,0Zm9.68,53.68L17.6,70.4,34.32,34.32,70.4,17.6Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 32 32" id="shrs-icon-notification">
        <ellipse class="shrs-icon__white-mask" cx="16" cy="24.89" rx="13.67" ry="2.7"></ellipse>
        <path d="M32,25c0-9-8-7-8-14a6.4,6.4,0,0,0-.16-1.5A8.67,8.67,0,0,0,18,2.27a1.78,1.78,0,0,0,0-.38,2,2,0,0,0-4,0,1.79,1.79,0,0,0,0,.38,8.85,8.85,0,0,0-6,8.54c0,.06,0,.12,0,.18,0,7-8,5-8,14,0,2.38,5.33,4.38,12.47,4.88a4,4,0,0,0,7.06,0C26.67,29.38,32,27.38,32,25v0Zm-6.23,1.69a34.25,34.25,0,0,1-5.84,1,4,4,0,0,0-8,0,34.17,34.17,0,0,1-5.84-1A9.78,9.78,0,0,1,2.32,25a9.81,9.81,0,0,1,3.86-1.69A39.59,39.59,0,0,1,16,22.19a39.59,39.59,0,0,1,9.82,1.12A9.78,9.78,0,0,1,29.67,25a9.81,9.81,0,0,1-3.85,1.69Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-organizational-chart">
        <path d="M79.16,62.33V48.44a8.87,8.87,0,0,0-8.79-8.79h-22v-14a13.19,13.19,0,1,0-8.79,0v14h-22a8.87,8.87,0,0,0-8.79,8.79V62.33A13.21,13.21,0,1,0,26.42,74.81a13.33,13.33,0,0,0-8.79-12.49V48.44h22V62.33a13.19,13.19,0,1,0,8.79,0V48.44h22V62.33A13.19,13.19,0,1,0,79.16,62.33ZM13.23,79.21a4.4,4.4,0,1,1,4.39-4.4A4.37,4.37,0,0,1,13.23,79.21Zm30.77,0a4.4,4.4,0,1,1,4.39-4.4A4.37,4.37,0,0,1,44,79.21Zm0-61.53a4.4,4.4,0,1,1,4.39-4.4A4.37,4.37,0,0,1,44,17.68ZM74.76,79.21a4.4,4.4,0,1,1,4.39-4.4A4.37,4.37,0,0,1,74.76,79.21Z"></path>
    </symbol>
    <symbol viewBox="0 0 103.17322 104.31332" id="shrs-icon-outlook">
        <metadata id="shrs-icon-outlook-metadata45">
            <rdf:rdf>
                <cc:work rdf:about="">
                    <dc:format>image/svg+xml</dc:format>
                    <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type>
                </cc:work>
            </rdf:rdf>
        </metadata>
        <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1600" inkscape:window-height="837" id="shrs-icon-outlook-namedview41" showgrid="false" fit-margin-top="0" fit-margin-left="0" fit-margin-right="0" fit-margin-bottom="0" inkscape:zoom="1" inkscape:cx="91.558992" inkscape:cy="89.87632" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="Layer_1"></sodipodi:namedview>
        <path d="m 64.566509,22.116383 v 20.404273 l 7.130526,4.489881 c 0.188058,0.05485 0.595516,0.05877 0.783574,0 L 103.16929,26.320259 c 0,-2.44867 -2.28412,-4.203876 -3.573094,-4.203876 H 64.566509 z" id="shrs-icon-outlook-path3" inkscape:connector-curvature="0"></path>
        <path d="m 64.566509,50.13308 6.507584,4.470291 c 0.916782,0.673874 2.021622,0 2.021622,0 -1.100922,0.673874 30.077495,-20.035993 30.077495,-20.035993 v 37.501863 c 0,4.082422 -2.61322,5.794531 -5.551621,5.794531 H 64.562591 V 50.13308 z" id="shrs-icon-outlook-path5" inkscape:connector-curvature="0"></path>
        <g id="shrs-icon-outlook-g23" transform="matrix(3.9178712,0,0,3.9178712,-13.481403,-41.384473)">
            <path d="m 11.321,20.958 c -0.566,0 -1.017,0.266 -1.35,0.797 -0.333,0.531 -0.5,1.234 -0.5,2.109 0,0.888 0.167,1.59 0.5,2.106 0.333,0.517 0.77,0.774 1.31,0.774 0.557,0 0.999,-0.251 1.325,-0.753 0.326,-0.502 0.49,-1.199 0.49,-2.09 0,-0.929 -0.158,-1.652 -0.475,-2.169 -0.317,-0.516 -0.75,-0.774 -1.3,-0.774 z" id="shrs-icon-outlook-path25" inkscape:connector-curvature="0"></path>
            <path d="m 3.441,13.563 v 20.375 l 15.5,3.25 V 10.563 l -15.5,3 z m 10.372,13.632 c -0.655,0.862 -1.509,1.294 -2.563,1.294 -1.027,0 -1.863,-0.418 -2.51,-1.253 C 8.094,26.4 7.77,25.312 7.77,23.97 c 0,-1.417 0.328,-2.563 0.985,-3.438 0.657,-0.875 1.527,-1.313 2.61,-1.313 1.023,0 1.851,0.418 2.482,1.256 0.632,0.838 0.948,1.942 0.948,3.313 10e-4,1.409 -0.327,2.545 -0.982,3.407 z" id="shrs-icon-outlook-path27" inkscape:connector-curvature="0"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-paper-clip">
        <path d="M44,88A23.16,23.16,0,0,1,20.87,64.87V16.78h5.44V64.87a17.69,17.69,0,0,0,35.38,0V16.78a11.34,11.34,0,0,0-22.68,0V64.87a5,5,0,0,0,10,0V16.78h5.44V64.87a10.43,10.43,0,0,1-20.87,0V16.78a16.78,16.78,0,0,1,33.57,0V64.87A23.16,23.16,0,0,1,44,88Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-pencil">
        <path d="M58,11,68.29.69a2.35,2.35,0,0,1,3.33,0L87.31,16.38a2.35,2.35,0,0,1,0,3.33L77,30a2.35,2.35,0,0,1-3.33,0L58,14.36A2.35,2.35,0,0,1,58,11Zm-1.66,1.66"></path>
        <path d="M4.09,86.83l15.5-4.46a2.35,2.35,0,0,0,1-3.92l-11-11a2.35,2.35,0,0,0-3.92,1L1.18,83.91A2.35,2.35,0,0,0,4.09,86.83ZM0,88"></path>
        <path d="M27.15,76.54,11.46,60.85a2.35,2.35,0,0,1,0-3.33L50.4,18.58a2.35,2.35,0,0,1,3.33,0L69.42,34.28a2.35,2.35,0,0,1,0,3.33L30.48,76.54A2.35,2.35,0,0,1,27.15,76.54Zm1.66,1.66"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-person">
        <path d="M75,82.54A70.61,70.61,0,0,1,64.68,85.4,102.31,102.31,0,0,1,44,88a102.31,102.31,0,0,1-20.68-2.6A70.61,70.61,0,0,1,13,82.54c-2.68-1-4.27-1.41-4.18-4.62a83.63,83.63,0,0,1,1.36-12.41c0.88-5,2-12.23,6.34-15.49,2.46-1.85,5.72-2.46,8.58-3.61a34.48,34.48,0,0,0,3.83-1.67A19.82,19.82,0,0,0,44,51.35a19.82,19.82,0,0,0,15.09-6.6,34.48,34.48,0,0,0,3.83,1.67C65.78,47.56,69,48.18,71.5,50c4.36,3.26,5.46,10.52,6.34,15.49A83.63,83.63,0,0,1,79.2,77.92C79.29,81.14,77.7,81.58,75,82.54ZM44,46.86c-13,0-17.69-13-18.92-23.72C23.58,9.94,29.79,0,44,0S64.42,9.94,62.92,23.14C61.69,33.84,57,46.86,44,46.86Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-phone-book">
        <path d="M18.6,13.8a3.47,3.47,0,0,1-3.3,3.6H8.2a3.47,3.47,0,0,1-3.3-3.6h0a3.47,3.47,0,0,1,3.3-3.6h7.1a3.47,3.47,0,0,1,3.3,3.6h0Zm0,20.1a3.47,3.47,0,0,0-3.3-3.6H8.2a3.47,3.47,0,0,0-3.3,3.6h0a3.47,3.47,0,0,0,3.3,3.6h7.1a3.47,3.47,0,0,0,3.3-3.6h0Zm0,20.2a3.47,3.47,0,0,0-3.3-3.6H8.2a3.47,3.47,0,0,0-3.3,3.6h0a3.47,3.47,0,0,0,3.3,3.6h7.1a3.47,3.47,0,0,0,3.3-3.6h0Zm0,20.1a3.47,3.47,0,0,0-3.3-3.6H8.2a3.47,3.47,0,0,0-3.3,3.6h0a3.47,3.47,0,0,0,3.3,3.6h7.1a3.47,3.47,0,0,0,3.3-3.6h0ZM83.1,4V84.1A3.82,3.82,0,0,1,79.3,88H15.5a3.88,3.88,0,0,1-3.8-3.9V80.6h3.5c3.2,0,5.8-2.9,5.8-6.4s-2.6-6.5-5.8-6.5H11.7V60.5h3.5c3.2,0,5.8-2.9,5.8-6.5s-2.6-6.5-5.8-6.5H11.7V40.3h3.5c3.2,0,5.8-2.9,5.8-6.5s-2.6-6.5-5.8-6.5H11.7V20.1h3.5c3.2,0,5.8-2.9,5.8-6.5s-2.6-6.4-5.8-6.4H11.7V4a3.91,3.91,0,0,1,3.8-4H79.3A3.85,3.85,0,0,1,83.1,4ZM40.5,33.4c0-1.2.8-2.4,1.7-2.3s3.6,2.1,4.5,2.6c2.5,1.4,6.5-9,4.6-10.3-1.5-.9-2.8-2.2-4.4-2.9-0.2-.1-0.5-0.1-0.8-0.2-6.5-1.5-13.2,4.4-13.8,13.2-0.2,3.4-.4,6.8-0.5,10.2h0v0.6h0c0,3.4.2,6.7,0.5,10.1,0.7,8.8,7.3,14.8,13.8,13.2a2,2,0,0,0,.8-0.1c1.6-.7,2.9-2,4.4-2.9,1.9-1.2-2.2-11.6-4.6-10.3-0.9.5-3.5,2.4-4.5,2.6s-1.7-1.1-1.7-2.3c-0.2-3.1,0-7,0-10.3h0V43.7h0C40.5,40.4,40.3,36.5,40.5,33.4ZM56.7,52.8a12.87,12.87,0,0,0,0-17.7l-2.5,2.6a9.13,9.13,0,0,1,0,12.5Zm4.4,4.7a19.78,19.78,0,0,0,0-27l-2.5,2.6a16,16,0,0,1,0,21.8ZM65.4,26l-2.5,2.6a22.56,22.56,0,0,1,0,30.8L65.4,62A26.3,26.3,0,0,0,65.4,26Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-plus">
        <g>
            <path d="M85.25,33H55V2.75A2.75,2.75,0,0,0,52.25,0H35.75A2.75,2.75,0,0,0,33,2.75V33H2.75A2.75,2.75,0,0,0,0,35.75v16.5A2.75,2.75,0,0,0,2.75,55H33V85.25A2.75,2.75,0,0,0,35.75,88h16.5A2.75,2.75,0,0,0,55,85.25V55H85.25A2.75,2.75,0,0,0,88,52.25V35.75A2.75,2.75,0,0,0,85.25,33Z"></path>
        </g>
    </symbol>
    
    <symbol viewBox="0 0 88 88" id="shrs-icon-repairtools">
        <g>
            <path d="M2.42,73.95A8.24,8.24,0,0,0,14.09,85.59l27-27.07L29.49,46.87Z"></path>
            <polygon points="70.05 23.77 81.83 17.68 88 5.74 82.26 0 70.32 6.17 64.23 17.95 49.88 32.3 55.7 38.13 70.05 23.77"></polygon>
            <path d="M72.63,55.06l-1.1-.11A16.36,16.36,0,0,0,65.81,56L32,22.19a16.18,16.18,0,0,0,1.07-5.71l-.1-1.1A16.35,16.35,0,0,0,9.5,1.63l10.95,11a5,5,0,0,1,1.23,2,5.46,5.46,0,0,1-5.1,7.4,5,5,0,0,1-1.9-.37,5.19,5.19,0,0,1-2-1.24L1.74,9.42A16.1,16.1,0,0,0,.08,16.47a16.46,16.46,0,0,0,15.37,16.4l1.13.11a16.73,16.73,0,0,0,5.69-1.07L56.1,65.73A16.57,16.57,0,0,0,55,71.42l.1,1.13a16.46,16.46,0,0,0,16.4,15.38,16.08,16.08,0,0,0,7.05-1.66L67.64,75.31a5.73,5.73,0,0,1-1.26-2A5.49,5.49,0,0,1,71.53,66a5.8,5.8,0,0,1,3.87,1.61l11,10.95A16.35,16.35,0,0,0,72.63,55.06Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 1000 1000" id="shrs-icon-roll-back">
        <g>
            <path d="M918.2,431.8C872,385.5,810.5,360,745,360c0,0,0,0,0,0H129.5l150.3-150.3c13.7-13.7,13.7-35.8,0-49.5C272.9,153.4,264,150,255,150s-17.9,3.4-24.7,10.3l-210,210c-13.7,13.7-13.7,35.8,0,49.5l210,210c13.7,13.7,35.8,13.7,49.5,0c13.7-13.7,13.7-35.8,0-49.5L129.5,430H745c96.5,0,175,78.5,175,175c0,96.5-78.5,175-175,175H465c-19.3,0-35,15.7-35,35s15.7,35,35,35h280c65.4,0,127-25.5,173.2-71.8C964.5,732,990,670.4,990,605C990,539.6,964.5,478,918.2,431.8z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-rotationleft">
        <path d="M44,5.5A43.85,43.85,0,0,0,12.89,18.39L0,5.5v33H33L20.67,26.17A33,33,0,1,1,65.83,74.25L73.1,82.5A44,44,0,0,0,44,5.5Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-rotationright">
        <g>
            <path d="M0,49.5c0,13.1,5.8,24.9,14.9,33l7.3-8.3C15.3,68.2,11,59.4,11,49.5c0-18.2,14.8-33,33-33c9.1,0,17.4,3.7,23.3,9.7L55,38.5 h33v-33L75.1,18.4c-8-8-19-12.9-31.1-12.9C19.7,5.5,0,25.2,0,49.5z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-school-holidays">
        <path d="M87.1,72.88L49.85,8.37a6.75,6.75,0,0,0-11.69,0L0.9,72.88A6.75,6.75,0,0,0,6.75,83h74.5A6.75,6.75,0,0,0,87.1,72.88ZM6.75,76.25L44,11.75l37.25,64.5H6.75Z"></path>
        <path d="M42.28,35.36a4.49,4.49,0,1,0,5.27,3.52A4.49,4.49,0,0,0,42.28,35.36Z"></path>
        <path d="M41,44.34l-0.12-.09a3.87,3.87,0,0,0-3.34-.73l-6.16,1.13a2,2,0,0,0-.52.17L26.37,47a2,2,0,1,0,1.79,3.69l4.24-2.06,1.88-.35-1.45,6.82a3.91,3.91,0,0,0-.06.41l-2.62,6.75L24.62,66.9a2.44,2.44,0,0,0,3.13,3.74l6-5a2.44,2.44,0,0,0,.71-1l1.87-4.81h0.12L40.13,63l1,6.88A2.44,2.44,0,0,0,43.59,72L44,72a2.44,2.44,0,0,0,2-2.78l-1.18-7.76a2.44,2.44,0,0,0-.8-1.47l-3.57-3.12,0-.09,1.39-6.54,1.85,1.44a2,2,0,0,0,.65.34l5.27,1.65a2,2,0,0,0,1.22-3.91l-4.91-1.54Z"></path>
        <path d="M61.68,54.12A2.73,2.73,0,1,0,58.48,52,2.73,2.73,0,0,0,61.68,54.12Z"></path>
        <path d="M50.38,57.51a1.24,1.24,0,0,0,1.66.58l2.58-1.25,1.14-.21-0.88,4.14a2.39,2.39,0,0,0,0,.25l-1.59,4.1-3.36,2.82a1.48,1.48,0,0,0,1.9,2.27l3.65-3.06a1.48,1.48,0,0,0,.43-0.6L57,63.62h0.07l2.23,2L60,69.76A1.48,1.48,0,0,0,61.42,71l0.22,0a1.48,1.48,0,0,0,1.24-1.69L62.17,64.6a1.48,1.48,0,0,0-.49-0.89l-2.17-1.9V61.76l0.84-4,1.12,0.88a1.24,1.24,0,0,0,.39.21l3.2,1a1.24,1.24,0,1,0,.74-2.38l-3-.94-3-2.33,0,0a2.36,2.36,0,0,0-2.07-.47L54,54.42a1.24,1.24,0,0,0-.32.1L51,55.85A1.24,1.24,0,0,0,50.38,57.51Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-search">
        <path d="M85.6,85.55a8.33,8.33,0,0,1-11.7,0L53.7,65.35a38.41,38.41,0,0,0,11.7-11.7l20.2,20.2A8.33,8.33,0,0,1,85.6,85.55ZM33,66.05a33,33,0,1,1,33-33A33,33,0,0,1,33,66.05Zm0-57.8A24.7,24.7,0,1,0,57.8,33,24.75,24.75,0,0,0,33,8.25Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-shutdown">
        <path d="M58.48,6.89V18.08a31.59,31.59,0,1,1-31.36,1.4V7.89A41.82,41.82,0,1,0,58.48,6.89Z"></path>
        <path d="M43.2,51.54c4.59,0,5.09-2.7,5.09-6.09V6.09C48.3,2.7,47.8,0,43.2,0s-5.09,2.7-5.09,6.09V45.45C38.11,48.84,38.61,51.54,43.2,51.54Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-skills">
        <path d="M85.71,32.5V55.89L88,63.45H81.67L83.87,56V33.43L48.53,48.72a14.22,14.22,0,0,1-9.54.13L2,34.26c-2.64-1.06-2.64-2.73,0-3.78L39,16.24a15.66,15.66,0,0,1,9.63-.09l36,13.1c2.24,0.84,2.59,2.2,1.1,3.25h0ZM38.06,52.64a16.69,16.69,0,0,0,11.43-.18l13.63-5.89h8.26V56a8.68,8.68,0,0,1-4.7,7.34L47.91,71.71a13.1,13.1,0,0,1-9.41,0L19.91,63.41A8.8,8.8,0,0,1,15.2,56V46.57h7.52Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-smiley-happy">
        <path d="M55,16.83a16.47,16.47,0,1,1,32.94,0,4.73,4.73,0,1,1-9.46,0,7,7,0,1,0-14,0A4.73,4.73,0,1,1,55,16.83ZM2.53,16.78A16.68,16.68,0,0,1,19,0,16.63,16.63,0,0,1,35.47,16.78a4.73,4.73,0,1,1-9.46,0,7,7,0,1,0-14,0A4.73,4.73,0,1,1,2.53,16.78Zm-2.09,43a5.77,5.77,0,0,1,3.08-7.53,5,5,0,0,1,2.14-.44,5.72,5.72,0,0,1,5.28,3.52C16.17,68.14,29.2,76.45,44.11,76.45c14.57,0,27.5-8.3,33-21.17a5.67,5.67,0,0,1,7.48-3,5.87,5.87,0,0,1,3,7.59A46.94,46.94,0,0,1,44.11,88C24.58,88,7.48,76.89.44,59.73Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-smiley-sad">
        <path d="M84.48,87.47a5.08,5.08,0,0,1-2.14.49,5.76,5.76,0,0,1-5.28-3.74c-5.28-13.47-18.3-22.15-33.2-22.15a35.45,35.45,0,0,0-33,22.15,5.54,5.54,0,0,1-7.47,3.13,6.21,6.21,0,0,1-3-7.91,46.91,46.91,0,0,1,87.11.16A6.12,6.12,0,0,1,84.48,87.47ZM71.73,18.06a8.79,8.79,0,0,1-8.57-9A8.58,8.58,0,1,1,80.31,9,8.79,8.79,0,0,1,71.73,18.06Zm-55.46,0A8.85,8.85,0,0,1,7.59,9a8.85,8.85,0,0,1,8.68-9,8.8,8.8,0,0,1,8.63,9A8.8,8.8,0,0,1,16.28,18.06Z"></path>
    </symbol>
    <symbol viewBox="0 0 12.25 17.2" id="shrs-icon-sorting-asc">
        <polygon points="6.17 0 11.18 7.05 1.16 7.05 6.17 0"></polygon>
        <path d="M9.25,10.4,6.17,14.72,3.1,10.4H9.25m1.94-1h-10l5,7,5-7Z"></path>
    </symbol>
    <symbol viewBox="0 0 12.25 17.2" id="shrs-icon-sorting-desc">
        <path d="M6.17,1.73,9.25,6H3.1L6.17,1.73M6.17,0l-5,7h10l-5-7Z"></path>
        <polygon points="6.17 16.45 1.16 9.4 11.18 9.4 6.17 16.45"></polygon>
    </symbol>
    <symbol viewBox="0 0 12.25 17.2" id="shrs-icon-sorting-neutral">
        <path d="M6.17,1.73,9.25,6H3.1L6.17,1.73M6.17,0l-5,7h10l-5-7Z"></path>
        <path d="M9.25,10.4,6.17,14.72,3.1,10.4H9.25m1.94-1h-10l5,7,5-7Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-threedots">
        <path d="M44,0A10.15,10.15,0,1,1,33.85,10.15,10.15,10.15,0,0,1,44,0Zm0,33.85A10.15,10.15,0,1,1,33.85,44,10.15,10.15,0,0,1,44,33.85Zm0,33.85A10.15,10.15,0,1,1,33.85,77.85,10.15,10.15,0,0,1,44,67.69Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-time">
        <g>
            <path d="M44,0C19.7,0,0,19.7,0,44s19.7,44,44,44s44-19.7,44-44C88,19.7,68.3,0,44,0z M44,83.3C22.3,83.3,4.7,65.7,4.7,44
		S22.3,4.7,44,4.7S83.3,22.3,83.3,44C83.3,65.7,65.7,83.3,44,83.3z"></path>
            <path d="M74.1,41.7H46.3V14.4c-0.1-1.3-1.2-2.3-2.5-2.2c-1.2,0.1-2.1,1-2.2,2.2V44c0,1.3,1,2.3,2.3,2.3h30.1
		c1.3-0.1,2.3-1.2,2.2-2.5C76.2,42.7,75.2,41.7,74.1,41.7L74.1,41.7z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-training">
        <path d="M84.3,16H79.73V62.26A2.79,2.79,0,0,1,76.91,65H49.54l9.81,9.59a2.63,2.63,0,0,1,0,3.83,2.83,2.83,0,0,1-2,.79,2.73,2.73,0,0,1-1.94-.79L43.65,66.92,32.56,77.75a2.82,2.82,0,0,1-3.92,0,2.68,2.68,0,0,1,0-3.87L37.75,65H11.09a2.79,2.79,0,0,1-2.82-2.73V16.06H3.7A3.68,3.68,0,0,1,0,12.41,3.64,3.64,0,0,1,3.7,8.8H84.3A3.61,3.61,0,1,1,84.3,16ZM74.14,16H13.86V59.53H74.14V16ZM66,48.4H22v-22H66v22Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-trash-can">
        <g>
            <path d="M82.44,18.75A8.22,8.22,0,0,0,74.25,11H66V8.25h0A8.25,8.25,0,0,0,57.75,0H30.25A8.25,8.25,0,0,0,22,8.25h0V11H13.75a8.22,8.22,0,0,0-8.2,7.74H5.5v6a5.5,5.5,0,0,0,5.5,5.5h0V77A11,11,0,0,0,22,88H66A11,11,0,0,0,77,77V30.25h0a5.5,5.5,0,0,0,5.5-5.5v-6ZM27.5,8.25A2.75,2.75,0,0,1,30.24,5.5H57.75A2.75,2.75,0,0,1,60.5,8.25h0V11h-33V8.25ZM71.5,77A5.5,5.5,0,0,1,66,82.5H22A5.5,5.5,0,0,1,16.5,77V30.25h55ZM77,22v2.75H11v-5.5a2.75,2.75,0,0,1,2.74-2.75H74.25A2.75,2.75,0,0,1,77,19.24h0ZM24.75,77h5.5A2.75,2.75,0,0,0,33,74.25V38.5a2.75,2.75,0,0,0-2.74-2.75H24.75A2.75,2.75,0,0,0,22,38.49h0V74.25A2.75,2.75,0,0,0,24.75,77Zm0-38.5h5.5V74.25h-5.5ZM41.25,77h5.5a2.75,2.75,0,0,0,2.75-2.75V38.5a2.75,2.75,0,0,0-2.74-2.75H41.25a2.75,2.75,0,0,0-2.75,2.74h0V74.25A2.75,2.75,0,0,0,41.25,77Zm0-38.5h5.5V74.25h-5.5ZM57.75,77h5.5A2.75,2.75,0,0,0,66,74.25V38.5a2.75,2.75,0,0,0-2.74-2.75H57.75A2.75,2.75,0,0,0,55,38.49h0V74.25A2.75,2.75,0,0,0,57.75,77Zm0-38.5h5.5V74.25h-5.5Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-triangle-bottom">
        <path d="M44,75.2L0,12.8H88Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-triangle-left">
        <path d="M12.8,44L75.2,0V88Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-triangle-right">
        <path d="M75.2,44L12.8,88V0Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-triangle-top">
        <path d="M44,12.8L88,75.2H0Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-validated">
        <path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0ZM63.75,37.31L45.51,55.55c-7.55,7.55-14,0-14,0l-7.31-7.31a4.37,4.37,0,0,1,.11-6.19l0.73-.73a4.3,4.3,0,0,1,6.13-.06l7.31,7.31L56.77,30.33a4.35,4.35,0,0,1,6.13.06l0.73,0.73A4.37,4.37,0,0,1,63.75,37.31Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-warning-triangle">
        <rect class="shrs-icon__white-mask" x="37.29" y="21.63" width="13.52" height="51.18"></rect>
        <path d="M86.57,66.52,50,11.89a7.11,7.11,0,0,0-5.79-3.1,6.86,6.86,0,0,0-5.79,3.1l-37,54.63a8.4,8.4,0,0,0-.5,8.39,7.93,7.93,0,0,0,7,4.29h72.2a8,8,0,0,0,7-4.29A8.16,8.16,0,0,0,86.57,66.52ZM44,72.81a5.67,5.67,0,0,1-5.59-5.69,5.61,5.61,0,0,1,5.53-5.69H44a5.69,5.69,0,0,1,0,11.38ZM39,25.47A6.77,6.77,0,0,1,48.56,25q.23.21.44.44a7.06,7.06,0,0,1,1.8,5.19L48.6,56.53a10,10,0,0,0-4.59-1,11.85,11.85,0,0,0-4.59,1l-2.1-25.87A7,7,0,0,1,39,25.47Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-warning">
        <path d="M35.77,59.08a21.05,21.05,0,0,1,8.16-1.77,17.77,17.77,0,0,1,8.16,1.77l3.9-46A12.54,12.54,0,0,0,52.8,3.9a12,12,0,0,0-17.74,0,12.4,12.4,0,0,0-3,9.23Z"></path>
        <path d="M43.93,67.77A10,10,0,0,0,34,77.89,9.94,9.94,0,1,0,43.93,67.77Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-workinprogress">
        <g>
            <path d="M62.45,24.52a7,7,0,1,0-.87-9.9A7,7,0,0,0,62.45,24.52Z"></path>
            <path d="M79,32.57a7.56,7.56,0,0,0-1.31.11A7.45,7.45,0,0,0,79,47.47a7.7,7.7,0,0,0,1.31-.11A7.45,7.45,0,0,0,79,32.57Z"></path>
            <path d="M78.77,56.94a7.88,7.88,0,1,0,2.88,10.77A7.86,7.86,0,0,0,78.77,56.94Z"></path>
            <path d="M56.36,71a8.3,8.3,0,1,0,7.81,5.47A8.31,8.31,0,0,0,56.36,71Z"></path>
            <path d="M35.23,71.05a8.75,8.75,0,1,0,4.93,4.52A8.73,8.73,0,0,0,35.23,71.05Z"></path>
            <path d="M21.71,59.18A9.16,9.16,0,0,0,9.2,55.83,9.16,9.16,0,0,0,5.85,68.35a9.16,9.16,0,1,0,15.87-9.16Z"></path>
            <path d="M9.61,49.61a9.59,9.59,0,0,0,1.65-19,9.82,9.82,0,0,0-1.68-.15,9.59,9.59,0,0,0-1.65,19A9.67,9.67,0,0,0,9.61,49.61Z"></path>
            <path d="M21.65,29.16A10,10,0,1,0,14,25.58,10,10,0,0,0,21.65,29.16Z"></path>
            <circle cx="44.3" cy="10.89" r="10.89"></circle>
        </g>
    </symbol>

     <symbol viewBox="0 0 88 88" id="shrs-icon-rapport">
     <path d="M 3.1394,0 C 1.5321154,-0.01090936 0.22037768,1.2833387 0.20971303,2.890625 v 82.21875 C 0.22037768,86.716661 1.5321154,88.010909 3.1394,88 h 60.839844 c 1.607285,0.01091 2.919022,-1.283339 2.929687,-2.890625 V 78.614 c -1.766684,0.466102 -3.31397,0.754608 -5.859375,0.7 v 2.906703 h -55 V 5.7792969 h 55 v 18.9414061 2.195289 c 1.953125,0 3.626589,-0.05253 5.859375,0.600016 V 2.890625 C 66.898266,1.2833387 65.586529,-0.01090938 63.979244,0 Z" id="path11591" inkscape:connector-curvature="0" sodipodi:nodetypes="cccccccccccccccccc"></path>
  <path d="m 23.371001,16.4857 -12.656384,8.420091 v 6.309621 l 12.4608,-8.20353 c 0.944627,-0.654235 1.140208,-7.180417 0.195584,-6.526182 z" id="path11597" inkscape:connector-curvature="0" style="stroke-width:0.60010988" inkscape:transform-center-x="-0.945" inkscape:transform-center-y="-5.2266307" sodipodi:nodetypes="ccccc"></path>
  <g id="g816" transform="matrix(-0.4548236,0.28290265,0.28290265,0.4548236,71.110829,15.758833)">
    <path inkscape:connector-curvature="0" id="path815" d="m 53.582324,10.193463 c -14.652697,0.0039 -26.268294,8.551992 -32.151867,16.814396 l 31.86937,23.217287 -34.050489,21.179318 c 7.27525,12.081095 20.230438,19.297242 34.332986,19.313999 22.236299,-1.51e-4 40.262342,-18.026201 40.262484,-40.2625 C 93.844666,28.219664 75.818623,10.193614 53.582324,10.193463 Z M 13.342366,50.006816 c -0.0083,0.149673 -0.01585,0.299391 -0.02252,0.449147 0.0057,0.205449 0.01291,0.410852 0.02173,0.61619 0.0017,-0.355129 0.0017,-0.710216 0.0017,-1.065337 z" style="fill-opacity:1;stroke:none;stroke-width:0.15218298" sodipodi:nodetypes="cccccccccccc"></path>
    <path sodipodi:nodetypes="cccc" inkscape:connector-curvature="0" id="path832-8-5" d="M 6.8848276,27.16591 39.732172,49.72902 6.5629916,70.616202 C -1.0516945,56.120135 1.3397775,40.625699 6.8848276,27.16591 Z" style="fill-opacity:1;stroke-width:1.84125185"></path>
  </g>
  <path inkscape:connector-curvature="0" id="path12202" d="M 31.53494,44.6 H 14.407463 c -2.547697,0 -2.547697,4.4 0,4.4 H 31.53494 c 2.547696,0 2.547696,-4.4 0,-4.4 z" sodipodi:nodetypes="sssss" style="stroke-width:0.93195134"></path>
  <path inkscape:connector-curvature="0" id="path12204" d="M 31.53494,56.42 H 14.407463 c -1.066882,-0.0056 -1.932983,0.991628 -1.928144,2.22 0.02803,1.205898 0.880444,2.169661 1.928144,2.18 H 31.53494 c 1.060953,-0.01086 1.918712,-0.998454 1.928144,-2.22 -0.01429,-1.212723 -0.874769,-2.1856 -1.928144,-2.18 z" sodipodi:nodetypes="ccccccc" style="stroke-width:0.93195134"></path>
  <path sodipodi:nodetypes="ccccccc" d="M 31.53494,68.24 H 14.407463 c -1.066882,-0.0056 -1.932983,0.991628 -1.928144,2.22 0.02803,1.205898 0.880444,2.169661 1.928144,2.18 H 31.53494 c 1.060953,-0.01086 1.918712,-0.998454 1.928144,-2.22 -0.01429,-1.212723 -0.874769,-2.1856 -1.928144,-2.18 z" id="path12208" inkscape:connector-curvature="0" style="stroke-width:0.93195134"></path>
  <path inkscape:transform-center-y="-1.3585687" inkscape:transform-center-x="-5.2431692" style="stroke-width:0.63922304" inkscape:connector-curvature="0" id="path12210" d="M 37.896758,24.770374 23.686197,16.448723 c 0,0 -2.685679,5.092253 -1.580668,5.740313 l 13.977202,8.139087 z" sodipodi:nodetypes="ccccc"></path>
  <path inkscape:transform-center-y="-5.6905842" inkscape:transform-center-x="-1.1245494" style="stroke-width:0.74595928" inkscape:connector-curvature="0" id="path12212" d="m 55.543491,8.885866 -19.506216,14.839863 0.04546,6.602394 19.45999,-14.307968 z" sodipodi:nodetypes="ccccc"></path>
     
     </symbol>
     
    <symbol viewBox="0 0 88 88" id="shrs-icon-zoom-in">
        <g>
            <path d="M85.6,85.55a8.33,8.33,0,0,1-11.7,0L53.7,65.35a38.41,38.41,0,0,0,11.7-11.7l20.2,20.2A8.74,8.74,0,0,1,85.6,85.55ZM33,66.05a33,33,0,1,1,33-33A33,33,0,0,1,33,66.05Zm0-57.8a24.75,24.75,0,1,0,.05,0Z"></path>
            <path d="M48,28.95H37v-11a1,1,0,0,0-1-1H30a1,1,0,0,0-1,1v11H18a1,1,0,0,0-1,1v6a1,1,0,0,0,1,1H29v11a1,1,0,0,0,1,1h6a1,1,0,0,0,1-1v-11H48a1,1,0,0,0,1-1v-6A1,1,0,0,0,48,28.95Z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 98 104" id="shrs-icon-filter">
        <path d="M84.2,10L84.2,10l-36.9,0l-35.6,0c-3.5,0-6.3,2.8-6.3,6.3v2.2c0,2.1,1,4.2,2.6,5.5l28.2,22.7V92c0,3.4,2.6,6,5.8,6
c1.5,0,3-0.6,4.1-1.7l11.1-10.5c1.4-1.3,2.3-3.3,2.3-5.3V46.9l28.1-21.6c1.7-1.3,2.7-3.4,2.7-5.6v-3.4C90.5,12.9,87.7,10,84.2,10z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-add">
        <path id="add" d="M48.44,39.58V16.39a3.93,3.93,0,0,0-4-3.86H43.5a3.91,3.91,0,0,0-3.93,3.86V39.58H16.39a3.87,3.87,0,0,0-3.86,3.94v.93a3.93,3.93,0,0,0,3.86,4H39.57V71.61a3.87,3.87,0,0,0,3.93,3.86h.93a3.93,3.93,0,0,0,4-3.86V48.45H71.61a3.93,3.93,0,0,0,3.86-4v-.93a3.91,3.91,0,0,0-3.86-3.94Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-anomaly">
        <path style="fill:#CF022B;" id="alerte" d="M80.29,63.19,49.13,16.63A6.06,6.06,0,0,0,44.2,14a5.85,5.85,0,0,0-4.94,2.63L7.68,63.19a7.17,7.17,0,0,0-.43,7.15,6.76,6.76,0,0,0,6,3.66H74.75a6.85,6.85,0,0,0,6-3.66A7,7,0,0,0,80.29,63.19ZM44,68.56a4.84,4.84,0,0,1-4.77-4.86A4.78,4.78,0,0,1,44,58.85a4.86,4.86,0,0,1,0,9.71ZM39.77,28.21a5.78,5.78,0,0,1,8.51,0,6,6,0,0,1,1.53,4.43l-1.87,22A8.59,8.59,0,0,0,44,53.83a10.09,10.09,0,0,0-3.92.85l-1.79-22A6,6,0,0,1,39.77,28.21Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-alert1">
        <path id="alerte" d="M80.29,63.19,49.13,16.63A6.06,6.06,0,0,0,44.2,14a5.85,5.85,0,0,0-4.94,2.63L7.68,63.19a7.17,7.17,0,0,0-.43,7.15,6.76,6.76,0,0,0,6,3.66H74.75a6.85,6.85,0,0,0,6-3.66A7,7,0,0,0,80.29,63.19ZM44,68.56a4.84,4.84,0,0,1-4.77-4.86A4.78,4.78,0,0,1,44,58.85a4.86,4.86,0,0,1,0,9.71ZM39.77,28.21a5.78,5.78,0,0,1,8.51,0,6,6,0,0,1,1.53,4.43l-1.87,22A8.59,8.59,0,0,0,44,53.83a10.09,10.09,0,0,0-3.92.85l-1.79-22A6,6,0,0,1,39.77,28.21Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-alert2">
        <path d="M40.05,54.68A10.13,10.13,0,0,1,44,53.83a8.59,8.59,0,0,1,3.91.85l1.88-22a6,6,0,0,0-1.54-4.43,5.78,5.78,0,0,0-8.51,0,6,6,0,0,0-1.45,4.43Z"></path>
        <ellipse cx="43.97" cy="63.7" rx="4.77" ry="4.85"></ellipse>
        <path d="M44.2,18.26a1.83,1.83,0,0,1,1.41.77L76.74,65.56l0,0,0,0a2.68,2.68,0,0,1,.13,2.69,2.58,2.58,0,0,1-2.19,1.39H13.21a2.47,2.47,0,0,1-2.16-1.33,2.92,2.92,0,0,1,.16-2.82L42.79,19l0,0,0,0a1.61,1.61,0,0,1,1.37-.7m0-4.26a5.85,5.85,0,0,0-4.94,2.63L7.68,63.19a7.17,7.17,0,0,0-.43,7.15,6.76,6.76,0,0,0,6,3.66H74.75a6.85,6.85,0,0,0,6-3.66,7,7,0,0,0-.42-7.15L49.13,16.63A6.06,6.06,0,0,0,44.2,14Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-arrows-double">
        <path d="M16.27,48.68H6.5V72.11l6.94-6.38A37.49,37.49,0,0,0,81.2,48.68H71.73A28.13,28.13,0,0,1,20.41,59.32L32,48.68Zm0,0"></path>
        <path d="M44,6.5A37.47,37.47,0,0,0,6.8,39.32h9.47A28.14,28.14,0,0,1,68,29.36l-10,10H81.5V15.89l-6.71,6.7A37.47,37.47,0,0,0,44,6.5Zm0,0"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-arrow-simple">
        <path id="fleche-droite" d="M47.66,13.62a4,4,0,0,1,5.34,0L80.51,41.33c.********.4.59l.19.2c0,.*******.4s0,.19.2.39v2.38c0,.19,0,.19-.2.39,0,.2-.2.2-.2.4a.19.19,0,0,1-.19.2c-.2.19-.2.39-.4.59L52.81,74.38a3.72,3.72,0,0,1-5.15,0,4,4,0,0,1,0-5.35L69,47.46h-59A3.59,3.59,0,0,1,6.5,43.7a3.54,3.54,0,0,1,3.56-3.56H69l-21-21.37A3.39,3.39,0,0,1,47.66,13.62Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-attached">
        <path d="M44,81.5A19.74,19.74,0,0,1,24.28,61.78v-41h4.64v41a15.08,15.08,0,0,0,30.16,0v-41a9.67,9.67,0,0,0-19.33,0v41a4.25,4.25,0,1,0,8.5,0v-41h4.64v41a8.89,8.89,0,1,1-17.78,0v-41a14.31,14.31,0,0,1,28.61,0v41A19.74,19.74,0,0,1,44,81.5Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-comment">
        <path d="M77.66,9.66H10.31A3.81,3.81,0,0,0,6.5,13.45V58.21A3.81,3.81,0,0,0,10.31,62h6L11.81,74.47a2.92,2.92,0,0,0,.24,2.8,2.56,2.56,0,0,0,2.1,1.07A3.16,3.16,0,0,0,16,77.7L38.44,62.48A3.65,3.65,0,0,1,39.93,62H77.66a3.83,3.83,0,0,0,3.84-3.81V13.45A3.83,3.83,0,0,0,77.66,9.66ZM22.28,27.23H55.1a2.27,2.27,0,1,1,0,4.53H22.28a2.27,2.27,0,0,1,0-4.53ZM64.86,46.59H22.28a2.28,2.28,0,0,1,0-4.56H64.86a2.28,2.28,0,1,1,0,4.56Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-comment4">
        <path d="M 77.66,9.66 H 10.31 C 8.2135809,9.6599711 6.5110048,11.35361 6.5,13.45 v 44.76 c 0.011005,2.09639 1.7135809,3.790029 3.81,3.79 h 6 l -4.5,12.47 c -0.403922,0.912991 -0.313401,1.969065 0.24,2.8 0.484429,0.676849 1.267677,1.075933 2.1,1.07 0.668865,-0.01213 1.316624,-0.236221 1.85,-0.64 L 38.44,62.48 c 0.456108,-0.265875 0.964464,-0.429641 1.49,-0.48 h 37.73 c 2.111365,0.0055 3.828982,-1.698657 3.84,-3.81 V 13.45 C 81.478037,11.346484 79.763623,9.6543928 77.66,9.66 Z M 72.912242,55 H 36.356121 L 22.060311,65.550847 26.07806,55 H 15.8 c 0,0 -2.3,0.25496 -2.3,-2 V 18.136104 C 13.5,17.253052 13.90339,16.66 14.71,16.66 h 58.268644 c 0.924792,0 1.484427,0.48706 1.521356,1.746919 V 53 c 0,0 0.137805,1.987469 -1.587758,2 z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-crown">
        <path d="M60.4,41.65,44,20.56,27.59,41.65,6.5,20.56l4.69,46.88H76.81L81.5,20.56Zm0,0"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche_demande-attestation">
        <path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z"></path>
        <path d="M44,7.5A36.51,36.51,0,0,0,30,77.73v-20l7.83,6.64V46.82a11.79,11.79,0,0,0,2.31-16.64H52.73V44.37a1.7,1.7,0,0,0,1.7,1.7H68.62V70.94A36.5,36.5,0,0,0,44,7.5ZM35.8,60.16,30,55l-5.76,5.15V45A9.1,9.1,0,0,0,35.8,45ZM30,45.59a8.23,8.23,0,1,1,8.23-8.23A8.24,8.24,0,0,1,30,45.59Zm10.66-8.23a10.62,10.62,0,0,1-4.09,8.38V44.66a9.84,9.84,0,1,0-13.14,0v1.08a10.66,10.66,0,1,1,17.23-8.38ZM56.14,43.8A1.14,1.14,0,0,1,55,42.66V30.18L68.62,43.8Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-eye">
        <g id="voir">
            <path d="M44,19.5A41.18,41.18,0,0,0,6.65,43.32a1.62,1.62,0,0,0,0,1.36,41.19,41.19,0,0,0,74.7,0,1.62,1.62,0,0,0,0-1.36A41.18,41.18,0,0,0,44,19.5Zm0,41.41A16.91,16.91,0,1,1,60.9,44,16.9,16.9,0,0,1,44,60.91Z"></path>
            <circle cx="44" cy="44" r="10.82"></circle>
        </g>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-filters1">
        <g id="_8" data-name="8">
            <path d="M27.47,50.85v-43A1.34,1.34,0,0,0,26.13,6.5H23.08a1.34,1.34,0,0,0-1.35,1.34v43a8,8,0,0,0,0,15V80.16a1.34,1.34,0,0,0,1.35,1.34h3.05a1.34,1.34,0,0,0,1.34-1.34V65.82a8,8,0,0,0,0-15Z"></path>
            <path d="M46.87,21.74V7.84A1.34,1.34,0,0,0,45.53,6.5H42.47a1.34,1.34,0,0,0-1.34,1.34v13.9a8,8,0,0,0,0,15V80.16a1.34,1.34,0,0,0,1.34,1.34h3.06a1.34,1.34,0,0,0,1.34-1.34V36.7a8,8,0,0,0,0-15Z"></path>
            <path d="M66.27,41.44V7.84A1.34,1.34,0,0,0,64.92,6.5H61.87a1.34,1.34,0,0,0-1.34,1.34v33.6a8,8,0,0,0,0,15V80.16a1.34,1.34,0,0,0,1.34,1.34h3.05a1.34,1.34,0,0,0,1.35-1.34V56.41a8,8,0,0,0,0-15Z"></path>
        </g>
    </symbol>
	<symbol id="shrs-icon-refused-cancel-leave-i" viewBox="0 0 88 88">
		<g id="g7426">
			<path id="path4033" style="stroke-width:1.18754" d="M 88,0 78.199219,9.8007812 H 88 Z M 68.5,19.5 58.699219,29.300781 H 88 V 19.5 Z m -6.109375,17.335938 a 25.59406,25.59406 0 0 0 -9.837891,2.263671 h -3.578125 l -9.798828,9.701172 h 2.164063 a 25.59406,25.59406 0 0 0 -3.748047,9.798828 H 29.400391 L 19.5,68.400391 h 18.425781 a 25.59406,25.59406 0 0 0 4.865235,9.798828 H 9.8007812 L 0,88 h 88 v -9.800781 h -6.171875 a 25.59406,25.59406 0 0 0 5.140625,-9.798828 h 1.0625 v -9.800782 h -0.423828 a 25.59406,25.59406 0 0 0 -3.566406,-9.699218 h 3.933593 V 39.099609 H 72.773438 A 25.59406,25.59406 0 0 0 62.390625,36.835938 Z m 2.277344,9.845703 a 4.1357674,4.1357674 0 0 1 2.771484,1.066406 3.4493812,3.4493812 0 0 1 1.164063,2.59375 3.4144802,3.4144802 0 0 1 -1.164063,2.595703 4.1357674,4.1357674 0 0 1 -5.542969,0 3.4261138,3.4261138 0 0 1 -1.164062,-2.595703 3.490099,3.490099 0 0 1 1.164062,-2.59375 4.1357674,4.1357674 0 0 1 2.771485,-1.066406 z m -3.644531,10.646484 a 5.3165841,5.3165841 0 0 1 3.660156,1.164063 3.8042079,3.8042079 0 0 1 1.291015,2.984374 c 0,0.255941 -0.03554,0.697998 -0.08789,1.332032 a 9.1033414,9.1033414 0 0 1 -0.326172,1.74414 l -1.640625,5.816407 c -0.139604,0.465347 -0.254757,1.006291 -0.371094,1.599609 a 8.399505,8.399505 0 0 0 -0.158203,1.355469 1.9660891,1.9660891 0 0 0 0.519531,1.576172 2.867698,2.867698 0 0 0 1.796875,0.425781 5.2351485,5.2351485 0 0 0 1.337891,-0.25 7.8003713,7.8003713 0 0 0 1.099609,-0.367188 l -0.419922,1.796875 c -1.32042,0.517698 -2.367071,0.908122 -3.152343,1.164063 a 8.1959159,8.1959159 0 0 1 -2.728516,0.414062 5.4271039,5.4271039 0 0 1 -3.722656,-1.164062 3.7460396,3.7460396 0 0 1 -1.320313,-2.960938 11.238119,11.238119 0 0 1 0.09375,-1.425781 16.682674,16.682674 0 0 1 0.314453,-1.640625 l 1.650391,-5.816406 C 59.004796,64.494489 59.133493,63.988614 59.226562,63.5 a 7.3641089,7.3641089 0 0 0 0.152344,-1.373047 2.105693,2.105693 0 0 0 -0.466797,-1.552734 2.5710396,2.5710396 0 0 0 -1.755859,-0.470703 4.8105198,4.8105198 0 0 0 -1.314453,0.191406 c -0.442079,0.139604 -0.826687,0.268428 -1.164063,0.384766 l 0.453125,-1.740235 c 1.076112,-0.442079 2.118519,-0.812887 3.101563,-1.132812 a 9.0509899,9.0509899 0 0 1 2.791016,-0.478516 z"></path>
		</g>
	</symbol>
	<symbol id="shrs-icon-refused-cancel-leave" viewBox="0 0 88 88">
		<g id="g3916">
			<path id="path4033" style="stroke-width:1.18754" d="M 88,0 78.2,9.8 H 88 Z M 68.5,19.5 58.7,29.3 H 88 V 19.5 Z M 29.4,58.6 19.5,68.4 H 88 V 58.6 Z M 9.8,78.2 0,88 h 88 v -9.8 z" sodipodi:nodetypes="ccccccccccccccccccc"></path>
			<path d="m 48.975,39.1 -9.8,9.7 H 88 v -9.7 z" style="stroke-width:1.18754" id="path1575" sodipodi:nodetypes="ccccc"></path>
		</g>
	</symbol>
	
	<symbol id="shrs-icon-refused-leave-i" viewBox="0 0 88 88">
		<g id="g1145">
			<path id="path4033" style="stroke-width:1.18754" d="M 88,0 0,88 H 88 V 62.328125 A 25.671232,25.671232 0 1 1 62.328125,36.658203 25.671232,25.671232 0 0 1 88,62.328125 Z M 64.613281,46.533203 a 4.1482377,4.1482377 0 0 0 -2.78125,1.070313 3.5006226,3.5006226 0 0 0 -1.166015,2.601562 3.4364445,3.4364445 0 0 0 1.166015,2.601563 4.1482377,4.1482377 0 0 0 5.560547,0 3.4247757,3.4247757 0 0 0 1.167969,-2.601563 3.459782,3.459782 0 0 0 -1.167969,-2.601562 4.1482377,4.1482377 0 0 0 -2.779297,-1.070313 z m -3.65625,10.679688 a 9.0782812,9.0782812 0 0 0 -2.800781,0.478515 c -0.986009,0.32089 -2.030016,0.693307 -3.109375,1.136719 l -0.455078,1.744141 c 0.338393,-0.116687 0.724557,-0.244741 1.167969,-0.384766 a 4.8250248,4.8250248 0 0 1 1.318359,-0.193359 2.578792,2.578792 0 0 1 1.761719,0.472656 2.1120423,2.1120423 0 0 1 0.466797,1.558594 7.3863136,7.3863136 0 0 1 -0.152344,1.376953 c -0.09335,0.490087 -0.221329,0.998594 -0.367188,1.582031 l -1.65625,5.833984 a 16.732976,16.732976 0 0 0 -0.316406,1.644532 11.272005,11.272005 0 0 0 -0.0918,1.429687 3.7573349,3.7573349 0 0 0 1.324219,2.96875 5.4434681,5.4434681 0 0 0 3.734375,1.167969 8.2206287,8.2206287 0 0 0 2.736328,-0.414063 c 0.78764,-0.256713 1.837707,-0.648709 3.16211,-1.167968 l 0.419921,-1.802735 a 7.8238914,7.8238914 0 0 1 -1.103515,0.367188 5.2509339,5.2509339 0 0 1 -1.341797,0.251953 2.8763449,2.8763449 0 0 1 -1.802735,-0.425781 1.9720174,1.9720174 0 0 1 -0.519531,-1.582032 8.4248316,8.4248316 0 0 1 0.158203,-1.359375 c 0.116687,-0.595106 0.233022,-1.136765 0.373047,-1.603515 l 1.644531,-5.833985 a 9.1307905,9.1307905 0 0 0 0.328126,-1.751953 c 0.05251,-0.635946 0.08594,-1.079225 0.08594,-1.335937 a 3.8156786,3.8156786 0 0 0 -1.294922,-2.992188 5.332615,5.332615 0 0 0 -3.669922,-1.166015 z"></path>
		</g>
	</symbol>
	<symbol id="shrs-icon-refused-leave" viewBox="0 0 88 88">
		<g id="g1145">
			<path id="path4033" style="stroke-width:1.18754" d="M 88,0 0,88 h 88 z" sodipodi:nodetypes="cccc"></path>
		</g>
	</symbol>
	<symbol id="shrs-icon-clock-small" viewBox="0 0 88 88">
		<path style="stroke-width:0.153322" d="M 40.93662,87.8177 C 33.112367,87.36832 24.877894,84.419435 18.356777,79.731533 13.997907,76.59802 9.5987872,71.933829 6.754192,67.429821 4.597084,64.01435 2.34491,58.943588 1.544721,55.700715 1.461491,55.363407 1.287381,54.67346 1.157812,54.167499 0.446171,51.388558 0,47.4595 0,43.971609 0,40.51876 0.216349,38.486849 1.012151,34.465667 1.264705,33.18951 1.260357,33.204966 2.155075,30.402644 5.571519,19.702087 13.334272,10.455264 23.304633,5.2097484 27.807439,2.8407717 31.967253,1.4521309 37.256903,0.55215944 39.104761,0.23776775 39.825207,0 44,0 c 5.142435,0 6.699526,0.36943336 10.63457,1.3591339 10.720395,2.6962804 19.887939,9.1478991 25.997073,18.2953171 4.162248,6.232278 6.615918,13.271402 7.220425,20.7141 1.149046,14.146873 -4.438997,27.709135 -15.25453,37.023004 -7.198951,6.199421 -16.252337,9.85476 -25.834692,10.430875 -2.609594,0.156888 -3.018043,0.156562 -5.826226,-0.0049 z M 48.5,48.439369 V 5.4872383 h -9 V 39.439369 h -18 v 9 z" id="path4558" inkscape:connector-curvature="0" sodipodi:nodetypes="ccsccsssssssscscccccccccc"></path>
	</symbol>
	<symbol id="shrs-icon-clock-small-sup" viewBox="0 0 88 88">
		<path style="stroke-width:0.143269" d="m 41.779311,87.952414 c -8.66472,-0.376725 -17.34773,-3.508223 -24.34292,-8.779187 l -1.15424,-0.869735 13.85892,-0.03653 c 7.62241,-0.02009 20.08981,-0.02009 27.70533,0 l 13.8464,0.03653 -1.05956,0.798224 c -3.12769,2.356277 -5.86808,3.948818 -9.29757,5.403173 -6.1239,2.596975 -12.65163,3.747729 -19.55636,3.447525 z M 6.6479771,67.455132 C 5.4499121,65.603114 2.9951131,60.604195 2.5300761,59.069497 L 2.4323961,58.74714 H 43.990341 c 22.85688,0 41.55795,0.03612 41.55795,0.08027 0,0.04415 -0.29291,0.795419 -0.6509,1.669498 -0.79411,1.938861 -2.37678,5.059337 -3.39977,6.703165 l -0.75786,1.217794 -36.71115,0.03609 -36.7111499,0.03609 z M 0.10259406,47.819196 c -0.18483,-2.277927 -0.109371,-6.737362 0.14176,-8.377687 l 0.07129,-0.465627 H 19.578961 38.842281 v -4.871179 -4.871178 h -18.19529 c -10.00741,0 -18.1952819,-0.03267 -18.1952819,-0.07259 0,-0.03992 0.231909,-0.668522 0.515354,-1.396882 0.736205,-1.891803 2.341137,-5.097374 3.388805,-6.76855 l 0.898167,-1.432699 15.7941159,-0.03645 15.79413,-0.03645 V 14.62022 9.7505373 l -11.28251,-0.03669 -11.28251,-0.03669 1.46001,-1.096402 c 4.52048,-3.394691 9.95944,-5.939261 15.71787,-7.353451 C 37.100931,0.33193432 39.901681,0.00159432 43.860911,4.3245062e-6 51.188281,-0.00299568 57.741141,1.5548243 64.201071,4.8353143 c 1.92678,0.978456 5.85811,3.486044 7.02022,4.477823 l 0.50145,0.427946 -11.28251,0.0039 -11.28251,0.0039 v 4.8711787 4.87118 h 15.76063 15.76064 l 0.61883,0.967071 c 0.91725,1.433434 2.10804,3.635891 2.88544,5.336808 0.6212,1.359165 1.36503,3.195599 1.36503,3.370116 0,0.0376 -8.18788,0.06836 -18.19529,0.06836 h -18.19528 v 4.871179 4.871179 h 19.26332 19.26332 l 0.0713,0.465627 c 0.25114,1.640324 0.3266,6.09976 0.14176,8.377687 l -0.0962,1.185585 H 44.000001 0.19879206 Z M 48.584641,26.941204 V 5.4507043 h -4.58464 -4.58464 V 22.499835 39.548963 h -8.88274 -8.88274 v 4.441369 4.44137 h 13.46738 13.46738 z" id="path1217"></path>
	</symbol>
	<symbol id="shrs-icon-clock-small-bro" viewBox="0 0 88 88">
		<path style="stroke-width:0.143134" d="m 40.421137,87.871274 c -4.267315,-0.31718 -9.152445,-1.4958 -13.239828,-3.19433 -1.403079,-0.58305 -4.638612,-2.16978 -4.862889,-2.38478 -0.08856,-0.0849 10.807385,-11.08115 29.843289,-30.11798 27.099506,-27.10085 30.001268,-29.96006 30.175046,-29.73262 0.363069,0.47518 2.14703,4.26021 2.715897,5.7623 2.009915,5.30723 2.885055,10.06371 2.887485,15.69382 7e-4,1.58581 -0.0648,3.55032 -0.14552,4.36559 -1.534155,15.49428 -10.946973,28.87438 -25.016158,35.55989 -6.799529,3.23105 -14.526628,4.63015 -22.357322,4.04811 z M 5.8390521,66.027084 c -3.000718,-5.35892 -4.776819,-10.60261 -5.58744701,-16.49622 -0.299866,-2.18015 -0.341658,-8.33345 -0.07145,-10.52036 0.94310801,-7.63307 3.56061701,-14.56248 7.72808801,-20.45884 1.962292,-2.77635 5.7810939,-6.81504 8.4935659,-8.9826103 5.830766,-4.65946 12.833646,-7.80058 20.011599,-8.97615002 2.732017,-0.44744 4.544359,-0.59173 7.447108,-0.5928999982287 C 51.788466,-0.00299632 58.791205,1.8273037 65.661315,5.6986337 l 1.408485,0.79369 -8.958765,8.9596603 -8.958766,8.95967 v 12.27334 12.27334 H 36.878163 24.604058 l -9.016544,9.01745 c -4.959099,4.95959 -9.0595009,9.01745 -9.1120059,9.01745 -0.05246,0 -0.338899,-0.43477 -0.636456,-0.96615 z M 48.579734,26.915664 V 5.4455337 h -4.580265 -4.580264 v 17.0329703 17.03297 h -8.874264 -8.874262 v 4.43716 4.43716 h 13.454527 13.454528 z" id="path936"></path>
	</symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-filters2">
        <path id="Forma_1.svg" data-name="Forma 1.svg" d="M81.15,8.59A3.17,3.17,0,0,0,78,6.52H10A3.17,3.17,0,0,0,6.84,8.59a3.08,3.08,0,0,0,.75,3.75L33.82,38.57V64.45a3.31,3.31,0,0,0,1,2.4L48.41,80.47a3.15,3.15,0,0,0,2.39,1,3.76,3.76,0,0,0,1.33-.27,3.18,3.18,0,0,0,2.07-3.14V38.57L80.44,12.33A3.06,3.06,0,0,0,81.15,8.59Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-information">
        <path d="M53.72,74.78c-2,0-3.46-.33-4.29-1S48.19,71.85,48.19,70a19.44,19.44,0,0,1,.38-3.24c.28-1.41.55-2.7.88-3.81L53.36,49.1a22,22,0,0,0,.79-4.19c.13-1.52.2-2.58.2-3.19a9.08,9.08,0,0,0-3.08-7.11c-2-1.83-5-2.76-8.73-2.76A21.74,21.74,0,0,0,35.86,33c-2.34.76-4.82,1.64-7.39,2.7l-1,4.27c.76-.28,1.67-.58,2.73-.91a11.17,11.17,0,0,1,3.13-.45c2.07,0,3.46.33,4.19,1s1.11,1.95,1.11,3.71a17.65,17.65,0,0,1-.35,3.28c-.23,1.17-.53,2.43-.88,3.77l-4,13.91c-.34,1.47-.6,2.75-.75,3.92a26.48,26.48,0,0,0-.23,3.4,8.94,8.94,0,0,0,3.16,7.07c2.1,1.87,5.07,2.81,8.89,2.81a19.54,19.54,0,0,0,6.51-1c1.87-.65,4.37-1.59,7.53-2.82l1.06-4.3a17.46,17.46,0,0,1-2.63.88A12.42,12.42,0,0,1,53.72,74.78Z"></path>
        <path d="M51.22,6.5A9.52,9.52,0,0,0,44.6,9.05a8.34,8.34,0,0,0,0,12.38,9.9,9.9,0,0,0,13.24,0,8.38,8.38,0,0,0,0-12.38A9.46,9.46,0,0,0,51.22,6.5Z"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-certificaterequest">
        <path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z"></path>
        <path d="M44,7.5A36.51,36.51,0,0,0,30,77.73v-20l7.83,6.64V46.82a11.79,11.79,0,0,0,2.31-16.64H52.73V44.37a1.7,1.7,0,0,0,1.7,1.7H68.62V70.94A36.5,36.5,0,0,0,44,7.5ZM35.8,60.16,30,55l-5.76,5.15V45A9.1,9.1,0,0,0,35.8,45ZM30,45.59a8.23,8.23,0,1,1,8.23-8.23A8.24,8.24,0,0,1,30,45.59Zm10.66-8.23a10.62,10.62,0,0,1-4.09,8.38V44.66a9.84,9.84,0,1,0-13.14,0v1.08a10.66,10.66,0,1,1,17.23-8.38ZM56.14,43.8A1.14,1.14,0,0,1,55,42.66V30.18L68.62,43.8Z"></path>
    </symbol>
	  <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-manageprobation">
         <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="namedview1256" showgrid="false" inkscape:zoom="4" inkscape:cx="10.524903" inkscape:cy="24.22857" inkscape:window-x="-8" inkscape:window-y="224" inkscape:window-maximized="1" inkscape:current-layer="Calque_1"></sodipodi:namedview>
		 <defs id="defs1247">    <style id="style1245">.cls-1{fill:#59b9bf;}</style>  </defs>
		 <path class="cls-1" d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z" id="path1251"></path>
  <path style="fill-opacity:1;stroke-width:0.1641791" d="M 39.271642,87.694963 C 26.86364,86.181152 16.059879,80.002509 8.8032977,70.270198 4.2118416,64.112274 1.3562235,56.948097 0.34112742,49.040299 5.9147164e-4,46.387456 0.08318985,40.627792 0.50123104,37.876119 1.6682874,30.194204 4.3929746,23.637908 8.9617639,17.51791 10.883141,14.944182 14.920609,10.912162 17.545765,8.9454934 26.871247,1.9591887 38.229809,-1.0316092 49.779104,0.45820347 62.300922,2.0734673 73.653636,9.2110153 80.754187,19.932499 c 2.103336,3.175933 4.2443,7.792999 5.392502,11.629113 1.924489,6.429682 2.265929,14.475247 0.898972,21.183019 -2.729242,13.392621 -11.615363,24.827016 -23.928587,30.790592 -3.754144,1.818216 -8.557806,3.315087 -12.599164,3.926027 -2.110763,0.319087 -9.318723,0.468879 -11.246268,0.233713 z m 10.671642,-2.040754 c 9.356245,-1.41507 17.686914,-5.685907 24.223216,-12.418378 3.214494,-3.310969 5.417047,-6.415078 7.44953,-10.498794 2.038904,-4.096618 3.345896,-8.250663 4.051143,-12.875843 0.369036,-2.420241 0.425374,-8.714175 0.09846,-11 C 84.364144,29.061716 80.358801,20.902727 73.674224,14.230662 67.291009,7.8593956 59.505227,3.9325522 50.189552,2.3859149 47.931277,2.0109844 40.510316,1.9574709 38.286567,2.3000811 32.526677,3.1875009 26.478328,5.3724726 21.87161,8.2300012 16.59428,11.503507 11.228532,16.89498 8.1145728,22.052995 5.9849187,25.580589 4.13214,30.060209 3.1541402,34.046251 c -0.9285307,3.784422 -1.0690656,5.086425 -1.0690656,9.904495 0,4.70767 0.1389376,6.073626 0.9852646,9.686567 4.0926474,17.471369 18.9033968,30.402784 36.9401088,32.252779 2.081921,0.213539 7.870175,0.07608 9.932836,-0.235883 z" id="path1260" inkscape:connector-curvature="0"></path>
  <path style="fill-opacity:1;stroke-width:0.1641791" d="M 27.656443,76.547764 C 17.279456,71.353357 10.078728,61.60453 8.0663408,50.025373 7.6231068,47.475031 7.4735667,43.120112 7.739259,40.500071 9.6884414,21.278813 26.065155,6.9366016 45.264179,7.6369228 c 5.454518,0.1989639 9.702041,1.2549612 14.61194,3.6327452 11.000485,5.327353 18.417506,15.518013 20.20131,27.755705 0.343216,2.354613 0.391588,6.983454 0.09694,9.27612 -1.037661,8.073973 -4.421502,15.18005 -10.007092,21.014925 l -1.493066,1.559701 -0.0073,-12.436567 -0.0072,-12.436567 h -7.385468 -7.385471 l -0.536171,-0.478938 -0.536173,-0.47894 -0.04737,-7.483747 -0.04737,-7.483748 h -6.31457 c -3.473014,0 -5.014118,0.03324 -5.014118,0.07386 0,0.04062 0.14785,0.358127 0.296,0.809733 0.821715,2.504835 1.287721,5.280416 0.718232,7.859412 -0.597832,2.707351 -1.389591,4.787051 -3.408232,6.830072 -2.890433,2.725258 -5.904254,4.217088 -9.003,4.817311 v 25.715 1.342374 c -0.08242,0.0089 -1.265404,-0.439924 -2.339549,-0.977609 z m 34.271915,-39.49254 c -3.746572,-3.747388 -6.867694,-6.813433 -6.935825,-6.813433 -0.06813,0 -0.123876,2.811132 -0.123876,6.246959 0,5.192243 0.041,6.326236 0.242814,6.71651 0.133548,0.258254 0.33672,0.507335 0.451493,0.553513 0.114772,0.04618 3.126628,0.08979 6.69301,0.09692 l 6.484333,0.01296 z" id="path1262" inkscape:connector-curvature="0" sodipodi:nodetypes="cssssscscccccccccssssccssccsssccscs"></path>
  <circle style="fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:1.16893351" id="path6178" cx="28.119398" cy="35.893539" r="12"></circle>
  <path inkscape:connector-curvature="0" d="M 64.590492,50.154417 H 34.930321 a 1.7299145,1.493 0 1 0 0,2.986 h 29.660171 a 1.7299145,1.493 0 1 0 0,-2.986 z" id="path10049" style="stroke-width:0.73049885"></path>
  <path inkscape:connector-curvature="0" d="m 64.590085,57.824982 h -29.66017 a 1.7299145,1.493 0 1 0 0,2.986 h 29.66017 a 1.7299145,1.493 0 1 0 0,-2.986 z" id="path10049-2" style="stroke-width:0.73049885"></path>
  <path inkscape:connector-curvature="0" d="m 64.590085,65.470573 h -29.66017 a 1.7299145,1.493 0 1 0 0,2.986 h 29.66017 a 1.7299145,1.493 0 1 0 0,-2.986 z" id="path10049-6" style="stroke-width:0.73049885"></path>
  <path style="fill-rule:nonzero;stroke-width:0.56031221" d="m 27.692777,25.997181 c 3.970223,0 7.883205,1.714561 7.883205,5.815574 -0.01162,3.808087 -4.636057,5.261206 -5.631506,6.628203 -0.285027,0.489296 -0.532453,0.997026 -0.740393,1.519319 -1.123541,-0.38776 -2.360401,-0.372285 -3.4725,0.04346 -0.204051,-0.329402 -0.310218,-0.704029 -0.30746,-1.084924 0,-3.466332 5.434822,-4.252689 5.434822,-7.106041 0,-1.567716 -1.117213,-2.500413 -2.980786,-2.500413 -3.976011,0 -2.425324,3.839616 -5.434824,3.839616 -1.102127,0.09016 -2.043547,-0.736906 -2.018841,-1.773597 0,-2.85335 3.471685,-5.38118 7.268283,-5.38118 z m -0.284853,15.73352 c 1.449164,-0.05986 2.658879,1.025306 2.658632,2.384928 -0.0031,1.315079 -1.138032,2.381261 -2.541074,2.38705 -3.350256,0.0307 -3.465755,-4.657686 -0.117558,-4.771978 z" id="path836" inkscape:connector-curvature="0" sodipodi:nodetypes="ccccccssccsccccc"></path>
    </symbol>
	<symbol viewBox="0 0 88 88" id="shrs-icon-demarche-managecontract">
		  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="namedview1256" showgrid="false" inkscape:zoom="5.6568543" inkscape:cx="36.145623" inkscape:cy="53.169956" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="Calque_1"></sodipodi:namedview>
  <defs id="defs1247">
    <style id="style1245">.cls-1{fill:#59b9bf;}</style>
  </defs>
  <path class="cls-1" d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z" id="path1251"></path>
  <path style="fill-opacity:1;stroke-width:0.1641791" d="M 39.271642,87.694963 C 26.86364,86.181152 16.059879,80.002509 8.8032977,70.270198 4.2118416,64.112274 1.3562235,56.948097 0.34112742,49.040299 5.9147164e-4,46.387456 0.08318985,40.627792 0.50123104,37.876119 1.6682874,30.194204 4.3929746,23.637908 8.9617639,17.51791 10.883141,14.944182 14.920609,10.912162 17.545765,8.9454934 26.871247,1.9591887 38.229809,-1.0316092 49.779104,0.45820347 62.300922,2.0734673 73.653636,9.2110153 80.754187,19.932499 c 2.103336,3.175933 4.2443,7.792999 5.392502,11.629113 1.924489,6.429682 2.265929,14.475247 0.898972,21.183019 -2.729242,13.392621 -11.615363,24.827016 -23.928587,30.790592 -3.754144,1.818216 -8.557806,3.315087 -12.599164,3.926027 -2.110763,0.319087 -9.318723,0.468879 -11.246268,0.233713 z m 10.671642,-2.040754 c 9.356245,-1.41507 17.686914,-5.685907 24.223216,-12.418378 3.214494,-3.310969 5.417047,-6.415078 7.44953,-10.498794 2.038904,-4.096618 3.345896,-8.250663 4.051143,-12.875843 0.369036,-2.420241 0.425374,-8.714175 0.09846,-11 C 84.364144,29.061716 80.358801,20.902727 73.674224,14.230662 67.291009,7.8593956 59.505227,3.9325522 50.189552,2.3859149 47.931277,2.0109844 40.510316,1.9574709 38.286567,2.3000811 32.526677,3.1875009 26.478328,5.3724726 21.87161,8.2300012 16.59428,11.503507 11.228532,16.89498 8.1145728,22.052995 5.9849187,25.580589 4.13214,30.060209 3.1541402,34.046251 c -0.9285307,3.784422 -1.0690656,5.086425 -1.0690656,9.904495 0,4.70767 0.1389376,6.073626 0.9852646,9.686567 4.0926474,17.471369 18.9033968,30.402784 36.9401088,32.252779 2.081921,0.213539 7.870175,0.07608 9.932836,-0.235883 z" id="path1260" inkscape:connector-curvature="0"></path>
  <path style="fill-opacity:1;stroke-width:0.1641791" d="M 27.656443,76.547764 C 17.279456,71.353357 10.078728,61.60453 8.0663408,50.025373 7.6231068,47.475031 7.4735667,43.120112 7.739259,40.500071 9.6884414,21.278813 26.065155,6.9366016 45.264179,7.6369228 c 5.454518,0.1989639 9.702041,1.2549612 14.61194,3.6327452 11.000485,5.327353 18.417506,15.518013 20.20131,27.755705 0.343216,2.354613 0.391588,6.983454 0.09694,9.27612 -1.037661,8.073973 -4.421502,15.18005 -10.007092,21.014925 l -1.493066,1.559701 -0.0073,-12.436567 -0.0072,-12.436567 h -7.385468 -7.385471 l -0.536171,-0.478938 -0.536173,-0.47894 -0.04737,-7.483747 -0.04737,-7.483748 h -6.31457 c -3.473014,0 -5.014118,0.03324 -5.014118,0.07386 0,0.04062 0.14785,0.358127 0.296,0.809733 0.821715,2.504835 1.287721,5.280416 0.718232,7.859412 -0.597832,2.707351 -1.389591,4.787051 -3.408232,6.830072 -1.185049,1.199361 -2.37074,1.988309 -3.903,2.812311 1.549233,0.67947 3.032635,1.818991 3.903,2.812 0.935127,1.025503 3.265344,4.328721 3.608,8.859 0.125455,1.747137 -0.02887,5.336633 -1.956,8.673566 -1.927134,3.336933 -6.445797,6.156136 -10.655,6.760434 v 0.615 1.342374 c -0.08242,0.0089 -1.265404,-0.439924 -2.339549,-0.977609 z m 34.271915,-39.49254 c -3.746572,-3.747388 -6.867694,-6.813433 -6.935825,-6.813433 -0.06813,0 -0.123876,2.811132 -0.123876,6.246959 0,5.192243 0.041,6.326236 0.242814,6.71651 0.133548,0.258254 0.33672,0.507335 0.451493,0.553513 0.114772,0.04618 3.126628,0.08979 6.69301,0.09692 l 6.484333,0.01296 z" id="path1262" inkscape:connector-curvature="0" sodipodi:nodetypes="cssssscscccccccccssssscccscssccsssccscs"></path>
  <circle style="fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:1.16893351" id="path6178" cx="28.119398" cy="35.893539" r="12"></circle>
  <circle style="fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:1.16893363" id="path6178-1" cx="28.063677" cy="60.921265" r="12"></circle>
  <path inkscape:connector-curvature="0" d="m 37.222377,31.648032 -2.074063,-2.050846 a 0.63427006,0.62717005 0 0 0 -0.887976,0 l -8.63876,8.542056 -3.674538,-3.633405 a 0.63427006,0.62717005 0 0 0 -0.887977,0 l -2.074064,2.050847 a 0.63427006,0.62717005 0 0 0 0,0.878037 l 6.192591,6.12327 a 0.63427006,0.62717005 0 0 0 0.887977,0 l 11.15681,-11.03192 a 0.63427006,0.62717005 0 0 0 0,-0.878039 z m 0,0" id="path6632" style="stroke-width:0.21023668"></path>
  <path inkscape:connector-curvature="0" d="m 30.291516,60.693806 5.522118,-5.509355 a 1.319203,1.3191938 0 0 0 -0.03446,-1.871587 l -0.228959,-0.219868 a 1.3137515,1.3137425 0 0 0 -1.853424,-0.01641 L 28.181938,58.58958 22.67072,53.078404 a 1.2992149,1.2992057 0 0 0 -1.853424,0.01641 l -0.221685,0.221678 a 1.319203,1.3191938 0 0 0 -0.03447,1.871586 l 5.511218,5.505726 -5.505765,5.51118 a 1.2992149,1.2992057 0 0 0 0.01641,1.853407 l 0.221675,0.221685 a 1.319203,1.3191938 0 0 0 1.86978,0.03448 l 5.507582,-5.507542 5.509401,5.518448 a 1.319203,1.3191938 0 0 0 1.871596,-0.03448 l 0.221686,-0.221675 a 1.3137515,1.3137425 0 0 0 0.01641,-1.853414 z" id="path7661" style="stroke-width:0.18170771"></path>
  <path inkscape:connector-curvature="0" d="M 65.205433,57.858875 H 44.261 c 0,0 0.292529,1.239085 0.3,2.985786 h 20.644433 c 1.598461,10e-4 1.598461,-2.984755 0,-2.985786 z" id="path5048-7" style="stroke-width:0.60794061" sodipodi:nodetypes="ccccc"></path>
  <path inkscape:connector-curvature="0" d="M 65.048388,50.200739 H 40.613 c 0,0 1.458397,1.452934 2.1,2.985786 h 22.335388 c 1.807847,10e-4 1.807847,-2.984755 0,-2.985786 z" id="path5048-7-7" style="stroke-width:0.64653343" sodipodi:nodetypes="ccccc"></path>
  <path inkscape:connector-curvature="0" d="M 65.205433,65.517214 H 44.261 c 0,0 -0.25617,1.327473 -1,2.985786 h 21.944433 c 1.598461,0.001 1.598461,-2.984755 0,-2.985786 z" id="path5048-7-79" style="stroke-width:0.60794061" sodipodi:nodetypes="ccccc"></path>
    </symbol>
	
	<symbol viewBox="0 0 90 90" id="shrs-icon-demarche-clockingregularization">
		<metadata id="metadata5114">
		<rdf:rdf>
		  <cc:work rdf:about="">
			<dc:format>image/svg+xml</dc:format>
			<dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type>
		  </cc:work>
		</rdf:rdf>
	  </metadata>
	  <defs id="defs5112"></defs>
	  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="namedview5110" showgrid="false" inkscape:zoom="3.1363636" inkscape:cx="33.260962" inkscape:cy="40.181628" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-monDemenagement"></sodipodi:namedview>
	  <g transform="translate(-0.809322,-0.80932209)" id="g1165">
		<g id="g1163">
		  <g id="g1161">
			<path inkscape:connector-curvature="0" class="st0" d="m 35.2,80.3 c 3.1,0.9 6.4,1.3 9.8,1.3 3.4,0 6.7,-0.5 9.8,-1.3 z" id="path1157"></path>
		  </g>
		</g>
	  </g>
	  <g id="g2180" transform="matrix(0.66954073,0,0,0.66954073,13.924836,26.971881)">
		<g id="g2176">
		  <g id="g1753">
			<g id="g1758"></g>
		  </g>
		</g>
	  </g>
	  <g transform="translate(-0.809322,-0.80932209)" id="g2329">
		<path sodipodi:nodetypes="sscsssccsscccccccccccccccsssccsssssscccsssccsssccccc" inkscape:connector-curvature="0" id="path1159" d="M 45,8.4003906 C 24.8,8.4003906 8.4003906,24.8 8.4003906,45 c 0,8.054089 2.5981214,15.521735 7.0039064,21.572266 V 44.787109 c 0,-3.61552 2.945575,-6.49414 6.494141,-6.49414 h 45.929687 c 3.548566,0 6.494688,2.87862 6.427734,6.49414 V 66.945312 C 78.890008,60.820025 81.599609,53.265476 81.599609,45 81.599609,24.8 65.2,8.4003906 45,8.4003906 Z M 74.255859,66.945312 v 0 M 61.570312,77.527344 c 0,0 1.676967,-0.91653 0,0 m 0,0 H 28.09375 c 9.860184,5.468616 21.5588,6.513533 33.476562,0 z M 18.4175,70.398438 c 1.447652,1.429029 3.324773,2.845433 4.741,3.916015 H 66.609 c 1.297779,-0.945272 2.595558,-1.977243 3.891,-3.115234 l 0.609,-0.621094 3.75e-4,-19.363281 H 18.617188 Z M 71.5,23.449219 c 0.15,0 0.300391,0.05039 0.400391,0.15039 L 73.800781,25.5 C 73.900781,25.6 74,25.700391 74,25.900391 l -0.300781,0.298828 -10,10 c -0.1,0.1 -0.298438,0.201172 -0.398438,0.201172 -0.1,0 -0.30039,-0.101172 -0.40039,-0.201172 l -5.5,-5.5 c -0.2,-0.2 -0.2,-0.598828 0,-0.798828 L 59.300781,28 c 0.2,-0.2 0.695159,-0.263159 0.798828,0 l 3.300782,3.300781 7.699218,-7.701172 c 0.1,-0.1 0.250391,-0.15039 0.400391,-0.15039 z m -49.601562,18.05664 c -1.80776,0 -3.28125,1.47349 -3.28125,3.28125 v 3.146485 h 52.492187 v -3.146485 c 0,-1.80776 -1.47349,-3.28125 -3.28125,-3.28125 z m 1.673828,14.060547 h 20.554687 v 5.021485 H 23.572266 Z"></path>
		<path id="path5093" d="m 44.809322,0.80932209 a 44,44 0 1 0 44,43.99999991 44,44 0 0 0 -44,-43.99999991 z m 0,85.99999991 a 42,42 0 1 1 42,-42 42,42 0 0 1 -42,42 z" inkscape:connector-curvature="0"></path>
	  </g>
	</symbol>
	<symbol viewBox="0 0 88 88" id="shrs-icon-demarche-weeklytaplanning"><metadata id="shrs-icon-demarche-planninghebdo-metadata3433"> <rdf:rdf> <cc:work rdf:about=""> <dc:format>image/svg+xml</dc:format> <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type> </cc:work> </rdf:rdf> </metadata>  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="shrs-icon-demarche-planninghebdo-namedview3429" showgrid="false" inkscape:zoom="4" inkscape:cx="46.979261" inkscape:cy="25.538351" inkscape:window-x="-8" inkscape:window-y="224" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-teamplanning"></sodipodi:namedview>  <path d="M 44,7.5 C 23.8,7.5 7.5,23.8 7.5,44 c 0,10.8 4.700391,20.7 12.400391,27.5 V 40.900391 c 0,-1.7 1.499218,-3.09961 3.199218,-3.09961 h 5.5 V 43 c 0,2 1.59961,3.5 3.59961,3.5 h 0.701172 c 1.9,0 3.5,-1.6 3.5,-3.5 V 37.800781 H 53.599609 V 43 c 0,2.034377 1.595872,3.49303 3.562974,3.463804 0.972332,-0.01445 -0.459544,0.01882 0.631015,0.01289 C 59.758536,46.466004 61.400391,44.73192 61.400391,43 v -5.199219 h 5.5 C 69.090306,37.745141 70.050442,39.306154 70,40.900391 V 69.5 C 77.8,61.6 81.899609,50.3 80.099609,38.5 77.399609,20.7 62.1,7.5 44,7.5 Z m 13.2,26.4 c 0.879008,-0.01431 0.7,0 0.7,0 1.116736,0.02252 1.969837,0.820596 2,1.901 0.05526,1.979289 0,7.199 0,7.199 -0.01165,1.018326 -0.975094,1.882174 -2,1.9 -0.233557,0.0041 -0.7,0 -0.7,0 -0.515506,-0.02427 -1.982705,-0.455441 -2,-1.9 -0.01776,-1.483197 0,-7.199 0,-7.199 5.49e-4,-1.361762 1.120992,-1.886687 2,-1.901 z m -25.000781,3.91e-4 h 0.701172 c 1.1,0 2,0.80039 2,1.90039 V 43 c 0,1.1 -0.9,1.900391 -2,1.900391 h -0.701172 c -1.1,0 -2,-0.800391 -2,-1.900391 v -7.199219 c 0,-1.1 0.9,-1.90039 2,-1.90039 z" id="shrs-icon-demarche-planninghebdo-path3420" inkscape:connector-curvature="0" sodipodi:nodetypes="sscsscssssccsssscccccszssssssczsssssssss"></path> <path d="M44,0C19.7,0,0,19.7,0,44s19.7,44,44,44s44-19.7,44-44S68.3,0,44,0z M44,86C20.8,86,2,67.2,2,44S20.8,2,44,2 s42,18.8,42,42S67.2,86,44,86z" id="shrs-icon-demarche-planninghebdo-path3422"></path> <path inkscape:connector-curvature="0" d="m 23.099609,50.099609 v 23.9 c 2.7,1.9 5.7,3.4 8.9,4.5 h 24 c 4,-1.4 7.7,-3.4 10.9,-6 v -22.4 z m 36.5,4.100391 c 0,-0.2 0.2,-0.4 0.4,-0.4 h 3.8 c 0.2,0 0.4,0.2 0.4,0.4 V 73 c 0,0.2 -0.2,0.4 -0.4,0.4 h -3.8 c -0.2,0 -0.4,-0.2 -0.4,-0.4 z M 30.100196,72.999609 c 0,0.2 -0.100391,0.4 -0.300392,0.4 h -3.8 v 0 c -0.2,0 -0.398936,-0.200003 -0.4,-0.4 v -18.8 0 c 0,-0.2 0.2,-0.4 0.4,-0.4 h 3.7 v 0 c 0.2,0 0.401064,0.200004 0.4,0.4 v 18.8 z M 37.2,73 v 0 c 0,0.3 -0.2,0.4 -0.4,0.4 H 33 v 0 c -0.2,0 -0.401069,-0.200003 -0.4,-0.4 V 54.299609 54.2 c 0,-0.2 0.2,-0.4 0.4,-0.4 h 3.8 v 0 c 0.2,0 0.4,0.2 0.4,0.4 z m 6.7,0 v 0 c 0,0.2 -0.2,0.4 -0.4,0.4 h -3.7 v 0 c -0.2,0 -0.4,-0.2 -0.4,-0.4 v -18.8 0 c 0,-0.2 0.2,-0.4 0.4,-0.4 h 3.7 c 0.2,0 0.4,0.200001 0.4,0.4 z m 6.8,0 v 0 c 0,0.2 -0.2,0.4 -0.4,0.4 h -3.8 v 0 c -0.2,0 -0.4,-0.2 -0.4,-0.4 V 54.2 c 0,-0.2 0.2,-0.4 0.4,-0.4 h 3.8 c 0.2,0 0.4,0.2 0.4,0.4 z m 6.8,0 c 0,0.2 -0.2,0.4 -0.4,0.4 h -3.8 c -0.2,0 -0.4,-0.2 -0.4,-0.4 V 54.2 c 0,-0.2 0.2,-0.4 0.4,-0.4 h 3.8 c 0.2,0 0.4,0.2 0.4,0.4 z" id="shrs-icon-demarche-planninghebdo-path3893" sodipodi:nodetypes="cccccccssssssssscsccsccsccsccccsccsccsccscccsccsccssscccsccssssscsssssssss"></path> </symbol>
	<symbol viewBox="0 0 88 88" id="shrs-icon-demarche-daytasummary"><metadata id="shrs-icon-demarche-daytasummary-metadata3433"> <rdf:rdf> <cc:work rdf:about=""> <dc:format>image/svg+xml</dc:format> <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type> </cc:work> </rdf:rdf> </metadata>  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="shrs-icon-demarche-daytasummary-namedview3429" showgrid="false" inkscape:zoom="1.3409091" inkscape:cx="55.783937" inkscape:cy="59.926498" inkscape:window-x="-8" inkscape:window-y="224" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-teamplanning"></sodipodi:namedview>  <path d="M 44,7.5 C 23.8,7.5 7.5,23.8 7.5,44 c 0,10.8 4.700391,20.7 12.400391,27.5 V 40.900391 c 0,-1.7 1.499218,-3.09961 3.199218,-3.09961 h 5.5 V 43 c 0,2 1.59961,3.5 3.59961,3.5 h 0.701172 c 1.9,0 3.5,-1.6 3.5,-3.5 V 37.800781 H 53.599609 V 43 c 0,2.034377 1.595872,3.49303 3.562974,3.463804 0.972332,-0.01445 -0.459544,0.01882 0.631015,0.01289 C 59.758536,46.466004 61.400391,44.73192 61.400391,43 v -5.199219 h 5.5 C 69.090306,37.745141 70.050442,39.306154 70,40.900391 V 69.5 C 77.8,61.6 81.899609,50.3 80.099609,38.5 77.399609,20.7 62.1,7.5 44,7.5 Z m 13.2,26.4 c 0.879008,-0.01431 0.7,0 0.7,0 1.116736,0.02252 1.969837,0.820596 2,1.901 0.05526,1.979289 0,7.199 0,7.199 -0.01165,1.018326 -0.975094,1.882174 -2,1.9 -0.233557,0.0041 -0.7,0 -0.7,0 -0.515506,-0.02427 -1.982705,-0.455441 -2,-1.9 -0.01776,-1.483197 0,-7.199 0,-7.199 5.49e-4,-1.361762 1.120992,-1.886687 2,-1.901 z m -25.000781,3.91e-4 h 0.701172 c 1.1,0 2,0.80039 2,1.90039 V 43 c 0,1.1 -0.9,1.900391 -2,1.900391 h -0.701172 c -1.1,0 -2,-0.800391 -2,-1.900391 v -7.199219 c 0,-1.1 0.9,-1.90039 2,-1.90039 z" id="shrs-icon-demarche-daytasummary-path3420" inkscape:connector-curvature="0" sodipodi:nodetypes="sscsscssssccsssscccccszssssssczsssssssss"></path> <path d="M44,0C19.7,0,0,19.7,0,44s19.7,44,44,44s44-19.7,44-44S68.3,0,44,0z M44,86C20.8,86,2,67.2,2,44S20.8,2,44,2 s42,18.8,42,42S67.2,86,44,86z" id="shrs-icon-demarche-daytasummary-path3422"></path> <path d="M 23.099609,50.099609 V 74 C 25.799609,75.9 28.8,77.4 32,78.5 h 24 c 4,-1.4 7.700391,-3.4 10.900391,-6 V 50.099609 Z m 28.40184,7.574219 c 0.690838,0.0056 1.377639,0.110088 2.041016,0.310547 0.663132,0.193818 1.28755,0.509334 1.84375,0.931641 0.524707,0.439841 0.978351,0.964037 1.34375,1.552734 0.345715,0.647973 0.517715,1.380488 0.498047,2.121094 0.02329,0.803382 -0.184822,1.595691 -0.597656,2.27539 -0.397939,0.682046 -1.044774,1.167526 -1.792969,1.345704 0.882011,0.208379 1.671009,0.718786 2.240234,1.449218 0.521473,0.753064 0.800397,1.658198 0.796875,2.585938 0.0013,0.876784 -0.185511,2.022504 -0.546875,2.814817 -0.335331,0.699903 -0.810802,1.317217 -1.394531,1.810547 -0.611237,0.500316 -1.301985,0.88568 -2.041016,1.138672 -0.77807,0.229628 -1.581857,0.351772 -2.390625,0.363281 -0.894628,0.0011 -1.784267,-0.138455 -2.638672,-0.414062 -0.7471,-0.275293 -1.427098,-0.717292 -1.992187,-1.294922 C 46.291221,74.107995 45.847389,73.416475 45.575668,72.646849 45.262252,71.770061 45.11052,70.560132 45.128402,69.625 h 3.335938 c -0.07543,0.445144 -0.07543,0.900559 0,1.345703 0.110889,0.412263 0.296504,1.078399 0.546875,1.418333 0.259728,0.324846 0.582518,0.589069 0.947265,0.775391 0.410705,0.175262 0.850946,0.263576 1.294922,0.259765 0.802947,0.07579 1.599022,-0.206851 2.189454,-0.777343 0.557232,-0.504115 0.852642,-1.531712 0.796874,-2.29724 0.04095,-0.542444 -0.08047,-1.08478 -0.347656,-1.552734 -0.260303,-0.345336 -0.603988,-0.612871 -0.996094,-0.775391 -0.412263,-0.169553 -0.851541,-0.257011 -1.294921,-0.257812 h -1.294922 v -2.535156 h 1.195312 c 0.393499,-0.048 0.778684,-0.152512 1.144531,-0.310547 0.359514,-0.149662 0.68292,-0.379714 0.947266,-0.673828 0.231381,-0.368586 0.352367,-0.800194 0.347656,-1.240235 0.03615,-0.676757 -0.241115,-1.330718 -0.746093,-1.759765 -0.475075,-0.410305 -1.075675,-0.630594 -1.69336,-0.621094 -0.772993,-0.05973 -1.522613,0.290829 -1.992187,0.931641 -0.462525,0.657813 -0.691019,1.462027 -0.646485,2.27539 h -3.287109 c 0.01453,-0.864974 0.166307,-1.72153 0.449219,-2.535156 0.279138,-0.710405 0.684656,-1.359764 1.195312,-1.914063 0.526764,-0.551854 1.152483,-0.991118 1.841797,-1.292968 0.786037,-0.275086 1.610032,-0.414948 2.439453,-0.414063 z m -14.115234,0.002 c 0.05469,-1.11e-4 0.109385,5.4e-4 0.164062,0.002 0.746019,-0.01385 1.488668,0.111031 2.195313,0.369141 0.676967,0.239841 1.306294,0.614125 1.855469,1.103516 0.52714,0.501166 0.957816,1.109023 1.267578,1.789062 0.295212,0.748354 0.444782,1.554035 0.439453,2.367188 0.01525,0.808412 -0.117487,1.612137 -0.390625,2.365234 -0.270851,0.635714 -0.632683,1.221632 -1.072266,1.736328 -0.446159,0.525947 -0.955168,0.985697 -1.513672,1.367188 l -1.757812,1.263671 -1.658203,1.314454 c -0.53871,0.4556 -1.001111,1.007178 -1.367188,1.630859 h 7.90625 v 3.154297 H 31.155746 c -0.0082,-0.945514 0.123686,-1.886378 0.390625,-2.78711 0.296144,-0.741294 0.690914,-1.432142 1.171875,-2.050781 0.480656,-0.639059 1.039071,-1.205358 1.660156,-1.683593 l 2.048828,-1.578126 1.171876,-0.841796 1.169921,-0.945313 c 0.36801,-0.394039 0.665899,-0.857421 0.878907,-1.367187 0.238154,-0.447357 0.356474,-0.957148 0.341796,-1.472657 0.156047,-1.561374 -1.034445,-2.895771 -2.488281,-2.789062 -0.448584,-0.02305 -0.892584,0.106055 -1.269531,0.369141 -0.336765,0.247648 -0.619915,0.570772 -0.830078,0.947265 -0.197158,0.434941 -0.344717,0.894012 -0.439453,1.367188 -0.04808,0.489518 -0.04808,0.983138 0,1.472656 h -3.220703 c -0.056,-0.975139 0.04308,-1.95342 0.292968,-2.892578 0.205844,-0.749513 0.518846,-1.459864 0.927735,-2.105469 0.516157,-0.694854 1.187503,-1.237172 1.951172,-1.576172 0.783068,-0.352904 1.623882,-0.532889 2.472656,-0.529297 z" id="shrs-icon-demarche-daytasummary-path3424" inkscape:connector-curvature="0" sodipodi:nodetypes="ccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccc"></path> </symbol>
	<symbol viewBox="0 0 88 88" id="shrs-icon-demarche-mydaytasummary"><metadata id="shrs-icon-demarche-mydaytasummary-metadata3433"> <rdf:rdf> <cc:work rdf:about=""> <dc:format>image/svg+xml</dc:format> <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type> </cc:work> </rdf:rdf> </metadata>  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="shrs-icon-demarche-mydaytasummary-namedview3429" showgrid="false" inkscape:zoom="1.3409091" inkscape:cx="55.783937" inkscape:cy="59.926498" inkscape:window-x="-8" inkscape:window-y="224" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-teamplanning"></sodipodi:namedview>  <path d="M 44,7.5 C 23.8,7.5 7.5,23.8 7.5,44 c 0,10.8 4.700391,20.7 12.400391,27.5 V 40.900391 c 0,-1.7 1.499218,-3.09961 3.199218,-3.09961 h 5.5 V 43 c 0,2 1.59961,3.5 3.59961,3.5 h 0.701172 c 1.9,0 3.5,-1.6 3.5,-3.5 V 37.800781 H 53.599609 V 43 c 0,2.034377 1.595872,3.49303 3.562974,3.463804 0.972332,-0.01445 -0.459544,0.01882 0.631015,0.01289 C 59.758536,46.466004 61.400391,44.73192 61.400391,43 v -5.199219 h 5.5 C 69.090306,37.745141 70.050442,39.306154 70,40.900391 V 69.5 C 77.8,61.6 81.899609,50.3 80.099609,38.5 77.399609,20.7 62.1,7.5 44,7.5 Z m 13.2,26.4 c 0.879008,-0.01431 0.7,0 0.7,0 1.116736,0.02252 1.969837,0.820596 2,1.901 0.05526,1.979289 0,7.199 0,7.199 -0.01165,1.018326 -0.975094,1.882174 -2,1.9 -0.233557,0.0041 -0.7,0 -0.7,0 -0.515506,-0.02427 -1.982705,-0.455441 -2,-1.9 -0.01776,-1.483197 0,-7.199 0,-7.199 5.49e-4,-1.361762 1.120992,-1.886687 2,-1.901 z m -25.000781,3.91e-4 h 0.701172 c 1.1,0 2,0.80039 2,1.90039 V 43 c 0,1.1 -0.9,1.900391 -2,1.900391 h -0.701172 c -1.1,0 -2,-0.800391 -2,-1.900391 v -7.199219 c 0,-1.1 0.9,-1.90039 2,-1.90039 z" id="shrs-icon-demarche-mydaytasummary-path3420" inkscape:connector-curvature="0" sodipodi:nodetypes="sscsscssssccsssscccccszssssssczsssssssss"></path> <path d="M44,0C19.7,0,0,19.7,0,44s19.7,44,44,44s44-19.7,44-44S68.3,0,44,0z M44,86C20.8,86,2,67.2,2,44S20.8,2,44,2 s42,18.8,42,42S67.2,86,44,86z" id="shrs-icon-demarche-mydaytasummary-path3422"></path> <path d="M 23.099609,50.099609 V 74 C 25.799609,75.9 28.8,77.4 32,78.5 h 24 c 4,-1.4 7.700391,-3.4 10.900391,-6 V 50.099609 Z m 28.40184,7.574219 c 0.690838,0.0056 1.377639,0.110088 2.041016,0.310547 0.663132,0.193818 1.28755,0.509334 1.84375,0.931641 0.524707,0.439841 0.978351,0.964037 1.34375,1.552734 0.345715,0.647973 0.517715,1.380488 0.498047,2.121094 0.02329,0.803382 -0.184822,1.595691 -0.597656,2.27539 -0.397939,0.682046 -1.044774,1.167526 -1.792969,1.345704 0.882011,0.208379 1.671009,0.718786 2.240234,1.449218 0.521473,0.753064 0.800397,1.658198 0.796875,2.585938 0.0013,0.876784 -0.185511,2.022504 -0.546875,2.814817 -0.335331,0.699903 -0.810802,1.317217 -1.394531,1.810547 -0.611237,0.500316 -1.301985,0.88568 -2.041016,1.138672 -0.77807,0.229628 -1.581857,0.351772 -2.390625,0.363281 -0.894628,0.0011 -1.784267,-0.138455 -2.638672,-0.414062 -0.7471,-0.275293 -1.427098,-0.717292 -1.992187,-1.294922 C 46.291221,74.107995 45.847389,73.416475 45.575668,72.646849 45.262252,71.770061 45.11052,70.560132 45.128402,69.625 h 3.335938 c -0.07543,0.445144 -0.07543,0.900559 0,1.345703 0.110889,0.412263 0.296504,1.078399 0.546875,1.418333 0.259728,0.324846 0.582518,0.589069 0.947265,0.775391 0.410705,0.175262 0.850946,0.263576 1.294922,0.259765 0.802947,0.07579 1.599022,-0.206851 2.189454,-0.777343 0.557232,-0.504115 0.852642,-1.531712 0.796874,-2.29724 0.04095,-0.542444 -0.08047,-1.08478 -0.347656,-1.552734 -0.260303,-0.345336 -0.603988,-0.612871 -0.996094,-0.775391 -0.412263,-0.169553 -0.851541,-0.257011 -1.294921,-0.257812 h -1.294922 v -2.535156 h 1.195312 c 0.393499,-0.048 0.778684,-0.152512 1.144531,-0.310547 0.359514,-0.149662 0.68292,-0.379714 0.947266,-0.673828 0.231381,-0.368586 0.352367,-0.800194 0.347656,-1.240235 0.03615,-0.676757 -0.241115,-1.330718 -0.746093,-1.759765 -0.475075,-0.410305 -1.075675,-0.630594 -1.69336,-0.621094 -0.772993,-0.05973 -1.522613,0.290829 -1.992187,0.931641 -0.462525,0.657813 -0.691019,1.462027 -0.646485,2.27539 h -3.287109 c 0.01453,-0.864974 0.166307,-1.72153 0.449219,-2.535156 0.279138,-0.710405 0.684656,-1.359764 1.195312,-1.914063 0.526764,-0.551854 1.152483,-0.991118 1.841797,-1.292968 0.786037,-0.275086 1.610032,-0.414948 2.439453,-0.414063 z m -14.115234,0.002 c 0.05469,-1.11e-4 0.109385,5.4e-4 0.164062,0.002 0.746019,-0.01385 1.488668,0.111031 2.195313,0.369141 0.676967,0.239841 1.306294,0.614125 1.855469,1.103516 0.52714,0.501166 0.957816,1.109023 1.267578,1.789062 0.295212,0.748354 0.444782,1.554035 0.439453,2.367188 0.01525,0.808412 -0.117487,1.612137 -0.390625,2.365234 -0.270851,0.635714 -0.632683,1.221632 -1.072266,1.736328 -0.446159,0.525947 -0.955168,0.985697 -1.513672,1.367188 l -1.757812,1.263671 -1.658203,1.314454 c -0.53871,0.4556 -1.001111,1.007178 -1.367188,1.630859 h 7.90625 v 3.154297 H 31.155746 c -0.0082,-0.945514 0.123686,-1.886378 0.390625,-2.78711 0.296144,-0.741294 0.690914,-1.432142 1.171875,-2.050781 0.480656,-0.639059 1.039071,-1.205358 1.660156,-1.683593 l 2.048828,-1.578126 1.171876,-0.841796 1.169921,-0.945313 c 0.36801,-0.394039 0.665899,-0.857421 0.878907,-1.367187 0.238154,-0.447357 0.356474,-0.957148 0.341796,-1.472657 0.156047,-1.561374 -1.034445,-2.895771 -2.488281,-2.789062 -0.448584,-0.02305 -0.892584,0.106055 -1.269531,0.369141 -0.336765,0.247648 -0.619915,0.570772 -0.830078,0.947265 -0.197158,0.434941 -0.344717,0.894012 -0.439453,1.367188 -0.04808,0.489518 -0.04808,0.983138 0,1.472656 h -3.220703 c -0.056,-0.975139 0.04308,-1.95342 0.292968,-2.892578 0.205844,-0.749513 0.518846,-1.459864 0.927735,-2.105469 0.516157,-0.694854 1.187503,-1.237172 1.951172,-1.576172 0.783068,-0.352904 1.623882,-0.532889 2.472656,-0.529297 z" id="shrs-icon-demarche-mydaytasummary-path3424" inkscape:connector-curvature="0" sodipodi:nodetypes="ccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccccc"></path> </symbol>
	<symbol viewBox="0 0 88 88" id="shrs-icon-demarche-managetacounters"><metadata id="shrs-icon-demarche-CompteurTpsWequipe-metadata7207"> <rdf:rdf> <cc:work rdf:about=""> <dc:format>image/svg+xml</dc:format> <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type> </cc:work> </rdf:rdf> </metadata>  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1119" inkscape:window-height="766" id="shrs-icon-demarche-CompteurTpsWequipe-namedview7203" showgrid="false" inkscape:zoom="2.8284271" inkscape:cx="42.091239" inkscape:cy="53.880199" inkscape:window-x="232" inkscape:window-y="0" inkscape:window-maximized="0" inkscape:current-layer="shrs-icon-demarche-teamplanning"></sodipodi:namedview>  <path d="M 44,7.5 C 23.8,7.5 7.5,23.8 7.5,44 7.5,54.8 10.457089,61.940644 14.796875,66.396484 14.729962,38.921775 38.467431,32.956576 48.962891,34.789062 48.975219,34.991546 49,35.200091 49,34.794922 c 0,-7.066949 5.899219,-12.595703 13.199219,-12.595703 7.3,0 13.016966,5.903496 13.201172,13.201172 0.131833,5.222834 -3.797907,9.680727 -7.31836,11.570312 0,0 1.537914,1.225191 3.369141,7.748047 1.234097,4.39587 0.75,11.480469 0.75,11.480469 C 74.548358,63.802211 81.899609,50.3 80.099609,38.5 77.399609,20.7 62.1,7.5 44,7.5 Z m -1.712891,29.537109 -0.04687,8.308594 4.949219,0.02734 0.04687,-8.308594 z M 60.016601,47.682617 59.5,48.181641 56.560547,51.117188 60.056641,54.619141 65.9375,48.75 64.844727,47.638672 c -1.946707,0.438377 -3.181085,0.492666 -4.828126,0.04395 z m -34.45996,-2.405273 -3.453125,3.544922 5.953125,5.796875 3.453125,-3.546875 z m -8.410157,18.328125 v 4.949219 h 8.308594 v -4.949219 z m 44,0 v 4.949219 h 8.308594 v -4.949219 z" id="shrs-icon-demarche-CompteurTpsWequipe-path7194" inkscape:connector-curvature="0" sodipodi:nodetypes="ssccssscsccsccccccccccccccccccccccccccc"></path> <path d="M44,0C19.7,0,0,19.7,0,44s19.7,44,44,44s44-19.7,44-44S68.3,0,44,0z M44,86C20.8,86,2,67.2,2,44S20.8,2,44,2 s42,18.8,42,42S67.2,86,44,86z" id="shrs-icon-demarche-CompteurTpsWequipe-path7196"></path> <path d="M62.2,24.2c-6.2,0-11.2,5-11.2,11.2s5,11.2,11.2,11.2c6.2,0,11.2-5,11.2-11.2S68.4,24.2,62.2,24.2z M57.5,37.8 V39h-3.4v-1c0-0.2,0.1-0.4,0.3-0.5c0.6-0.5,1.3-1,2.1-1.3c0,0,0,0,0-0.1v-1.4c-0.2-0.1-0.3-0.3-0.3-0.5v-1.5c0-0.7,0.6-1.3,1.3-1.3 h0.3c0.7,0,1.3,0.6,1.3,1.3v1.5c0,0.2-0.1,0.4-0.3,0.5V36l0,0.1c-0.3,0.2-0.6,0.4-0.8,0.6C57.7,37,57.5,37.4,57.5,37.8z M66.4,39 h-3.6l-0.4-1.7c0.7-1,0-1.1-0.2-1.1s-0.9,0.1-0.2,1.1L61.7,39h-3.6v-1.2c0-0.2,0.1-0.5,0.3-0.6c0.8-0.6,1.6-1.1,2.4-1.6 c0,0,0,0,0-0.1v-1.7c-0.2-0.1-0.3-0.4-0.3-0.6v-1.7c0-0.8,0.7-1.5,1.5-1.5h0.4c0,0,0,0,0,0c0.9,0,1.6,0.6,1.6,1.5v1.7 c0,0.3-0.1,0.5-0.4,0.6v1.7c0,0,0,0,0,0.1c0.9,0.4,1.7,1,2.5,1.6c0.2,0.2,0.3,0.4,0.3,0.6V39z M67,39v-1.2c0-0.4-0.2-0.8-0.5-1.1 c-0.3-0.2-0.6-0.4-0.8-0.6v-1.4c-0.2-0.1-0.3-0.3-0.3-0.5v-1.5c0-0.7,0.6-1.3,1.3-1.3H67c0.7,0,1.3,0.6,1.3,1.3v1.5 c0,0.2-0.1,0.4-0.3,0.5V36c0,0,0,0,0,0.1c0,0,0,0,0.1,0c0.7,0.4,1.4,0.8,2.1,1.3c0.2,0.1,0.2,0.3,0.2,0.6v1H67z" id="shrs-icon-demarche-CompteurTpsWequipe-path7200"></path> <g transform="matrix(-0.73686,0.67604538,0.67604538,0.73686,29.588949,-13.461377)" id="shrs-icon-demarche-CompteurTpsWequipe-g7779-4-1-0"> <path sodipodi:nodetypes="cccc" inkscape:connector-curvature="0" id="shrs-icon-demarche-CompteurTpsWequipe-path7200-3-0-1-1" d="M 36.519613,43.762875 51.583216,69.55285 39.907427,73.076605 Z" style="stroke-width:2.31947732"></path> </g> <path inkscape:connector-curvature="0" d="m 44.812893,65.146564 c -3.508891,0 -6.338641,2.829751 -6.338641,6.338641 0,3.508891 2.82975,6.338641 6.338641,6.338641 3.508891,0 6.338641,-2.82975 6.338641,-6.338641 0,-3.50889 -2.82975,-6.338641 -6.338641,-6.338641 z" id="shrs-icon-demarche-CompteurTpsWequipe-path7200-6" sodipodi:nodetypes="sssss" style="stroke-width:0.5659501"></path> </symbol>
	
	<symbol viewBox="0 0 151.1 89.19" id="shrs-icon-nuages">
		<defs><style>.nuages-cls-1{fill:#3a738a;}</style></defs>
		<g id="Calque_2" data-name="Calque 2"><g id="Weather"><g id="_Groupe_" data-name="&lt;Groupe&gt;"><path id="_Tracé_" data-name="&lt;Tracé&gt;" class="nuages-cls-1" d="M125.8,12.41a25.81,25.81,0,0,0-3.93.3,27.75,27.75,0,0,0-48.59,3.57,36.37,36.37,0,0,1,7.27,6.24,18.74,18.74,0,0,1,35-2.74,4.5,4.5,0,0,0,5.42,2.37,16.65,16.65,0,0,1,4.87-.73,16.3,16.3,0,0,1,0,32.59h-5.68a32.69,32.69,0,0,1,.36,4.88,33.06,33.06,0,0,1-.26,4.13h5.58a25.31,25.31,0,0,0,0-50.61Z"></path><path id="_Tracé_transparent_" data-name="&lt;Tracé transparent&gt;" class="nuages-cls-1" d="M87.48,28.59a30.46,30.46,0,0,0-5.4.48A33.34,33.34,0,0,0,21,39.76a24.86,24.86,0,0,0,3.82,49.43H87.48a30.3,30.3,0,0,0,0-60.6Zm0,51.59H24.86a15.85,15.85,0,0,1,0-31.7H25a4.5,4.5,0,0,0,4.49-4.11A24.32,24.32,0,0,1,75.7,36.18a4.52,4.52,0,0,0,5.42,2.38,21.3,21.3,0,1,1,6.36,41.62Z"></path></g></g></g>
    </symbol>
	<symbol viewBox="0 0 130.54 121.11" id="shrs-icon-orage">
		<defs><style>.orage-cls-1{fill:#3a738a;}.orage-cls-2{fill:#ff7e00;}</style></defs>
        <g id="Calque_2" data-name="Calque 2"><g id="Weather"><g id="_Groupe_" data-name="&lt;Groupe&gt;"><path id="_Tracé_" data-name="&lt;Tracé&gt;" class="orage-cls-1" d="M97,17.05a33.09,33.09,0,0,0-6,.54A37,37,0,0,0,23.32,29.44a27.55,27.55,0,0,0,4.23,54.78h7.93a5,5,0,0,0,0-10H27.55a17.57,17.57,0,0,1,0-35.13h.14a5,5,0,0,0,5-4.56,27,27,0,0,1,51.26-9.08,5,5,0,0,0,6,2.64,23.6,23.6,0,1,1,7,46.13,5,5,0,0,0,0,10,33.59,33.59,0,0,0,0-67.17Z"></path><polygon id="_Tracé_2" data-name="&lt;Tracé&gt;" class="orage-cls-2" points="77.14 25.9 41.8 83.15 62.8 83.15 53.4 121.11 88.74 63.87 67.74 63.87 77.14 25.9"></polygon></g></g></g>
    </symbol>
	<symbol viewBox="0 0 139.88 139.89" id="shrs-icon-soleil">
		<defs><style>.soleil-cls-1{fill:#f0c419;}</style></defs>
        <g id="Calque_2" data-name="Calque 2"><g id="Weather"><g id="_Groupe_" data-name="&lt;Groupe&gt;"><path id="_Tracé_transparent_" data-name="&lt;Tracé transparent&gt;" class="soleil-cls-1" d="M97.85,42a39.45,39.45,0,1,0,11.57,27.91A39.21,39.21,0,0,0,97.85,42ZM69.94,100.56a30.62,30.62,0,1,1,30.62-30.62A30.65,30.65,0,0,1,69.94,100.56Z"></path><rect id="_Tracé_" data-name="&lt;Tracé&gt;" class="soleil-cls-1" x="16.91" y="106.34" width="24.43" height="8.85" transform="translate(-69.79 53.03) rotate(-45)"></rect><rect id="_Tracé_2" data-name="&lt;Tracé&gt;" class="soleil-cls-1" x="98.55" y="24.7" width="24.43" height="8.85" transform="translate(11.85 86.85) rotate(-45)"></rect><rect id="_Tracé_3" data-name="&lt;Tracé&gt;" class="soleil-cls-1" y="65.52" width="24.43" height="8.85"></rect><rect id="_Tracé_4" data-name="&lt;Tracé&gt;" class="soleil-cls-1" x="115.46" y="65.52" width="24.43" height="8.85"></rect><rect id="_Tracé_5" data-name="&lt;Tracé&gt;" class="soleil-cls-1" x="24.7" y="16.91" width="8.85" height="24.43" transform="translate(-12.06 29.12) rotate(-45)"></rect><rect id="_Tracé_6" data-name="&lt;Tracé&gt;" class="soleil-cls-1" x="106.34" y="98.55" width="8.85" height="24.43" transform="translate(-45.88 110.76) rotate(-45)"></rect><rect id="_Tracé_7" data-name="&lt;Tracé&gt;" class="soleil-cls-1" x="65.52" width="8.85" height="24.43"></rect><rect id="_Tracé_8" data-name="&lt;Tracé&gt;" class="soleil-cls-1" x="65.52" y="115.46" width="8.85" height="24.43"></rect></g></g></g>
    </symbol>
	<symbol viewBox="0 0 198.1 143.47" id="shrs-icon-soleil-nuage">
		<defs><style>.soleil-nuage-cls-1{fill:#f0c419;}.soleil-nuage-cls-2{fill:#3a738a;}</style></defs>
        <g id="Calque_2" data-name="Calque 2"><g id="Weather"><g id="_Groupe_" data-name="&lt;Groupe&gt;"><rect id="_Tracé_" data-name="&lt;Tracé&gt;" class="soleil-nuage-cls-1" x="155.7" y="25.33" width="25.05" height="9.08" transform="translate(28.15 127.71) rotate(-45)"></rect><rect id="_Tracé_2" data-name="&lt;Tracé&gt;" class="soleil-nuage-cls-1" x="173.05" y="67.2" width="25.05" height="9.08"></rect><rect id="_Tracé_3" data-name="&lt;Tracé&gt;" class="soleil-nuage-cls-1" x="79.96" y="17.34" width="9.08" height="25.05" transform="translate(3.63 68.5) rotate(-45)"></rect><rect id="_Tracé_4" data-name="&lt;Tracé&gt;" class="soleil-nuage-cls-1" x="163.69" y="101.08" width="9.08" height="25.05" transform="translate(-31.06 152.23) rotate(-45)"></rect><rect id="_Tracé_5" data-name="&lt;Tracé&gt;" class="soleil-nuage-cls-1" x="121.82" width="9.08" height="25.05"></rect><rect id="_Tracé_6" data-name="&lt;Tracé&gt;" class="soleil-nuage-cls-1" x="121.82" y="118.42" width="9.08" height="25.05"></rect><path id="_Tracé_7" data-name="&lt;Tracé&gt;" class="soleil-nuage-cls-1" d="M155,43.11A40.49,40.49,0,0,0,85.88,71.74v0c.76,0,1.52-.08,2.28-.08a33.26,33.26,0,0,1,6.81.7c0-.22,0-.44,0-.65a31.41,31.41,0,1,1,31.4,31.4,31.85,31.85,0,0,1-5-.4c.05.73.07,1.47.07,2.21a33.87,33.87,0,0,1-.71,6.88,40.62,40.62,0,0,0,5.65.39A40.48,40.48,0,0,0,155,43.11Z"></path><path id="_Tracé_transparent_" data-name="&lt;Tracé transparent&gt;" class="soleil-nuage-cls-2" d="M88.16,74.41a30,30,0,0,0-5.44.49A33.6,33.6,0,0,0,21.21,85.68a25.05,25.05,0,0,0,3.84,49.81H88.16a30.54,30.54,0,1,0,0-61.08Zm0,52H25.05a16,16,0,0,1,0-31.95h.14a4.54,4.54,0,0,0,4.52-4.14A24.52,24.52,0,0,1,76.3,82.06a4.53,4.53,0,0,0,5.46,2.4,21.46,21.46,0,1,1,6.4,42Z"></path></g></g></g>
    </symbol>
     <symbol viewBox="0 0 28 28" id="shrs-icon-remove-delegation">
        <path d="M9.75,13c3.06,0,4.16-3.06,4.45-5.57C14.55,4.3,13.09,2,9.75,2S5,4.3,5.31,7.4C5.6,9.91,6.69,13,9.75,13Z"></path>
        <path d="M10.54,18.54a8.08,8.08,0,0,1,2.35-5.7A4.63,4.63,0,0,1,9.75,14a4.66,4.66,0,0,1-3.54-1.55,6.77,6.77,0,0,1-.9.39,8.81,8.81,0,0,0-2,.85c-1,.77-1.28,2.47-1.49,3.64a20.22,20.22,0,0,0-.32,2.92c0,.75.35.85,1,1.08A16.24,16.24,0,0,0,4.89,22a24.21,24.21,0,0,0,4.86.61c.62,0,1.23-.05,1.84-.11A8,8,0,0,1,10.54,18.54Z"></path><path d="M19,11.18a7.5,7.5,0,1,0,7.5,7.5A7.5,7.5,0,0,0,19,11.18Zm4.5,10.76a.77.77,0,0,1,0,1.09l-.13.13a.76.76,0,0,1-1.1,0L19,19.93l-3.26,3.25a.78.78,0,0,1-1.1,0L14.53,23a.75.75,0,0,1,0-1.09l3.25-3.26-3.26-3.26a.78.78,0,0,1,0-1.1l.13-.14a.78.78,0,0,1,1.1,0L19,17.43l3.26-3.26a.77.77,0,0,1,1.09,0l.14.13a.79.79,0,0,1,0,1.11l-3.26,3.26Z"></path>
    </symbol>
      <symbol viewBox="0 0 28 28" id="shrs-icon-add-delegation">
        <path d="M9.75,13c3.06,0,4.16-3.06,4.45-5.57C14.55,4.3,13.09,2,9.75,2S5,4.3,5.31,7.4C5.6,9.91,6.69,13,9.75,13Z"></path>
        <path d="M10.54,18.54a8.08,8.08,0,0,1,2.35-5.7A4.63,4.63,0,0,1,9.75,14a4.66,4.66,0,0,1-3.54-1.55,6.77,6.77,0,0,1-.9.39,8.81,8.81,0,0,0-2,.85c-1,.77-1.28,2.47-1.49,3.64a20.22,20.22,0,0,0-.32,2.92c0,.75.35.85,1,1.08A16.24,16.24,0,0,0,4.89,22a24.21,24.21,0,0,0,4.86.61c.62,0,1.23-.05,1.84-.11A8,8,0,0,1,10.54,18.54Z"></path><path d="M19,11.18a7.5,7.5,0,1,0,7.5,7.5A7.5,7.5,0,0,0,19,11.18Zm1.71,9.17V18.54C17.82,18.25,15,19.4,14,22.6c0-4.11,3.25-6.4,6.73-7.16V13.67L25,17Z"></path>
    </symbol>
    <symbol viewBox="0 0 28 28" id="shrs-icon-receive_delegation">
        <path d="M 9.75,13 C 12.81,13 13.91,9.94 14.2,7.43 14.55,4.3 13.09,2 9.75,2 6.41,2 5,4.3 5.31,7.4 5.6,9.91 6.69,13 9.75,13 Z"></path>
        <path d="m 10.54,18.54 a 8.08,8.08 0 0 1 2.35,-5.7 4.63,4.63 0 0 1 -3.14,1.16 4.66,4.66 0 0 1 -3.54,-1.55 6.77,6.77 0 0 1 -0.9,0.39 8.81,8.81 0 0 0 -2,0.85 c -1,0.77 -1.28,2.47 -1.49,3.64 A 20.22,20.22 0 0 0 1.5,20.25 c 0,0.75 0.35,0.85 1,1.08 A 16.24,16.24 0 0 0 4.89,22 24.21,24.21 0 0 0 9.75,22.61 c 0.62,0 1.23,-0.05 1.84,-0.11 a 8,8 0 0 1 -1.05,-3.96 z"></path>
        <path d="m 19,11.18 a 7.5,7.5 0 1 1 -7.5,7.5 7.5,7.5 0 0 1 7.5,-7.5 z m -1.71,9.17 V 18.54 C 20.18,18.25 23,19.4 24,22.6 24,18.49 20.75,16.2 17.27,15.44 V 13.67 L 13,17 Z"></path>
    </symbol>
	<symbol id="shrs-icon-demarche-ticketing" data-name="Calque 1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 88 88">
		<g transform="translate(-49.59 -100.45)">
			<g transform="matrix(.6952 0 0 .6952 20.896 46.403)">
				<path d="m104.96 87.529a53.922 53.922 0 1 0 53.922 53.922 53.922 53.922 0 0 0-53.922-53.922zm-1.1504 81.631a6.3268 6.3268 0 1 1 6.3125-6.3268 6.3412 6.3412 0 0 1-6.3125 6.3268zm6.0105-21.367a31.073 31.073 0 0 0-1.8406 4.0262 12.05 12.05 0 0 0-8.6275 0.10929 5.651 5.651 0 0 1-0.76212-2.8756c0-9.1884 13.502-11.273 13.502-18.837 0-4.1557-2.7752-6.6291-7.4052-6.6291-9.8785 0-6.0249 10.18-13.502 10.18a4.6157 4.6157 0 0 1-5.0184-4.702c0-7.5632 8.6275-14.264 18.06-14.264 9.8642 0 19.584 4.544 19.584 15.415-5e-3 10.094-11.518 13.947-13.991 17.571z" style="stroke-width:1.4379"></path>
				<g transform="matrix(1.4384 0 0 1.4384 41.275 77.737)">
					<path d="m44 0a44 44 0 1 0 44 44 44 44 0 0 0-44-44zm0 86a42 42 0 1 1 42-42 42 42 0 0 1-42 42z"></path>
				</g>
			</g>
		</g>
	</symbol> 
    <symbol viewBox="0 0 88 88" id="shrs-icon-folder-closed">
       <path d="M 83.961482,24.727226 H 4.5046071 c -4.07828228,0 -4.17775258,4.007887 -3.97881198,6.417351 l 0.0385579,44.250953 c 0.1989406,2.409464 -0.69576299,4.468199 3.28304888,4.468199 H 83.966145 c 4.078282,0 3.73665,-1.732463 3.935591,-4.141927 l -0.008,-46.530615 c 0.208887,-2.409464 0.05651,-4.463961 -3.932202,-4.463961 z M 81.037055,20.978937 C 80.539703,18.97105 78.152416,17.364741 75.655712,17.364741 h -29.84109 c -2.79171,-0.159443 -5.4499,-1.258754 -7.549796,-3.122264 L 35.678599,11.632224 C 33.582169,9.751771 30.910494,8.6533305 28.108909,8.52 H 13.864762 C 11.392288,8.605932 9.335475,10.466228 8.9807705,12.937351 L 7.6976037,24.492738 H 81.872605 Z"></path>
    </symbol>
	
	<!-- svg of question ICON-->
	
	<symbol viewBox="0 0 88 88" id="shrs-icon-demand">
        <path d="M44,6.5A37.5,37.5,0,1,0,81.5,44,37.5,37.5,0,0,0,44,6.5Zm-.8,56.77a4.4,4.4,0,1,1,4.39-4.4A4.41,4.41,0,0,1,43.2,63.27Zm4.18-14.86a21.61,21.61,0,0,0-1.28,2.8,8.38,8.38,0,0,0-6,.08,3.93,3.93,0,0,1-.53-2c0-6.39,9.39-7.84,9.39-13.1,0-2.89-1.93-4.61-5.15-4.61-6.87,0-4.19,7.08-9.39,7.08a3.21,3.21,0,0,1-3.49-3.27c0-5.26,6-9.92,12.56-9.92,6.86,0,13.62,3.16,13.62,10.72C57.09,43.21,49.1,45.89,47.38,48.41Z"></path>
    </symbol>

    <symbol viewBox="0 0 88 88" id="shrs-icon-folder-home">
        <path d="M 13.763672,6.0273438 C 11.291198,6.1132738 9.2336113,7.9741895 8.8789062,10.445312 L 7.5957031,22 H 81.771484 L 80.935547,18.486328 C 80.438195,16.478441 78.051391,14.871094 75.554688,14.871094 H 45.712891 C 42.921181,14.711651 40.263959,13.61351 38.164062,11.75 L 35.576172,9.1386719 C 33.479742,7.2582189 30.809398,6.1606748 28.007812,6.0273438 Z M 4.4023438,22.234375 c -4.07828204,0 -4.17745667,4.008505 -3.97851568,6.417969 l 0.0390625,44.25 c 0.19894001,2.409464 -0.69560886,4.46875 3.28320318,4.46875 H 83.865234 c 4.078282,0 3.736606,-1.733114 3.935547,-4.142578 l -0.0078,-46.529297 c 0.208887,-2.409464 0.05512,-4.464844 -3.933594,-4.464844 z M 44.28125,26.388672 c 0.705171,-7.85e-4 1.387962,0.219202 1.927734,0.621094 l 23.998047,18.617187 c 1.084501,0.8376 1.177987,2.301635 0.207031,3.242188 l -0.658203,0.648437 c -0.97628,0.945719 -2.26482,1.329077 -2.875,0.853516 l -1.109375,-0.865235 v 20.601563 c -2e-6,1.280906 -1.172869,2.319082 -2.61914,2.318359 h -9.988282 c -1.409939,-0.01708 -2.519464,-1.071527 -2.439453,-2.318359 V 55.488281 H 38.746094 v 14.619141 c -2e-6,1.280231 -1.17168,2.318127 -2.617188,2.318359 H 26.71875 c -1.445508,-2.32e-4 -2.617186,-1.038128 -2.617188,-2.318359 V 49.214844 L 22.332031,50.625 c -1.084773,0.783181 -2.672079,0.676731 -3.611328,-0.242188 l -0.611328,-0.541015 c -1.004732,-0.915092 -0.945994,-2.378968 0.117187,-3.242188 L 42.308594,27.046875 c 0.54552,-0.424232 1.246685,-0.658185 1.972656,-0.658203 z"></path>
        <path d="m 113.98704,28.752225 c -0.3589,-0.191328 -0.84131,-0.62442 -1.07203,-0.962427 -0.39436,-0.577742 -0.41949,-0.803895 -0.41949,-3.774913 0,-3.596213 0.14328,-4.139292 1.27832,-4.845124 l 0.6793,-0.422431 h 13.61017 c 13.27486,0 13.62577,0.0092 14.24323,0.372882 1.34466,0.792033 1.41779,1.03726 1.41779,4.754237 0,3.650611 -0.0453,3.818068 -1.28358,4.742717 -0.50748,0.378955 -0.70808,0.3851 -14.15797,0.433664 -13.01143,0.04698 -13.67341,0.03315 -14.29574,-0.298605 z"></path>
    </symbol>

    <symbol viewBox="0 0 88 88" id="shrs-icon-folder-manager">
        <path d="M 13.470703 8.5195312 A 5.14 5.14 0 0 0 8.5605469 12.919922 L 7.2695312 24.429688 L 81.839844 24.429688 L 81 20.929688 C 80.5 18.929688 78.099844 17.330078 75.589844 17.330078 L 45.589844 17.330078 A 12.57 12.57 0 0 1 38 14.220703 L 35.400391 11.619141 A 12.33 12.33 0 0 0 27.789062 8.5195312 L 13.470703 8.5195312 z M 4.0605469 30.640625 C -0.039453125 30.640625 -0.13945313 32.639062 0.060546875 35.039062 L 3.6601562 75.039062 C 3.8601563 77.439063 4.4609375 79.439453 8.4609375 79.439453 L 79.539062 79.439453 C 83.639062 79.439453 84.139844 77.439063 84.339844 75.039062 L 87.939453 35.039062 C 88.149453 32.639063 87.949453 30.640625 83.939453 30.640625 L 4.0605469 30.640625 z M 42.792969 39.466797 L 44.431641 39.466797 C 48.116242 39.466797 50.980469 41.922821 50.980469 45.607422 L 50.980469 52.566406 C 50.980469 53.794606 50.571953 54.614039 49.34375 55.023438 L 49.34375 61.982422 L 49.34375 62.392578 C 53.028351 64.030176 56.302923 66.486957 59.578125 68.943359 C 60.396922 69.76216 60.806641 70.579637 60.806641 71.398438 L 60.806641 76.3125 L 46.068359 76.3125 L 44.431641 69.351562 C 47.297441 65.257563 44.430129 64.849609 43.611328 64.849609 C 42.792528 64.849609 39.927169 65.257563 42.792969 69.351562 L 41.564453 76.3125 L 26.826172 76.3125 L 26.826172 71.398438 C 26.826172 70.579637 27.235901 69.352758 28.054688 68.943359 C 31.32989 66.486957 34.605661 64.439578 37.880859 62.392578 L 37.880859 61.982422 L 37.880859 55.023438 C 37.062059 54.614038 36.652344 53.385207 36.652344 52.566406 L 36.652344 45.607422 C 36.652344 42.33222 39.517767 39.466797 42.792969 39.466797 z M 24.371094 45.197266 L 25.599609 45.197266 C 28.465399 45.197266 30.919922 47.653731 30.919922 50.519531 L 30.919922 56.660156 C 30.919922 57.478957 30.512159 58.297629 29.693359 58.707031 L 29.693359 64.029297 L 29.693359 64.439453 C 28.46516 65.258253 27.23677 66.077684 26.417969 66.896484 C 25.18977 68.124684 24.371094 69.760837 24.371094 71.398438 L 24.371094 76.3125 L 10.451172 76.3125 L 10.451172 72.21875 C 10.451172 71.399953 10.860886 70.579324 11.679688 70.169922 C 14.136088 68.122923 17.002145 66.077812 20.277344 64.849609 L 20.277344 64.439453 L 20.277344 58.707031 C 19.458543 58.297629 19.048828 57.478957 19.048828 56.660156 L 19.048828 50.519531 C 19.048828 47.653731 21.505294 45.197266 24.371094 45.197266 z M 62.035156 45.197266 L 63.263672 45.197266 C 66.129472 45.197266 68.585938 47.653731 68.585938 50.519531 L 68.585938 56.660156 C 68.585938 57.478957 68.176223 58.297629 67.357422 58.707031 L 67.357422 64.029297 L 67.357422 64.439453 L 67.767578 64.439453 C 70.633379 66.077054 73.497481 67.714716 76.363281 69.761719 C 77.182082 70.171117 77.183594 70.990551 77.183594 72.21875 L 77.183594 76.3125 L 63.263672 76.3125 L 63.263672 71.398438 C 63.263672 69.760836 62.444999 68.124684 61.216797 66.896484 C 59.988598 66.077684 58.760206 65.258253 57.941406 64.439453 L 57.941406 58.707031 C 57.122605 58.297629 56.712891 57.478957 56.712891 56.660156 L 56.712891 50.519531 C 56.712891 47.653731 59.169356 45.197266 62.035156 45.197266 z "></path>
    </symbol>

    <symbol viewBox="0 0 88 88" id="shrs-icon-building">
        <path d="M 13.9,88.106668 13.8,79 h 4 V 3.276 C 17.8,1.4578023 19.331414,-0.00560061 21.146262,1.2314429e-6 H 67.749689 C 69.57791,-0.02233398 70.749616,1.4444433 70.771642,3.276 L 70.705,79 h 4 v 9.004594 z M 54.147828,68 H 64.88086 V 58 H 54.147828 Z m 0,-17 H 64.88086 V 41 H 54.147828 Z m 0,-17 H 64.88086 V 24 H 54.147828 Z M 24.195647,68 H 49.132392 V 58 H 24.195647 Z m 0,-17 H 49.132392 V 41 H 24.195647 Z m 0,-17 H 49.132392 V 24 H 24.195647 Z M 49.132392,7.0344135 H 24.195647 V 17 h 24.936745 z m 15.748468,0 H 54.147828 V 17 H 64.88086 Z"></path>
    </symbol>

    <symbol viewBox="0 0 88 88" id="shrs-icon-list">
        <path d="m 17.610961,74 v -9 c 13.103662,-0.04796 54.674531,-0.05087 70.451122,0 v 9 C 73.19264,74.114675 27.627761,73.92016 17.610961,74 Z m 0,-25 v -9 c 15.393439,-0.04744 50.514441,0.05216 70.471892,0 l -0.02077,9 C 70.807552,49.05661 30.784648,48.875806 17.610961,49 Z m 0,-25 v -9 c 19.358936,-0.0685 48.929533,-0.01846 70.70036,0 v 9 c -8.951356,-0.02158 -59.61768,0.01698 -70.70036,0 z"></path>
        <path d="M 0.04808103,74 V 65 C 1.78204,64.95204 7.282959,64.94913 9.370619,65 v 9 c -1.96762,0.114675 -7.997051,-0.07984 -9.32253797,0 z m 0,-25 V 40 C 2.085038,39.95256 6.732469,40.05216 9.373369,40 l -0.003,9 C 7.087139,49.05661 1.79106,48.875806 0.04783603,49 Z m 0,-25 V 15 C 2.609777,14.9315 6.522749,14.98154 9.403599,15 v 9 c -1.1845,-0.02158 -7.888988,0.01698 -9.35551797,0 z"></path>
    </symbol>

    <symbol viewBox="0 0 88 88" id="shrs-icon-thumbnail">
        <path d="M 0,73 C 0.04086,60.848024 -0.0063048,22.377149 0,17 L 88,16.767816 C 88.061463,35.303815 88.016789,62.36368 88,73 Z M 47,68 H 82 V 48 H 47 Z M 6,68 H 41 V 48 H 6 Z M 41,22 H 6 v 20 h 35 z m 41,0 H 47 v 20 c 1.097786,0.304518 35,0 35,0 z"></path>
    </symbol>

    <symbol viewBox="0 0 30.165956 35.318348" id="shrs-icon-HRa">
        <g inkscape:label="Calque 1" inkscape:groupmode="layer" id="layer1" transform="translate(-65.047974,-106.22773)">
            <g id="g825" transform="matrix(0.35277777,0,0,-0.35277777,85.817765,106.28778)">
                <path d="m 0,0 -58.875,-79.251 30.01,17.478 z" style="fill-opacity:1;fill-rule:nonzero;stroke:none" id="path827" inkscape:connector-curvature="0"></path>
            </g>
            <g id="g829" transform="matrix(0.35277777,0,0,-0.35277777,85.740789,107.38555)">
                <path d="M 0,0 -34.393,-93.708 0.109,-65.202 24.854,-90.939 Z M -36.605,-96.833 0.14,3.282 26.853,-94.461 0.031,-66.563 Z" style="fill-opacity:1;fill-rule:nonzero;stroke:none" id="path831" inkscape:connector-curvature="0"></path>
            </g>
        </g>
    </symbol>

    <symbol id="shrs-icon-onbehalf" viewBox="0 0 115.53 88">
        <g id="g840" transform="translate(-74)">
            <path d="m 826.68,485.11 19.26,-19 -19.26,-19 -5.44,5.4 13.76,13.6 -13.78,13.61 z" transform="translate(-747.47,-424.1)" id="path838" inkscape:connector-curvature="0"></path>
        </g>
        <g id="g844" transform="translate(-74)">
            <path d="m 843.74,485.11 19.26,-19 -19.26,-19 -5.44,5.4 13.78,13.61 -13.78,13.61 z" transform="translate(-747.47,-424.1)" id="path842" inkscape:connector-curvature="0"></path>
        </g>
        <g id="g848" transform="translate(45.014925)">
            <path d="m 813.66,506.64 a 70.61,70.61 0 0 1 -10.32,2.86 83.54,83.54 0 0 1 -41.36,0 70.61,70.61 0 0 1 -10.32,-2.86 c -2.68,-1 -4.27,-1.41 -4.18,-4.62 a 83.63,83.63 0 0 1 1.36,-12.41 c 0.88,-5 2,-12.23 6.34,-15.49 2.46,-1.85 5.72,-2.46 8.58,-3.61 a 34.48,34.48 0 0 0 3.83,-1.67 20.52,20.52 0 0 0 30.16,0 34.48,34.48 0 0 0 3.83,1.67 c 2.86,1.14 6.08,1.76 8.58,3.58 4.36,3.26 5.46,10.52 6.34,15.49 a 83.63,83.63 0 0 1 1.36,12.42 c 0.09,3.24 -1.5,3.68 -4.2,4.64 z m -31,-35.68 c -13,0 -17.69,-13 -18.92,-23.72 -1.5,-13.2 4.71,-23.14 18.92,-23.14 14.21,0 20.42,9.94 18.92,23.14 -1.23,10.7 -5.92,23.76 -18.92,23.76 z" transform="translate(-747.47,-424.1)" id="path846" inkscape:connector-curvature="0"></path>
        </g>
    </symbol>
	<symbol viewBox="0 0 130.54 121.11" id="shrs-icon-rain">
		<sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="namedview845" showgrid="false" inkscape:zoom="2.3996348" inkscape:cx="-54.763812" inkscape:cy="71.161664" inkscape:window-x="-8" inkscape:window-y="224" inkscape:window-maximized="1" inkscape:current-layer="svg843"></sodipodi:namedview>
		  <defs id="defs834"><style id="style832">.cls-1{fill:#3a738a;}.cls-2{fill:#ff7e00;}</style></defs>		
		<path id="rain" data-name="rain" class="cls-1" d="M97,17.05a33.09,33.09,0,0,0-6,.54A37,37,0,0,0,23.32,29.44a27.55,27.55,0,0,0,4.23,54.78h7.93a5,5,0,0,0,0-10H27.55a17.57,17.57,0,0,1,0-35.13h.14a5,5,0,0,0,5-4.56,27,27,0,0,1,51.26-9.08,5,5,0,0,0,6,2.64,23.6,23.6,0,1,1,7,46.13,5,5,0,0,0,0,10,33.59,33.59,0,0,0,0-67.17Z"></path>
		<rect id="_Tracé_2" data-name="Tracé" class="cls-1" x="-63.054218" y="81.605499" width="24.43" height="8.8500004" transform="rotate(-56.293591)" style="fill:#3a73d9;fill-opacity:1" ry="4.4250002"></rect> <rect id="_Tracé_2-8" data-name="Tracé" class="cls-1" x="-41.966579" y="113.21741" width="24.43" height="8.8500004" transform="rotate(-56.293591)" style="fill:#3a73d9;fill-opacity:1" ry="4.4250002"></rect> <rect id="_Tracé_2-8-1" data-name="Tracé" class="cls-1" x="-68.005081" y="97.624191" width="24.43" height="8.8500004" transform="rotate(-56.293591)" style="fill:#3a73d9;fill-opacity:1" ry="4.4250002"></rect> <rect id="_Tracé_2-8-11" data-name="Tracé" class="cls-1" x="-52.363426" y="97.631783" width="24.43" height="8.8500004" transform="rotate(-56.293591)" style="fill:#3a73d9;fill-opacity:1" ry="4.4250002"></rect>
   </symbol>
	<symbol viewBox="0 0 88 88" id="shrs-icon-print">
		<path d="m 17.580078,88 c -1.607164,0.01108 -2.918679,-1.28346 -2.929687,-2.890625 V 56 H 20.49 V 82.220703 H 67.490234 V 63.279297 56 h 5.859375 V 85.109375 C 73.338601,86.71654 72.027086,88.011084 70.419922,88 Z" id="path4178" inkscape:connector-curvature="0" sodipodi:nodetypes="cccccccccccc"></path>
		<path d="M 60.86,75.22 H 27.14 c -2.933332,0 -2.933332,-4.4 0,-4.4 h 33.72 c 2.933332,0 2.933332,4.4 0,4.4 z" id="path4184" inkscape:connector-curvature="0" sodipodi:nodetypes="sssss"></path>
		<path d="M 60.86,63.4 H 27.14 c -1.228372,0.0056 -2.225571,-0.991628 -2.22,-2.22 0.03227,-1.205898 1.013714,-2.169661 2.22,-2.18 h 33.72 c 1.221546,0.01086 2.209141,0.998454 2.22,2.22 -0.01645,1.212723 -1.007179,2.1856 -2.22,2.18 z" id="path4186" inkscape:connector-curvature="0" sodipodi:nodetypes="ccccccc"></path>
		<path d="m 17.580078,3e-6 c -1.607164,-0.0111 -2.918679,1.283458 -2.929687,2.890623 V 24 H 20.49 V 5.779298 H 67.490234 V 22.720704 24 h 5.859375 V 2.890626 C 73.338599,1.283461 72.027086,-0.011087 70.419922,3e-6 Z" id="path4178-1" inkscape:connector-curvature="0" sodipodi:nodetypes="cccccccccccc"></path>
		<path d="M 5,64.585844 C 1.9570314,64.61214 0.00999765,62.860934 0.01339154,59.6 L 0,24.778 c 0.01419074,-3.430846 1.7182942,-5.149218 5,-5.1735 h 78 c 3.449691,0.0256 4.994881,1.706141 5,5.173264 L 88.05141,59.6 c -0.03435,3.238117 -1.736354,5.012277 -4.635778,4.985844 H 77.022895 L 77,52.79 H 11 v 11.795844 z" id="path4186-3" inkscape:connector-curvature="0" style="stroke-width:3.13968635" sodipodi:nodetypes="cccccsccccccc"></path>
	</symbol>
	<symbol viewBox="0 0 88 88" id="shrs-icon-export1">
		<path d="m 13.580078,0 c -1.607164,-0.01108376 -2.918679,1.2834604 -2.929687,2.890625 v 82.21875 C 10.661401,86.71654 11.972914,88.011084 13.580078,88 H 52 c -3.627569,-1.573955 -6.380403,-3.55904 -8.033179,-5.779297 -10.079529,0 -17.18608,0 -27.476587,0 V 5.7792969 h 55 V 24.720703 39.360834 c 2.086645,0.851844 4.055879,1.640128 5.859375,2.996588 V 2.890625 C 77.338601,1.2834604 76.027086,-0.01108376 74.419922,0 Z" id="path4178" inkscape:connector-curvature="0" sodipodi:nodetypes="ccccccccccccccc"></path>
		<path d="M 43.05775,47.69 H 25.14 c -1.228372,-0.0056 -2.225571,0.991628 -2.22,2.22 0.01086,1.221546 0.998454,2.209141 2.22,2.22 h 14.797166 c 1.132137,-2.136342 1.839992,-3.092225 3.120584,-4.44 z" id="path4180" inkscape:connector-curvature="0" sodipodi:nodetypes="cccccc"></path>
		<path d="M 62.86,35.88 H 25.14 c -2.933332,0 -2.933332,4.4 0,4.4 H 44 l 8.176841,-0.03296 c 0.366663,-0.0015 4.456062,-1.426976 6.723109,-1.651914 1.693495,-0.227194 4.529747,-0.405261 6.080812,-0.266637 0.109347,-0.20105 0.07924,0.02651 0.07924,-0.248491 0,-1.1 -0.733333,-2.2 -2.199999,-2.2 z" id="path4182" inkscape:connector-curvature="0" sodipodi:nodetypes="csscsccssc"></path>
		<path d="M62.86,12.78H25.14a2.2,2.2,0,1,0,0,4.4H62.86A2.2,2.2,0,1,0,62.86,12.78Z" id="path4184"></path>
		<path d="M62.86,24.6H25.14a2.21,2.21,0,0,0-2.22,2.22A2.24,2.24,0,0,0,25.14,29H62.86a2.24,2.24,0,0,0,2.22-2.22A2.21,2.21,0,0,0,62.86,24.6Z" id="path4186"></path>
		<path inkscape:connector-curvature="0" d="m 63.774022,40.590385 a 24.09322,23.720339 0 1 0 24.09322,23.720339 24.09322,23.720339 0 0 0 -24.09322,-23.720339 z m 5.493254,29.002068 v -5.724509 c -9.283921,-0.917186 -18.342972,2.719933 -21.555401,12.840611 0,-12.998746 10.440395,-20.241356 21.619649,-22.645017 v -5.598 l 13.717074,10.53183 z" id="path3143" style="stroke-width:3.18747377"></path>
    </symbol>
	<symbol viewBox="0 0 88 88" id="shrs-icon-export">
		<sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="namedview4189" showgrid="false" inkscape:zoom="1.4142136" inkscape:cx="-109.28032" inkscape:cy="4.4062482" inkscape:window-x="-8" inkscape:window-y="224" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-document"></sodipodi:namedview>
		<path d="m 13.580078,0 c -1.607164,-0.01108376 -2.918679,1.2834604 -2.929687,2.890625 v 82.21875 C 10.661401,86.71654 11.972914,88.011084 13.580078,88 h 29.925546 c -3.39686,-1.738747 -6.149694,-3.526082 -8.19797,-5.746339 l -18.81742,-0.03296 V 5.7792969 h 55 v 18.9414061 2.640131 c 2.086645,0.851844 4.055879,1.640128 5.859375,2.996588 V 2.890625 C 77.338601,1.2834604 76.027086,-0.01108376 74.419922,0 Z" id="path4178" inkscape:connector-curvature="0" sodipodi:nodetypes="ccccccccccccccc"></path>
	  <path d="M 28.382103,43.69 H 25.14 c -1.228372,-0.0056 -2.225571,0.991628 -2.22,2.22 0.01086,1.221546 0.998454,2.209141 2.22,2.22 l 1.357458,10e-7 C 27.102261,46.339722 27.348699,45.74638 28.382103,43.69 Z" id="path4180" inkscape:connector-curvature="0" sodipodi:nodetypes="cccccc"></path>
	  <path d="M62.86,12.78H25.14a2.2,2.2,0,1,0,0,4.4H62.86A2.2,2.2,0,1,0,62.86,12.78Z" id="path4184"></path>
	  <path d="M 62.86,22.6 H 25.14 c -1.228372,-0.0056 -2.225571,0.991628 -2.22,2.22 0.03227,1.205898 1.013714,2.169661 2.22,2.18 0,0 22.61591,0.05277 22.743475,0 3.110677,-1.286762 6.829135,-1.847489 10.813474,-1.860169 1.653703,-0.0053 4.39507,0.269179 4.39507,0.269179 0.642023,0.01023 1.768687,0.230512 1.768687,0.230512 0,0 0.213864,-0.248749 0.219294,-0.859522 -0.01645,-1.212723 -1.007179,-2.1856 -2.22,-2.18 z" id="path4186" inkscape:connector-curvature="0" sodipodi:nodetypes="ccccsscccc"></path>
	  <path inkscape:connector-curvature="0" d="m 58.118786,28.43565 c -26.504887,0 -39.77378,32.09626 -21.034266,50.866798 8.507953,8.522038 21.302677,11.072038 32.418679,6.460194 C 80.619224,81.150789 87.867242,70.285391 87.867242,58.233356 87.867241,41.776537 74.548404,28.43565 58.118786,28.43565 Z m 6.782648,38.432663 v -9.191181 c -11.463073,-1.152177 -22.648493,3.416805 -26.614953,16.130493 0,-16.329143 12.890997,-25.427376 26.694281,-28.446877 V 36.328489 L 81.917551,51.55867 Z" id="path3143" style="stroke-width:3.96974301" sodipodi:nodetypes="sssssccccccc"></path>
	  <path d="M 37.383688,32.686265 25.14,32.732875 c -1.228372,-0.0056 -2.225571,0.991628 -2.22,2.22 0.01086,1.221546 0.998454,2.209141 2.22,2.22 l 7.646921,-0.06979 c 1.874871,-1.98476 2.096231,-2.357613 4.596767,-4.41682 z" id="path4180-9" inkscape:connector-curvature="0" sodipodi:nodetypes="cccccc"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-help">
        <path style="fill-rule:nonzero;stroke-width:2.32239795" d="m 43.175818,0.0148591 c 15.892452,0 31.555769,7.358516 31.555769,24.9591509 -0.04635,16.343448 -18.557724,22.579916 -22.54242,28.446794 -1.140935,2.099941 -2.131359,4.278997 -2.963727,6.520575 -4.497433,-1.664198 -9.448471,-1.597795 -13.900104,0.186436 -0.816802,-1.413724 -1.24178,-3.021547 -1.230739,-4.656252 0,-14.876728 21.755111,-18.251603 21.755111,-30.497548 0,-6.728285 -4.472104,-10.731207 -11.931825,-10.731207 -15.915621,0 -9.708354,16.47877 -21.755111,16.47877 -4.411719,0.386919 -8.180142,-3.162631 -8.081245,-7.611883 0,-12.245944 13.896843,-23.09483124 29.094291,-23.09483124 z M 42.035576,67.539618 c 5.800878,-0.256949 10.64326,4.400384 10.642266,10.235572 -0.01228,5.644028 -4.555441,10.219881 -10.17169,10.244664 -13.41078,0.131681 -13.87311,-19.989726 -0.470576,-20.480236 z" id="path836" inkscape:connector-curvature="0" sodipodi:nodetypes="ccccccssccsccccc"></path>
    </symbol>
     <symbol viewBox="0 0 88 88" id="shrs-icon-diagnostic">
        <path style="stroke-width:0.31622824;" d="M 40.383515,87.177601 C 33.956495,85.526053 27.906469,80.633592 24.929688,74.680603 22.736653,70.294951 22.506469,69.009027 22.32812,60.146823 l -0.160285,-7.964402 h -0.713247 c -0.392285,0 -1.604891,-0.283394 -2.694676,-0.629765 C 8.8828431,48.413392 1.7623585,39.940577 0.3831818,29.685798 0.2006732,28.32876 0.051346,21.972395 0.051346,15.560543 0.051346,2.7701396 0.080764,2.5426259 1.9347822,0.99051426 l 1.0185442,-0.85268278 6.0786347,0.001261 c 5.5376249,9.4687e-4 6.1720369,0.0578361 7.1280809,0.6377485 1.849964,1.12214762 2.460637,3.64830992 1.371733,5.67442912 -0.907559,1.6886857 -2.180772,2.2596029 -5.453182,2.4452372 L 9.2454131,9.0572251 V 19.207431 c 0,8.418358 0.08723,10.442519 0.511202,11.863421 2.5974449,8.704858 10.9794979,14.039514 19.8025829,12.603029 5.354938,-0.871838 10.393724,-4.501914 12.859291,-9.26418 1.758337,-3.396237 2.018009,-5.432732 2.018009,-15.826401 V 9.0571425 L 41.603315,8.8964247 C 38.330908,8.7107912 37.057692,8.1398741 36.150136,6.4511883 35.061229,4.4250691 35.671902,1.8989069 37.521865,0.77675925 c 0.95275,-0.57791258 1.589886,-0.63656841 6.914633,-0.63656841 9.388055,0 8.879236,-0.89817287 8.87075,15.65868816 -0.0034,6.739578 -0.150007,13.243248 -0.325673,14.452599 -1.311936,9.031896 -7.626051,17.013833 -16.241262,20.531246 -1.434132,0.585527 -3.213846,1.156109 -3.95492,1.267961 l -1.347406,0.203365 v 7.249199 c 0,8.219829 0.210065,9.420737 2.177066,12.44595 2.146916,3.301923 5.735109,5.898377 9.272609,6.709762 0.764677,0.17539 4.426484,0.322361 8.137352,0.3266 6.003986,0.0069 6.962264,-0.06443 8.701401,-0.647315 4.620415,-1.548575 8.179755,-5.098748 9.744215,-9.719115 0.992251,-2.930448 0.918814,-3.18037 -1.307362,-4.449255 -2.400523,-1.368258 -4.183786,-3.328844 -5.447314,-5.988985 -1.864927,-3.926279 -1.431497,-9.177524 1.054177,-12.771922 4.254056,-6.151567 12.702997,-7.659014 18.756236,-3.346467 2.922132,2.081834 4.672175,4.939785 5.34722,8.732382 0.734964,4.129309 -0.804171,8.519115 -4.043769,11.533356 -0.869955,0.809441 -2.314025,1.840895 -3.209024,2.292118 -1.502887,0.757691 -1.627289,0.906942 -1.627289,1.952395 0,3.208123 -2.637635,9.344565 -5.483868,12.758154 -3.103902,3.722621 -8.252883,6.910743 -12.934356,8.008632 -3.195366,0.749366 -17.078733,0.638023 -20.191766,-0.161938 z M 78.050094,58.44865 c 4.874163,-2.725475 4.107511,-10.053258 -1.243889,-11.889464 -5.297518,-1.81771 -10.360363,3.843411 -7.990891,8.935163 1.641055,3.526453 5.820456,4.863488 9.23478,2.954301 z" id="path4335" inkscape:connector-curvature="0" sodipodi:nodetypes="ssscsssscccccscscssscsccsscsccssccssssssscsssssssss"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-record">
       <path transform="matrix(1.4658116,0,0,1.4438393,-2.8735925,-2.1034041)" style="" inkscape:connector-curvature="0" d="M 32,62 A 30,30 0 1 0 2,32 30,30 0 0 0 32,62 Z M 32,6.8 A 25.2,25.2 0 1 1 6.8,32 25.2,25.2 0 0 1 32,6.8 Z" id="path1710"></path>
      <path style="stroke-width:0.66959584;" inkscape:connector-curvature="0" d="m 43.966586,64.146037 c 8.134785,0 15.468623,-4.888267 18.581495,-12.38522 C 65.660945,44.26388 63.939766,35.63477 58.18763,29.896772 52.435494,24.158774 43.785118,22.441828 36.269706,25.54704 28.75428,28.65226 23.853972,35.96807 23.853972,44.082858 c 0,11.080584 9.004724,20.063179 20.112614,20.063179 z" id="path1710-9" sodipodi:nodetypes="ssssss"></path>
    </symbol>
      <symbol viewBox="0 0 88 88" id="shrs-icon-stop">
      <path d="M 44.940013,45.090478 V 18.771759 H 19.015808 v 26.318719 z" id="path1708" inkscape:connector-curvature="0" sodipodi:nodetypes="ccccc" style="stroke-width:1.46991098"></path>
      <path inkscape:connector-curvature="0" d="M 32,62 A 30,30 0 1 0 2,32 30,30 0 0 0 32,62 Z M 32,6.8 A 25.2,25.2 0 1 1 6.8,32 25.2,25.2 0 0 1 32,6.8 Z" id="path1710"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-employeesickness">
        <g>
            <path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z" id="path897"></path>
            <path d="M 80.5,44 C 80.49998,19.462454 56.773213,1.9147581 33.311477,9.1002224 9.8497405,16.285687 0.01899397,44.110811 13.76,64.44 v -18.6 c 0.03299,-4.262109 3.51794,-7.688825 7.78,-7.65 h 1.16 v -1.84 c 0.01086,-0.450567 0.379302,-0.810131 0.83,-0.81 h 4.61 c 0.442907,0.01055 0.79945,0.367093 0.81,0.81 v 1.84 h 4.09 v -2 c 0.022,-3.168561 2.611484,-5.717726 5.78,-5.69 h 10.35 c 0.484278,-0.01401 0.806687,0.02324 1.23,0.2 0.242373,-0.995377 4.454049,-7.383663 11.8,-7.31 6.773249,-0.0046 13.210731,6.148907 13.2,12.4 -0.0097,4.647636 -1.631,6.695 -1.631,6.695 -0.229256,-0.0685 0.415786,-0.09611 0.431,3.355 v 18.6 C 78.303498,58.413211 80.498653,51.291151 80.5,44 Z" id="path899" inkscape:connector-curvature="0" sodipodi:nodetypes="cscccccccccccccccccccc"></path>
            <path d="M44,45.54A13.8,13.8,0,1,0,58,59.35,13.91,13.91,0,0,0,44,45.54ZM54.51,55.93l0,6.8a.83.83,0,0,1-.***********,0,0,1-.58.25H48.28v5.34a.81.81,0,0,1-.81.81H40.54a.81.81,0,0,1-.81-.81V63.54l-5.42,0a.82.82,0,0,1-.58-.25.81.81,0,0,1-.23-.56l0-6.8a.83.83,0,0,1,.83-.81h5.42V49.8a.81.81,0,0,1,.81-.81h6.92a.81.81,0,0,1,.81.81v5.34l5.42,0A.81.81,0,0,1,54.51,55.93Z" id="path901"></path>
            <path d="m 49,35.79 c 0.0094,-0.656501 0.35,-2.11 0.35,-2.11 H 38.83 c -1.367452,-0.0056 -2.483536,1.092636 -2.5,2.46 v 2.05 H 49.2 c 0,0 -0.21409,-1.415925 -0.2,-2.4 z" id="path903" inkscape:connector-curvature="0" sodipodi:nodetypes="scccccs"></path>
        </g>
        <path inkscape:connector-curvature="0" d="m 62.111724,25.427239 c -6.2,0 -11.2,5 -11.2,11.2 0,6.2 5,11.2 11.2,11.2 6.2,0 11.2,-5 11.2,-11.2 0,-6.2 -5,-11.2 -11.2,-11.2 z m -4.7,13.6 v 1.2 h -3.4 v -1 c 0,-0.2 0.1,-0.4 0.3,-0.5 0.6,-0.5 1.3,-1 2.1,-1.3 0,0 0,0 0,-0.1 v -1.4 c -0.2,-0.1 -0.3,-0.3 -0.3,-0.5 v -1.5 c 0,-0.7 0.6,-1.3 1.3,-1.3 h 0.3 c 0.7,0 1.3,0.6 1.3,1.3 v 1.5 c 0,0.2 -0.1,0.4 -0.3,0.5 v 1.3 0.1 c -0.3,0.2 -0.6,0.4 -0.8,0.6 -0.3,0.3 -0.5,0.7 -0.5,1.1 z m 8.9,1.2 h -3.6 l -0.4,-1.7 c 0.7,-1 0,-1.1 -0.2,-1.1 -0.2,0 -0.9,0.1 -0.2,1.1 l -0.3,1.7 h -3.6 v -1.2 c 0,-0.2 0.1,-0.5 0.3,-0.6 0.8,-0.6 1.6,-1.1 2.4,-1.6 0,0 0,0 0,-0.1 v -1.7 c -0.2,-0.1 -0.3,-0.4 -0.3,-0.6 v -1.7 c 0,-0.8 0.7,-1.5 1.5,-1.5 h 0.4 c 0,0 0,0 0,0 0.9,0 1.6,0.6 1.6,1.5 v 1.7 c 0,0.3 -0.1,0.5 -0.4,0.6 v 1.7 c 0,0 0,0 0,0.1 0.9,0.4 1.7,1 2.5,1.6 0.2,0.2 0.3,0.4 0.3,0.6 z m 0.6,0 v -1.2 c 0,-0.4 -0.2,-0.8 -0.5,-1.1 -0.3,-0.2 -0.6,-0.4 -0.8,-0.6 v -1.4 c -0.2,-0.1 -0.3,-0.3 -0.3,-0.5 v -1.5 c 0,-0.7 0.6,-1.3 1.3,-1.3 h 0.3 c 0.7,0 1.3,0.6 1.3,1.3 v 1.5 c 0,0.2 -0.1,0.4 -0.3,0.5 v 1.3 c 0,0 0,0 0,0.1 0,0 0,0 0.1,0 0.7,0.4 1.4,0.8 2.1,1.3 0.2,0.1 0.2,0.3 0.2,0.6 v 1 z" id="path840"></path>
    </symbol>
    
    <symbol viewBox="0 0 88 88" id="shrs-icon-remuneration">
    	<metadata id="shrs-icon-remuneration-metadata15"> 
    	<rdf:rdf> <cc:work rdf:about=""> <dc:format>image/svg+xml</dc:format> <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type>
     	</cc:work> </rdf:rdf> </metadata> <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="shrs-icon-remuneration-namedview13" showgrid="false" inkscape:zoom="5.3636364" inkscape:cx="28.718684" inkscape:cy="44.583387" inkscape:window-x="-8" inkscape:window-y="224" inkscape:window-maximized="1" inkscape:current-layer="Calque_1"></sodipodi:namedview> 
       <path style="fill-opacity:1;stroke:none;stroke-width:0.44688237" d="M 34.754273,87.421397 C 30.544978,86.50097 25.052367,84.283213 21.156933,81.931192 18.004106,80.027547 12.69046,75.497609 10.805654,73.106619 l -1.4583163,-1.849965 1.2635003,4.48e-4 c 0.694926,3.8e-4 4.473488,0.691647 8.396806,1.536239 11.607232,2.498734 13.853211,2.726898 17.912043,1.819631 6.515925,-1.456498 20.356615,-6.579045 22.143736,-8.195568 2.756968,-2.493797 1.19802,-6.35914 -2.564731,-6.35914 -0.567291,0 -5.021736,1.02127 -9.898777,2.269492 -11.386877,2.914337 -11.774146,2.894909 -18.414369,-0.92375 -2.718376,-1.563288 -4.942502,-3.116644 -4.942502,-3.451905 0,-1.374146 1.571074,-0.978923 6.418978,1.614772 2.807569,1.50209 5.530333,2.731069 6.050586,2.731069 0.520252,0 1.438354,-0.494769 2.040227,-1.09948 1.482541,-1.48954 1.431753,-3.343903 -0.131723,-4.809437 C 35.504838,54.405324 29.84113,50.54218 26.451332,48.770253 23.61132,47.285719 22.806493,47.06813 20.155399,47.06813 c -1.870798,0 -4.73318,0.453494 -7.443689,1.179321 -2.422213,0.648624 -6.1093304,1.636577 -8.1935927,2.195449 L 0.7285484,51.459027 0.3260594,48.031756 C 0.06537143,45.811948 0.09506465,42.946599 0.41034049,39.898735 2.3485781,21.161229 15.190759,6.3331517 33.835041,1.3052885 38.926486,-0.06773755 48.717704,-0.07366505 54.1724,1.2929763 69.924843,5.2396549 81.72864,16.551416 86.382079,32.160008 c 1.587365,5.324366 1.992091,15.140668 0.856415,20.771899 -3.279889,16.263307 -15.826401,29.746635 -31.672661,34.037575 -5.166466,1.399006 -15.45829,1.622487 -20.81156,0.451915 z M 51.501392,56.30149 c 2.267031,-0.951698 3.618609,-3.175179 3.618609,-5.952979 0,-3.865614 -2.671498,-6.588214 -6.464556,-6.588214 -5.038592,0 -8.054885,5.040618 -5.712176,9.54579 1.68213,3.234834 5.120793,4.438387 8.558123,2.995403 z M 37.500768,47.17942 c 4.929318,-2.488265 4.739975,-9.602032 -0.315373,-11.848806 -3.813997,-1.695073 -7.893804,0.300508 -8.992051,4.398339 -1.455757,5.431777 4.253246,10.001756 9.307424,7.450467 z M 52.659044,32.845478 c 4.850748,-2.448604 4.783415,-9.541173 -0.111781,-11.77382 -2.359743,-1.076255 -3.194731,-1.076255 -5.554474,0 -4.895197,2.232647 -4.962534,9.325216 -0.11178,11.77382 0.85334,0.430754 2.153398,0.783191 2.88902,0.783191 0.735622,0 2.035681,-0.352437 2.88902,-0.783191 z" id="shrs-icon-remuneration-path825" inkscape:connector-curvature="0"></path> <path style="fill-opacity:1;stroke:none;stroke-width:0.37288135" d="" id="shrs-icon-remuneration-path827" inkscape:connector-curvature="0"></path> 
   	</symbol>
       <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-allowance">
		 <g id="g1285">
		    <path d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z" id="path1275"></path>
		    <path d="M64.47,40.38H22.13a8.12,8.12,0,0,1-7.56,8V61.91a8.12,8.12,0,0,1,7.92,8H64.83s0,0,0-.07A8.13,8.13,0,0,1,73,61.72l.48,0V48.22a7.64,7.64,0,0,1-.84.06A8.12,8.12,0,0,1,64.47,40.38ZM41,62.5a7.35,7.35,0,1,1,7.35-7.35A7.35,7.35,0,0,1,41,62.5Zm11.79-5a2.3,2.3,0,1,1,2.3-2.3A2.3,2.3,0,0,1,52.83,57.45Z" id="path1277"></path>
		    <path d="M80.5,44A36.5,36.5,0,1,0,11.61,60.83V39.64a2.23,2.23,0,0,1,2.22-2.22H74.17a2.23,2.23,0,0,1,2.22,2.22V60.83A36.33,36.33,0,0,0,80.5,44Z" id="path1279"></path>
		    <path d="M23.82,74.41H64.18c.73-.49,1.44-1,2.14-1.54H21.69C22.38,73.41,23.09,73.93,23.82,74.41Z" id="path1281"></path>
		    <path d="M31.44,78.28H56.56a36.26,36.26,0,0,0,3.78-1.65H27.65A36.26,36.26,0,0,0,31.44,78.28Z" id="path1283"></path>
		 </g>
		<ellipse style="fill:#ffffff;fill-opacity:1;stroke:none;stroke-width:1.08373654" id="path2401" cx="56.888069" cy="26.625185" rx="13.873135" ry="13.791046"></ellipse>
  		<path inkscape:connector-curvature="0" d="m 56.970141,15.425183 c -6.199994,0 -11.199994,4.999999 -11.199994,11.199999 0,6.2 5,11.2 11.199994,11.2 6.2,0 11.2,-5 11.2,-11.2 0,-6.2 -5,-11.199999 -11.2,-11.199999 z m -4.699994,13.599999 v 1.2 h -3.4 v -1 c 0,-0.2 0.1,-0.4 0.3,-0.5 0.6,-0.5 1.3,-1 2.1,-1.3 0,0 0,0 0,-0.1 v -1.4 c -0.2,-0.1 -0.3,-0.3 -0.3,-0.5 v -1.5 c 0,-0.7 0.6,-1.3 1.3,-1.3 h 0.3 c 0.699997,0 1.299997,0.6 1.299997,1.3 v 1.5 c 0,0.2 -0.1,0.4 -0.3,0.5 v 1.3 0.1 c -0.3,0.2 -0.599997,0.4 -0.799997,0.6 -0.3,0.3 -0.5,0.7 -0.5,1.1 z m 8.899994,1.2 h -3.6 l -0.4,-1.7 c 0.7,-1 0,-1.1 -0.2,-1.1 -0.2,0 -0.9,0.1 -0.2,1.1 l -0.3,1.7 h -3.599994 v -1.2 c 0,-0.2 0.1,-0.5 0.299997,-0.6 0.8,-0.6 1.600001,-1.1 2.400001,-1.6 0,0 0,0 0,-0.1 v -1.7 c -0.2,-0.1 -0.3,-0.4 -0.3,-0.6 v -1.7 c 0,-0.8 0.699996,-1.5 1.499996,-1.5 h 0.4 c 0,0 0,0 0,0 0.9,0 1.6,0.6 1.6,1.5 v 1.7 c 0,0.3 -0.1,0.5 -0.4,0.6 v 1.7 c 0,0 0,0 0,0.1 0.9,0.4 1.7,1 2.5,1.6 0.2,0.2 0.3,0.4 0.3,0.6 z m 0.6,0 v -1.2 c 0,-0.4 -0.2,-0.8 -0.5,-1.1 -0.3,-0.2 -0.6,-0.4 -0.8,-0.6 v -1.4 c -0.2,-0.1 -0.3,-0.3 -0.3,-0.5 v -1.5 c 0,-0.7 0.6,-1.3 1.3,-1.3 h 0.3 c 0.7,0 1.3,0.6 1.3,1.3 v 1.5 c 0,0.2 -0.1,0.4 -0.3,0.5 v 1.3 c 0,0 0,0 0,0.1 0,0 0,0 0.1,0 0.7,0.4 1.4,0.8 2.1,1.3 0.2,0.1 0.2,0.3 0.2,0.6 v 1 z" id="path1830"></path>
    </symbol>
        <symbol viewBox="0 0 88 88" id="Logo-4YOU">
    	<title>Logo 4YOU</title> 
    	<metadata id="Logo-4YOU-metadata1292"> 
    		<rdf:rdf> 
    			<cc:work rdf:about=""> 
	    			<dc:format>image/svg+xml</dc:format> 
	    			<dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type> 
	    			<dc:title>Logo 4YOU</dc:title> 
    			</cc:work> 
			</rdf:rdf> 
		</metadata>  
		<sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="Logo-4YOU-namedview1288" showgrid="false" inkscape:zoom="8.6138461" inkscape:cx="32.180311" inkscape:cy="40.752087" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-myDepositOrAdvanceRequest"></sodipodi:namedview>  
		<path style="stroke-width:0.15332164" d="M 40.93662,87.878331 C 33.112367,87.428951 24.877894,84.480066 18.356777,
			79.792164 13.997907,76.658651 9.5987872,71.99446 6.754192,67.490452 4.597084,
			64.074981 2.34491,59.004219 1.544721,55.761346 1.461491,55.424038 1.287381,
			54.734091 1.157812,54.22813 0.446171,51.449189 0,47.520131 0,44.03224 0,
			40.579391 0.216349,38.54748 1.012151,34.526298 1.264705,33.250141 1.260357,
			33.265597 2.155075,30.463275 5.571519,19.762718 13.334272,10.515895 23.304633,
			5.2703796 c 4.502806,-2.3689767 8.66262,-3.7576175 13.95227,-4.657589 1.847858,
			-0.31439169 2.57136,-0.35215944 6.746153,-0.35215944 5.142435,0 6.69647,
			0.16943336 10.631514,1.15913394 10.720395,2.6962804 19.887939,9.1478989 25.997073,
			18.2953169 4.162248,6.232278 6.615918,13.271402 7.220425,20.7141 1.149046,
			14.146873 -4.438997,27.709135 -15.25453,37.023004 -7.198951,6.199421 -16.252337,
			9.85476 -25.834692,10.430875 -2.609594,0.156888 -3.018043,0.156562 -5.826226,
			-0.0049 z M 42.009871,41.349112 V 12.447981 H 38.363248 34.716619 L 21.497865,
			30.112191 8.279112,47.776403 v 3.570836 3.570838 H 22.07806 35.877004 v 7.666083 7.666082 h 3.066437 3.06643 z m -26.187524,
			6.937804 c 2.825982,-3.770086 19.541667,-25.855687 19.694124,-26.020874 0.174205,
			-0.188739 0.207217,1.905787 0.207217,13.147331 V 48.785211 H 25.586259 15.448834 Z M 69,
			66.659482 v -2.090535 l -7.709934,-0.05957 c -7.545568,-0.05833 -7.226938,
			-0.06683 -8.278066,-0.412324 -1.853278,-0.906086 -2.528007,-2.080564 -2.539,
			-4.119367 -0.01115,-2.068279 0.656699,-3.328745 2.539,-4.118738 1.051128,
			-0.345489 0.732498,-0.353984 8.278066,-0.412325 L 69,55.387053 V 53.29589 52.004955 l -6.943323,
			4.6e-4 c -8.155658,6.13e-4 -9.35891,0.123939 -11.583308,1.187579 -2.387529,
			1.141645 -3.708748,3.558635 -3.708748,6.784658 0,3.138467 1.285017,5.573459 3.53905,
			6.706184 1.046377,0.525836 1.484169,0.663722 3.052005,0.96125 1.263442,
			0.239763 2.396717,0.278324 8.547678,0.290851 L 69,67.950407 Z M 60.874869,
			47.865071 C 63.895048,47.578667 65.943826,46.779294 67.368852,45.33132 68.951214,
			43.723472 69.451942,42.42512 69.5,39.4 69.55638,35.850979 68.023457,
			33.491871 65.493572,32.181555 63.620123,31.21123 62.705417,30.876448 58.031982,
			30.88 c -3.519465,0.0026 -4.288996,0.244447 -5.13627,0.480446 -4.342817,
			1.419064 -6.056099,4.709973 -6.044477,8.039554 -0.06057,4.070087 2.323699,
			7.103107 6.013296,8.090102 1.917335,0.483106 5.233218,0.638325 8.010338,
			0.374969 z M 52.9,43.6 c -1.520371,-0.881521 -2.47681,-2.203851 -2.5,
			-4.2 -0.02776,-2.389702 0.803611,-3.459038 2.5,-4.2 3.382068,-1.333946 6.898588,
			-1.375631 10.7,0 1.293399,0.668261 2.4,1.777851 2.4,4.2 0,2.336017 -0.800309,
			3.222326 -2.4,4.2 -3.366977,1.206461 -7.260984,1.252061 -10.7,0 z M 68.989913,
			28.585085 69,27.396842 66.498106,26.051622 c -1.367804,-0.73987 -3.787054,
			-2.060026 -5.376125,-2.933679 l -2.889214,-1.588461 1.540531,-0.8227 c 0.847296,
			-0.452486 3.232522,-1.75413 5.300503,-2.892543 L 69,15.744396 V 14.15622 c 0.0021,
			-0.653534 0.06396,-2.088154 0,-2.088154 -0.06394,0 -3.215584,1.552777 -6.666856,
			3.450131 l -6.275045,3.449737 h -4.417812 -4.417825 v 2.526024 2.42697 l 4.484659,
			1.54e-4 4.484659,1.53e-4 6.28619,3.52598 c 3.457402,1.939287 6.458781,3.525742 6.52203,
			3.525857 0.06325,1.17e-4 0.0025,-1.734566 0,-2.388099 z" id="Logo-4YOU-path4558" inkscape:connector-curvature="0" sodipodi:nodetypes="ccsccsssssssscsccccccccccccccccccccsccccccccscsccccsscscccscssccccscsccsccccccccsccsscscccccsccc"></path> 
	</symbol>
	<symbol viewBox="0 0 48 48" id="shrs-icon-Accessibility">
		<sodipodi:namedview id="shrs-icon-Accessibility-namedview4430" pagecolor="#ffffff" bordercolor="#666666" borderopacity="1.0" inkscape:pageshadow="2" inkscape:pageopacity="0.0" inkscape:pagecheckerboard="0" showgrid="false" inkscape:zoom="17.3125" inkscape:cx="50.743683" inkscape:cy="28.64982" inkscape:window-width="1920" inkscape:window-height="1017" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-find-cdd"></sodipodi:namedview>
		<metadata id="shrs-icon-Accessibility-metadata4491"> <rdf:rdf> <cc:work rdf:about=""> <dc:title>fin cdd icon</dc:title> </cc:work> </rdf:rdf> </metadata> 
		<path style="stroke-width:0.126297" d="M 21.146406,47.995152 C 20.625428,47.936198 19.673571,
		47.773415 19.031172,47.633486 9.3699115,45.529081 2.1246471,38.064013 0.27576588,
		28.3091 0.04472534,27.090103 0,26.403506 0,24.0756 0,20.85134 0.19958097,19.488014 1.0702956,
		16.764452 5.4931987,2.9297879 21.077435,-3.8797216 34.213501,2.2825777 c 7.027907,3.2968839 12.014157,
		9.7887863 13.486968,17.5595203 0.399375,2.10715 0.399375,6.359852 0,8.467002 -1.843525,9.726646 -9.056406,
		17.182037 -18.681528,19.30963 -2.22984,0.492901 -5.473937,0.648015 -7.872535,0.376422 z m 5.700643,
		-1.938191 C 35.076373,44.961422 41.890769,39.509785 44.814364,31.682783 48.445614,21.961275 44.585462,
		10.802097 35.665258,5.2339425 26.959202,-0.20053064 15.566259,1.1313643 8.3092652,8.4320034 c -8.66348041,
		8.7155876 -8.66348041,22.5716036 0,31.2871876 4.8572988,4.886509 11.7465978,7.241854 18.5377838,
		6.33777 z m -11.385348,-5.27858 c -0.607908,-0.413736 -0.901963,-0.873534 -0.975418,-1.525205 -0.0601,
		-0.533186 0.04833,-0.813645 1.482539,-3.834916 0.851062,-1.792808 2.222095,-4.692783 3.046738,
		-6.444395 l 1.499355,-3.18474 v -2.695503 c 0,-1.966259 -0.04272,-2.749252 -0.157872,-2.894158 -0.08684,
		-0.109262 -2.184835,-1.714871 -4.662234,-3.568025 -4.780386,-3.575844 -4.904854,-3.695428 -4.904854,
		-4.71234 0,-1.314727 1.611106,-2.2437274 2.71541,-1.565762 0.208391,0.127939 2.046781,1.490239 4.085309,
		3.027331 2.038527,1.537095 4.027722,2.952507 4.420437,3.145359 0.636043,0.312348 0.851959,0.350642 1.977007,
		0.350642 1.125047,0 1.340963,-0.0383 1.977004,-0.350642 0.392713,-0.192852 2.381911,-1.608264 4.420438,
		-3.145359 4.305864,-3.246712 4.325643,-3.259859 4.906245,-3.260222 1.416322,-8.94e-4 2.336,1.516508 1.67084,
		2.756752 -0.154757,0.288563 -1.603675,1.45486 -4.704606,3.786942 l -4.480976,3.369953 -0.03401,2.89767 c -0.03232,
		2.754479 -0.0204,2.92611 0.241224,3.473166 0.151379,0.316528 0.82558,1.740594 1.498229,3.16459 0.672646,
		1.424 1.920486,4.059757 2.772977,5.857238 1.427946,3.010833 1.544955,3.312778 1.486102,3.834911 -0.119142,
		1.057034 -0.965589,1.800882 -2.049279,1.800882 -1.04266,0 -1.384895,-0.308072 -2.244867,-2.020756 -0.401109,
		-0.798829 -1.582782,-3.271093 -2.62594,-5.493916 -1.800988,-3.837657 -2.254446,-4.609844 -2.707087,
		-4.609844 -0.536561,0 -0.683281,0.270622 -4.142603,7.640971 -1.976826,4.21178 -2.185051,4.483545 -3.435289,
		4.483545 -0.473717,0 -0.773896,-0.07936 -1.074821,-0.284169 z m 7.572935,-25.140307 c -1.212599,
		-0.341695 -2.221599,-1.273196 -2.594186,-2.394939 -0.304209,-0.915877 -0.196903,-2.285286 0.237099,
		-3.025849 0.399212,-0.6811988 1.180394,-1.40659 1.823749,-1.6935089 0.743387,-0.3315249 2.340627,
		-0.2967488 3.091361,0.067305 0.704831,0.3417978 1.540711,1.2094402 1.875433,1.9467019 0.300377,
		0.661603 0.322406,1.964475 0.04689,2.773063 -0.581115,1.705462 -2.728499,2.820876 -4.480347,
		2.327227 z" id="shrs-icon-Accessibility-path1342"></path>
	</symbol>
	<symbol viewBox="0 0 88 88" id="Logo-SOPRA">
		<metadata id="Logo-SOPRA-metadata1292"> 
			<rdf:rdf> 
				<cc:work rdf:about=""> 
					<dc:format>image/svg+xml</dc:format> 
					<dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type> 
				</cc:work> 
			</rdf:rdf> 
		</metadata>  
		<sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="Logo-SOPRA-namedview1288" showgrid="false" inkscape:zoom="4.3069231" inkscape:cx="66.532098" inkscape:cy="28.581779" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-myDepositOrAdvanceRequest"></sodipodi:namedview>  
		<path style="stroke-width:0.14975731" d="M 24.094872,77.731499 C 18.540667,77.327297 14.444738,76.44345 11.515258,75.016979 
			10.316772,74.433395 9.8919547,74.121182 8.8196273,73.035867 5.6031966,69.780479 
			3.7144804,64.299089 3.9168627,58.807188 4.0215654,55.96596 4.5822045,54.256831 
			5.8665461,52.863483 7.5568162,51.029755 9.814934,50.873815 15.061025,52.228527 
			c 3.922368,1.012882 10.052225,3.221122 22.21249,8.001902 14.032521,5.516851 
			19.63755,7.481438 25.241598,8.847263 1.231378,0.300114 2.258625,0.563463 
			2.282765,0.585224 0.0697,0.06281 -2.4587,1.340908 -3.947123,1.995244 -6.666242,2.930596
			-14.719996,4.912569 -23.57724,5.802194 -2.21549,0.222527 -11.283133,0.409088 
			-13.178643,0.271145 z m 48.446491,-6.592043 c -2.566691,-0.307021 -7.614463,-1.407749
			-7.396318,-1.612851 0.03202,-0.03008 0.669285,-0.462339 1.416153,-0.960527 
			7.140281,-4.762813 6.764686,-12.609532 -1.071795,-22.391215 -0.692457,-0.86434 
			-1.275986,-1.728356 -1.296733,-1.920027 -0.03425,-0.316519 0.01932,-0.347414 
			0.583988,-0.336664 0.491545,0.0093 1.149986,0.303874 3.144905,1.406698 
			7.670423,4.240336 12.271399,7.419699 15.709242,10.855388 1.977577,1.976334 
			2.702779,2.919222 3.534352,4.595266 2.052343,4.136526 0.218506,8.228467 
			-4.379368,9.771921 -2.495159,0.837599 -6.335867,1.059548 -10.244426,0.592011 
			z M 22.722002,46.46754 C 22.018412,46.216241 18.849186,44.476565 15.78334,42.658715 
			6.602538,37.215085 1.4330474,32.415991 0.20695714,28.198401 -0.13722742,27.01444 
			-0.04177967,25.0456 0.41646611,23.876795 1.2795965,21.675292 3.7542852,19.992818 
			7.1151411,19.322557 c 1.8063101,-0.360236 6.3094269,-0.358914 8.8179589,0.0024 
			1.861984,0.268331 6.434222,1.234665 6.794624,1.436031 0.14429,0.08062 -0.04913,0.260288
			-0.650823,0.604537 -2.864326,1.638801 -4.935987,4.191403 -5.562252,6.85354 
			-0.355654,1.511826 -0.211661,4.329272 0.311887,6.102614 0.856108,2.899766 
			3.01825,6.711083 5.613934,9.895963 1.265456,1.552702 1.544063,2.097514 1.187959,2.323037
			-0.257349,0.162985 -0.241125,0.164298 -0.906427,-0.07332 z m 54.32837,-7.434784 C 
			72.830676,38.531325 67.090382,36.619046 51.799974,30.621009 37.458398,24.99518 
			31.449362,22.862227 26.172001,21.524173 c -1.562416,-0.396143 -2.8744,-0.753906 
			-2.915527,-0.795034 -0.112742,-0.112743 2.615653,-1.47597 4.73209,-2.364358 
			6.605831,-2.772833 14.910484,-4.721075 23.241243,-5.452308 11.657633,-1.023253 
			22.1024,0.237016 26.441773,3.190477 2.189662,1.490325 4.052612,4.252395 5.201506,7.711927
			1.520913,4.579739 1.604909,9.218266 0.220101,12.154322 -1.148032,2.434043 -3.0834,3.415224
			-6.042815,3.063557 z" id="Logo-SOPRA-path3907" inkscape:connector-curvature="0"></path> 
	</symbol>
    <symbol id="shrs-icon-folder-attachment" viewBox="0 0 88 88">
        <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="namedview5136" showgrid="false" inkscape:zoom="4.8977273" inkscape:cx="55.143457" inkscape:cy="30.30125" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-folder"></sodipodi:namedview>
        <path d="M 41.7,39.2 H 4.06 c -4.1,0 -4.2,2 -4,4.4 l 4.9,40 c 0.2,2.4 0.8,4.4 4.8,4.4 h 69.78 c 4.1,0 4.6,-2 4.8,-4.4 L 88,65.9528 C 81.887442,76.914583 67.481,76.972 67.481,76.972 54.650372,76.98791 41.7,67.455587 41.7,56.775538 Z m 0,-13.31 C 38.893424,25.731183 36.111078,24.636191 34,22.78 l -2.6,-2.6 c -2.107593,-1.873067 -4.793496,-2.967193 -7.61,-3.1 H 13.47 c -2.48564,0.0856 -4.5534064,1.938583 -4.91,4.4 L 7.27,32.99 H 41.7 Z" id="path5133" inkscape:connector-curvature="0" sodipodi:nodetypes="csccssccsscccccccccc"></path>
        <path inkscape:connector-curvature="0" d="M 67.467817,72.116009 C 56.052177,72.102087 46.801657,63.388329 46.786879,52.635083 V 14.131937 h 4.864 v 38.503146 c 0.0044,8.225658 7.08459,14.891662 15.81694,14.891662 8.73235,0 15.812549,-6.666004 15.81694,-14.891662 V 14.131937 c -0.0018,-5.2736816 -4.54076,-9.5479565 -10.139294,-9.5479565 -5.598535,0 -10.137537,4.2742749 -10.139295,9.5479565 v 38.503146 c 0,5.614906 8.941177,5.614906 8.941177,0 V 14.131937 h 4.864 v 38.503146 c -0.0013,4.853018 -4.17816,8.786504 -9.330118,8.786504 -5.151958,0 -9.328812,-3.933486 -9.330118,-8.786504 V 14.131937 c 0.0021,-7.8061947 6.720718,-14.13328168 15.007765,-14.13328168 8.287048,0 15.005627,6.32708698 15.007766,14.13328168 v 38.503146 c -0.0148,10.759817 -9.2762,19.476289 -20.698825,19.480926 z" id="path4553" style="stroke-width:0.86778939" sodipodi:nodetypes="cccccsccscsscccsccsccc"></path>
    </symbol>

    <symbol viewBox="0 0 88 88" id="shrs-icon-inaccessible">
        <path d="M 44 0 C 26.203922 0 10.159491 10.719029 3.3496094 27.160156 C -3.4602549 43.601267 0.3049954 62.527661 12.888672 75.111328 C 25.47234 87.695007 44.398732 91.460262 60.839844 84.650391 C 77.280972 77.840518 88 61.796078 88 44 C 88 19.69979 68.300211 -4.7369516e-015 44 0 z M 44 10.154297 A 33.845872 33.845871 0 0 1 77.845703 44 A 33.845872 33.845871 0 0 1 71.171875 64.113281 L 25.101562 15.955078 A 33.845872 33.845871 0 0 1 44 10.154297 z M 17.710938 22.724609 L 64.083984 71.195312 A 33.845872 33.845871 0 0 1 44 77.845703 A 33.845872 33.845871 0 0 1 10.154297 44 A 33.845872 33.845871 0 0 1 17.710938 22.724609 z "></path>
    </symbol>

    <symbol viewBox="0 0 90 90" id="carte-pointage"><metadata id="carte-pointage-metadata2187"><rdf:rdf><cc:work rdf:about=""><dc:format>image/svg+xml</dc:format><dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type></cc:work></rdf:rdf></metadata><sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="carte-pointage-namedview2183" showgrid="false" inkscape:zoom="6.1333333" inkscape:cx="45" inkscape:cy="45" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="Calque_1"></sodipodi:namedview> <style type="text/css" id="carte-pointage-style2170"> .st0{fill:#FFFFFF;} .st1{enable-background:new ;} .st2{fill:#010002;} .st3{fill:none;} .st4{fill:#252D3A;} .st5{fill:#1D1D1B;} </style> <g id="carte-pointage-g2180" transform="matrix(1.0238713,0,0,1.0238713,-1.0238713,-1.773644)"> <path d="m 79.3,20.5 c 2.7,0 4.9,2.2 4.9,4.9 v 39.2 c 0,2.7 -2.2,4.9 -4.9,4.9 H 10.7 C 8,69.5 5.8,67.3 5.8,64.6 V 25.4 c 0,-2.7 2.2,-4.9 4.9,-4.9 h 68.6 m 0,-4.8 H 10.7 C 5.4,15.7 1,20 1,25.4 v 39.2 c 0,5.4 4.4,9.7 9.7,9.7 h 68.5 c 5.4,0 9.7,-4.4 9.7,-9.7 V 25.4 C 89,20 84.6,15.7 79.3,15.7 Z" id="carte-pointage-path2172" inkscape:connector-curvature="0"></path> <g id="carte-pointage-g2176"> <rect x="4" y="30.1" width="82.099998" height="4.9000001" id="carte-pointage-rect2174"></rect> </g> <rect x="13.2" y="41.5" width="30.700001" height="7.5" id="carte-pointage-rect2178"></rect> </g> </symbol>

    <symbol id="shrs-icon-white-warning-triangle" data-name="Calque 1" viewBox="0 0 88 88" version="1.1">
        <defs id="defs2880"></defs>
        <path id="alerte" d="M 86.775436,72.145403 50.134315,3.857706 C 48.820833,1.4711791 46.657834,0.03197178 44.337116,3.8974641e-4 42.001306,-0.02738008 39.813804,1.4251846 38.528158,3.857706 L 1.393159,72.145403 C -0.26349284,75.278018 -0.45618581,79.274351 0.88752099,82.632024 2.2501159,85.925412 4.9715978,87.995999 7.9429357,88 H 80.260935 c 2.964632,-0.03348 5.67487,-2.095517 7.055415,-5.367976 1.376313,-3.354473 1.168682,-7.379772 -0.540914,-10.486621 z" inkscape:connector-curvature="0" style="stroke-width:1.31325912" sodipodi:nodetypes="cccccccccc"></path>
        <path inkscape:connector-curvature="0" id="alerte-2" d="m 104.31649,79.343379 c -3.88679,-0.05619 -7.003482,-3.231689 -6.987013,-7.118843 -0.05714,-3.90116 3.085443,-7.096452 6.987013,-7.104196 9.06134,0.411348 9.06134,13.811689 0,14.223039 z M 98.120459,20.239401 c 3.354241,-3.648377 9.111061,-3.648377 12.465301,0 1.59587,1.769714 2.40469,4.111609 2.24112,6.488987 l -2.73915,32.225217 c -1.80273,-0.846678 -3.7807,-1.253316 -5.77124,-1.186474 -1.97802,0.02657 -3.93058,0.449958 -5.741948,1.245066 L 95.952581,26.78698 c -0.199732,-2.386746 0.583268,-4.751622 2.167878,-6.547579 z" sodipodi:nodetypes="cccccccccccc" style="fill:#ffffff;fill-opacity:1;stroke-width:1.46478248"></path>
        <path inkscape:connector-curvature="0" id="alerte-2-7" d="m 44.302658,80.705142 c -3.886793,-0.05619 -7.003485,-3.231689 -6.987016,-7.118843 -0.05714,-3.90116 3.085443,-7.096452 6.987016,-7.104196 9.06134,0.411348 9.06134,13.811689 0,14.223039 z M 38.106624,21.601163 c 3.354241,-3.648376 9.111064,-3.648376 12.465304,0 1.59587,1.769714 2.40469,4.111609 2.24112,6.488987 l -2.73915,32.225218 c -1.80273,-0.846678 -3.7807,-1.253316 -5.77124,-1.186474 -1.978023,0.02657 -3.930583,0.449958 -5.741951,1.245066 L 35.938746,28.148742 c -0.199732,-2.386746 0.583268,-4.751622 2.167878,-6.547579 z" sodipodi:nodetypes="cccccccccccc" style="fill:#ffffff;fill-opacity:1;stroke-width:1.46478248"></path>
    </symbol>

    <symbol id="shrs-icon-info-circle" viewBox="0 0 88 88">
        <defs id="defs2009"></defs>
        <g id="g2573">
            <path sodipodi:nodetypes="cccccccccccccccccccccccccccccccccccc" inkscape:connector-curvature="0" id="path2004" d="m 53.17,68.2 c -2.27,0.89 -4.07,1.56 -5.42,2 -1.511976,0.500007 -3.097777,0.740075 -4.69,0.71 -2.307461,0.149437 -4.588207,-0.563296 -6.4,-2 -1.489657,-1.264443 -2.324686,-3.136822 -2.27,-5.09 0.0013,-0.819233 0.05478,-1.637552 0.16,-2.45 0.132805,-0.948426 0.31303,-1.889605 0.54,-2.82 l 2.84,-10 c 0.25,-1 0.47,-1.87 0.63,-2.71 0.160405,-0.776785 0.247454,-1.566926 0.26,-2.36 0.10039,-0.961103 -0.187654,-1.922451 -0.8,-2.67 C 37.145647,40.181064 36.071733,39.893027 35,40 c -0.76497,0.0049 -1.525555,0.116003 -2.26,0.33 -0.76,0.24 -1.42,0.46 -2,0.66 L 31.52,38 c 1.85,-0.76 3.64,-1.4 5.33,-1.95 1.547049,-0.523553 3.16688,-0.800274 4.8,-0.82 2.272591,-0.140069 4.515637,0.573141 6.29,2 1.470305,1.291693 2.284857,3.173968 2.22,5.13 0,0.44 -0.06,1.2 -0.15,2.29 -0.08849,1.015856 -0.276044,2.020632 -0.56,3 l -2.82,10 c -0.24,0.8 -0.44,1.73 -0.64,2.75 -0.153035,0.76806 -0.243333,1.547297 -0.27,2.33 -0.117696,0.9904 0.208031,1.982221 0.89,2.71 0.921317,0.577425 2.007681,0.834074 3.09,0.73 0.782112,-0.04264 1.555311,-0.187192 2.3,-0.43 0.644749,-0.162847 1.276492,-0.373428 1.89,-0.63 z M 52.68,27.68 c -2.706595,2.443982 -6.823405,2.443982 -9.53,0 -1.281182,-1.126815 -2.010784,-2.753828 -2,-4.46 0.0034,-1.703143 0.730546,-3.324572 2,-4.46 2.706595,-2.443982 6.823405,-2.443982 9.53,0 1.276862,1.129996 2.005535,2.754937 2,4.46 0.01344,1.706731 -0.716634,3.334797 -2,4.46 z"></path>
            <path d="M 44,0 A 44,44 0 1 0 88,44 44,44 0 0 0 44,0 Z m 0,86 A 42,42 0 1 1 86,44 42,42 0 0 1 44,86 Z" id="path3850" inkscape:connector-curvature="0"></path>
        </g>
    </symbol>

    <symbol id="shrs-icon-validated-white-check" viewBox="0 0 88 88" version="1.1">
        <defs id="defs10390"></defs>
        <g id="g10953"></g>
        <g id="g10958">
            <path d="M 44,0 C 26.20368,0 10.159577,10.720315 3.3496056,27.161676 -3.4603517,43.603003 0.30503806,62.527274 12.888882,75.111118 25.472726,87.694962 44.396997,91.460352 60.838324,84.650394 77.279685,77.840423 88,61.79632 88,44 88,19.699471 68.300529,0 44,0 Z M 75.75,33.31 47.51,61.55 c -11.768671,11.768671 -22,0 -22,0 L 12.2,48.24 c -1.683232,-1.738008 0.36608,-6.512895 2.11,-8.19 l 0.73,-0.73 c 1.667882,-1.728712 6.428599,-3.755734 8.13,-2.06 L 36.48,50.57 64.77,22.33 c 1.712694,-1.667791 6.45028,0.359004 8.13,2.06 l 0.73,0.73 c 1.710912,1.710912 3.800547,6.449253 2.12,8.19 z" id="path10385" inkscape:connector-curvature="0" sodipodi:nodetypes="sssssscsccccccccsc" style=""></path>
            <path style="fill:#ffffff;fill-opacity:1" sodipodi:nodetypes="csccccccccsc" inkscape:connector-curvature="0" id="path10385-3" d="M 75.75,33.31 47.51,61.55 c -11.76867,11.768671 -22.000003,0 -22.000003,0 L 12.199998,48.24 c -1.683232,-1.738008 0.36608,-6.512895 2.109999,-8.19 l 0.73,-0.73 c 1.667882,-1.728712 6.428599,-3.755734 8.13,-2.06 L 36.48,50.57 64.77,22.33 c 1.71269,-1.667791 6.45028,0.359004 8.13,2.06 l 0.73,0.73 c 1.71091,1.710912 3.80054,6.449253 2.12,8.19 z"></path>
        </g>
    </symbol>
    <symbol id="shrs-icon-open-transaction" viewBox="0 0 88 88">
        <g id="g8877">
            <path style="stroke-width:1.01288855" d="M 86.590858,62.800461 H 68.824345 v 17.4792 H 3.1681048e-4 L 7.4591142,33.119768 V 72.817764 L 61.358818,72.817411 V 62.800461 H 17.766513 V 33.133375 L 7.4591142,33.119763 3.1681048e-4,80.279657 0,25.657161 17.766513,25.85249 V 6.1779612 H 86.590858 Z M 25.22531,13.640568 v 41.697997 l 53.900021,-3.53e-4 c -4.7e-4,-10.728056 0,-27.961985 0,-41.697644 z" id="path1477-7"></path>
            <path style="stroke-width:1.12658846" id="path4588" d="M 68.965196,29.021 H 56.873 l 0,-12.092588 c 0,-0.622198 -0.80462,-1.126589 -1.426818,-1.126589 h -6.759533 c -0.622198,0 -1.44171,0.504573 -1.426649,1.126589 V 29.021 l -12.092419,0 c -0.622198,0 -1.126589,0.804282 -1.126589,1.42648 v 6.759533 c 0,0.622198 0.504583,1.442439 1.126589,1.426987 l 12.092419,0 v 12.092081 c 0,0.622198 0.804451,1.126589 1.426649,1.126589 h 6.759533 c 0.622198,0 1.426818,-0.504391 1.426818,-1.126589 V 38.634 h 12.092196 c 0.622219,3e-5 1.126643,-0.804768 1.126643,-1.426987 V 30.44748 c 0,-0.622219 -0.504424,-1.42651 -1.126643,-1.42648 z"></path>
        </g>
    </symbol>
    <symbol viewBox="0 0 50 50" id="shrs-icon-accessibility">
        <defs id="defs4432">
            <pattern id="EMFhbasepattern" patternUnits="userSpaceOnUse" width="6" height="6" x="0" y="0"></pattern>
        </defs>
        <sodipodi:namedview id="namedview4430" pagecolor="#ffffff" bordercolor="#666666" borderopacity="1.0" inkscape:pageshadow="2" inkscape:pageopacity="0.0" inkscape:pagecheckerboard="0" showgrid="false" inkscape:zoom="17.3125" inkscape:cx="50.743683" inkscape:cy="28.64982" inkscape:window-width="1920" inkscape:window-height="1017" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-find-cdd"></sodipodi:namedview>
        <title id="title4419">fin cdd icon</title>
        <metadata id="metadata4491">
            <rdf:rdf>
                <cc:work rdf:about="">
                    <dc:title>fin cdd icon</dc:title>
                </cc:work>
            </rdf:rdf>
        </metadata>
        <path style="stroke-width:0.126297" d="M 21.146406,47.995152 C 20.625428,47.936198 19.673571,47.773415 19.031172,47.633486 9.3699115,45.529081 2.1246471,38.064013 0.27576588,28.3091 0.04472534,27.090103 0,26.403506 0,24.0756 0,20.85134 0.19958097,19.488014 1.0702956,16.764452 5.4931987,2.9297879 21.077435,-3.8797216 34.213501,2.2825777 c 7.027907,3.2968839 12.014157,9.7887863 13.486968,17.5595203 0.399375,2.10715 0.399375,6.359852 0,8.467002 -1.843525,9.726646 -9.056406,17.182037 -18.681528,19.30963 -2.22984,0.492901 -5.473937,0.648015 -7.872535,0.376422 z m 5.700643,-1.938191 C 35.076373,44.961422 41.890769,39.509785 44.814364,31.682783 48.445614,21.961275 44.585462,10.802097 35.665258,5.2339425 26.959202,-0.20053064 15.566259,1.1313643 8.3092652,8.4320034 c -8.66348041,8.7155876 -8.66348041,22.5716036 0,31.2871876 4.8572988,4.886509 11.7465978,7.241854 18.5377838,6.33777 z m -11.385348,-5.27858 c -0.607908,-0.413736 -0.901963,-0.873534 -0.975418,-1.525205 -0.0601,-0.533186 0.04833,-0.813645 1.482539,-3.834916 0.851062,-1.792808 2.222095,-4.692783 3.046738,-6.444395 l 1.499355,-3.18474 v -2.695503 c 0,-1.966259 -0.04272,-2.749252 -0.157872,-2.894158 -0.08684,-0.109262 -2.184835,-1.714871 -4.662234,-3.568025 -4.780386,-3.575844 -4.904854,-3.695428 -4.904854,-4.71234 0,-1.314727 1.611106,-2.2437274 2.71541,-1.565762 0.208391,0.127939 2.046781,1.490239 4.085309,3.027331 2.038527,1.537095 4.027722,2.952507 4.420437,3.145359 0.636043,0.312348 0.851959,0.350642 1.977007,0.350642 1.125047,0 1.340963,-0.0383 1.977004,-0.350642 0.392713,-0.192852 2.381911,-1.608264 4.420438,-3.145359 4.305864,-3.246712 4.325643,-3.259859 4.906245,-3.260222 1.416322,-8.94e-4 2.336,1.516508 1.67084,2.756752 -0.154757,0.288563 -1.603675,1.45486 -4.704606,3.786942 l -4.480976,3.369953 -0.03401,2.89767 c -0.03232,2.754479 -0.0204,2.92611 0.241224,3.473166 0.151379,0.316528 0.82558,1.740594 1.498229,3.16459 0.672646,1.424 1.920486,4.059757 2.772977,5.857238 1.427946,3.010833 1.544955,3.312778 1.486102,3.834911 -0.119142,1.057034 -0.965589,1.800882 -2.049279,1.800882 -1.04266,0 -1.384895,-0.308072 -2.244867,-2.020756 -0.401109,-0.798829 -1.582782,-3.271093 -2.62594,-5.493916 -1.800988,-3.837657 -2.254446,-4.609844 -2.707087,-4.609844 -0.536561,0 -0.683281,0.270622 -4.142603,7.640971 -1.976826,4.21178 -2.185051,4.483545 -3.435289,4.483545 -0.473717,0 -0.773896,-0.07936 -1.074821,-0.284169 z m 7.572935,-25.140307 c -1.212599,-0.341695 -2.221599,-1.273196 -2.594186,-2.394939 -0.304209,-0.915877 -0.196903,-2.285286 0.237099,-3.025849 0.399212,-0.6811988 1.180394,-1.40659 1.823749,-1.6935089 0.743387,-0.3315249 2.340627,-0.2967488 3.091361,0.067305 0.704831,0.3417978 1.540711,1.2094402 1.875433,1.9467019 0.300377,0.661603 0.322406,1.964475 0.04689,2.773063 -0.581115,1.705462 -2.728499,2.820876 -4.480347,2.327227 z" id="path1342"></path>
    </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-lock">
      <path d="M 78,35 H 70 V 24 C 70,8.1203247 56.877423,-0.03814119 48,0 H 40 C 30.06812,0.10455472 18,7.1730884 18,24 v 11 h -8 c 0,0 -9.97696109,0.572473 -10,11 l -0.25,31 c 0,6.075132 4.174868,11 10.25,11 h 68 c 6.075132,0 10.28,-4.924868 10.28,-11 L 88.03,46 C 88.134142,35.206388 78,35 78,35 Z M 44,12 c 4.024564,-0.404273 14,1.549315 14,12 V 35 H 30 V 24 C 30,13.217901 39.818289,11.665409 44,12 Z" id="path4646" sodipodi:nodetypes="ccsccsccccssccccsccsc"></path>
</symbol>
    <symbol viewBox="0 0 698.79858 271.37808" id="shrs-icon-pleiades"><title>shrs-icon-pleiades</title>  <sodipodi:namedview id="shrs-icon-pleiades-namedview4" pagecolor="#ffffff" bordercolor="#000000" borderopacity="0.25" inkscape:showpageshadow="2" inkscape:pageopacity="0.0" inkscape:pagecheckerboard="0" inkscape:deskcolor="#d1d1d1" showgrid="false" inkscape:zoom="2.2037824" inkscape:cx="349.39929" inkscape:cy="135.90271" inkscape:window-width="1920" inkscape:window-height="1001" inkscape:window-x="-9" inkscape:window-y="-9" inkscape:window-maximized="1" inkscape:current-layer="g8"></sodipodi:namedview> <g inkscape:groupmode="layer" inkscape:label="Image" id="shrs-icon-pleiades-g8"> <image width="698.79858" height="271.37808" preserveAspectRatio="none" xlink:href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAM4AAABQCAYAAABRc0r3AAAABGdBTUEAALGPC/xhBQAAAAlwSFlz
        AAALEAAACxABrSO9dQAAAAd0SU1FB9wCDgonLaG6OWEAAAAZdEVYdFNvZnR3YXJlAHBhaW50Lm5l
        dCA0LjAuMjHxIGmVAAAw6UlEQVR4Xu2dB5Qlx3We7+T0Js/s7O7MbMbmnLDIgQGACIoSxWTJliXZ
        lm05HTlHyXKgbMlyOjZNyeHYx1EWacs6sk0d2ZbEnEGCBEUikAABbM6T4/P/Vfd92/Om3pv3Zmc2
        aS7wb3dXVVe8f91b1f2mLZ/PL8L8/HxAPj93U5ifn42GO+bmZkqmIXx6ejKkicWvYQ23E/pn9Ygz
        Ozu9gBiclyNLFrE05Fec5xrWcDugf0oTBwW9WUAUV/iY9SANcViXycnxBSjOpxjZ+DWs4VYiEGW1
        iOPkiJGGsLGxERsdvW7Xr1+1q1cv25Url+zy5YsFXLx4PhxJMzMztej+WJlrWMOtgJRwdYnDMUsa
        zsfHRwNRrl27YiMj1wKBJibGAogjDDI5kS5duhDOIRCWKZv3GtZwOxCIslrEcZcKYDGmpiYKxIA0
        uGNZIjj8Xo7cB2GwPufOnQkkgmBrxFnD7UQgymoRJ0sESAMBAOeEZ4nlRMnehzvm5xAMKwVxIB3k
        8fSrgbn8tM3kpwLm8oR5n6T9ongH8TP5+YA5Ia/4/JzqnslvDfcWpJRZsiyMdKUFXGcVnDDOPd7j
        /BxyzOt8ambSRsa0jhm9ZuOTYzY9K2WUUnGcnZ9ZBOIc3J8FCjoxNW6Xr16y02fftLGJ0UK4p+ec
        MsM9mbZUCwgzmhfR8yM6hwjqp1lBx4QwkF/k1XFSZBlXH44Kk/QnhGdNRp0iea/h7ocGvzRxHE6O
        YjiJOOJSOXEckGNyWu6ZCMMRYjgJKgEkANkw8oSEFy6dt0tXLhZI4sQjfaGconZUgzkRZzJ/Vbiu
        c1kRSJMiIQ7u4kg4YmkmssSZEXGm14hzL0ODX5o4TgzgZMjGZUnCtafHxWL9gqUZHdfCX1YCi5Al
        gZPCFR2QBmI4PAwUyCBwDRHPnj9j10auFuL8Hk/jdV0O8vNS/JkRQQSZkws2rz6CNAWLkxAnPy/L
        o3gs0QxphPz0GnHudWjwSxPHkSWQX0MO1h2+LZwlEeFhq1mkyVoaJ4mTIUseh5MjC0+fJQ/AZTt/
        8VzBZYM4gTDpPdk6V4uwRpkSOSZFDBEGUsxBGly2ecXNKXweUhGfWKQQB7A4a67aPY2yxHFLAjmc
        PE6O7LlfQxh2zNgBO3v2tF25djm4VSg2wAJhIQjzdY9bJdw53C4nSCkSEZ4l37kLZ+3i5QuFeyBP
        gYRpO5aDQBysBtYjtSbT6icsT35G8ZoQAgJBEosUEDYHFCYEyxTJew13P6T0N4iD1NTULUBtbb3V
        1TWEo1ntAhBfX99oTU0tlst1WF/fOhscHLbh4c3hWCyQBjhZIJbj6vUrC+AEg3AQKkskJw/nWB3I
        gzvItRMqpI00uFKEnbFZcMMNYwOgsPifEjmArIuvcSDWtM6nC7txa8S5V1ERcQDnkIVziNLW1h6I
        0tvbb93dvdbV1WM9PX3hmrhwD+QqQrE4CbAUvpEAWSAWpIBETiDi3aI4iSAMFgfyQRji/BhrcKXg
        fogwQ9+IOJxfEa4qHEIESwNxRKIJkWVEcZBqUvHjih8P5Fkjzr2KssRxq+LA8rS0tAWCbNgwaFgZ
        rrE6xDU3twbSNDQ0hXtrTcQT6mpktYT62oaAhrrGgHKEQiAAZIE4kIOdNMgEqbBC7p5BLtY6pF1g
        dSINrhS4XIkFoW8SS3JNRLgWLAmESaxNfm42EGc0pAezOp8OYBs7lvca7n5o7cI6Zi5V1cQFw1pA
        BicL4BqrgguGVXEyObkgjZOokE9KnGI4WSBT9jobDsH8uljOnDtt333jtUAmty64axDMrRBH1mZs
        k3PMrseAb2qAbLiH0Sf5adYr+fDQdjYQQeuwuSsizHWFTwqphRNhki1r0kMkLE71xPE+rQRILI81
        3BpIWRKLg8TcMs4hBG5Zf/9AsChOJE/T2NgciIOl8XuBK34xOSBFY73SptfEe1hTQ7O1NLVaa3Ob
        dbZ3WUeu09rbOgK47unqtXV9A7ZhYKPKWChvnH694PJBnGJCsMEBPMyvizsFhC1mdtMmxpVW1m30
        nOUnRJpR4bzORSaIg4XBWof10HSyLiJsIhBqcb5ZOAnuNCCx+q7hBqQUCWlqamoK1sUVH3R0dNnA
        wIZgZSBQcTykaW3NhXVP0ukpaRgEHbEeuGUQo5hAkKS5sSWQJNfaXiCIw0nT1pILaSCUk8rv6evp
        t6GNw3Zw/yE7cuhoOC+W2MPZLHGy8I4hfnqWe3DHLlt+/LzlP/ab9tFHnrFfe8+PWf41kUf5sqYp
        kAbMcL+/frOws0FWQe9GIMVtutsQa1cplGqvFCMhDuIWxy0Na5h169YHSwNBnBTE+zmEgThZFw1y
        kQayuDsGnERuWbo6uhdYFQgCMYgDpPf7s4TLgjJIO7hhKBBn545dId9ALtULCwmKxd0374hi4oS1
        Uh7ijFn+6quWf+FL9tHN++yT2+63Xz/xtL3yC78UNgcWEIc1D+dzNyaj4oG4l4AU+usuARJrSzlE
        8/FNgUQWWhG2ldvbO8PNTihPA1FIhxXCTSMN4aRxq4RSZ5Uc64JS93b3BUvhZCEcQpUiSJZ4WWQ3
        GShr8/AW27p5W3DnBvrXh3pQT1xIX4NxLLaaINtRob3WYLlaEa6l36x70E595CO2+8Uv2r7TX7V3
        vfw5s1MHTBVXKuqs/FR+QLhfB2V7rxOnFJCYst1uxOpaDbJ5BWGAE0kUiBka1wxrg6K5cqFwWaWD
        VBDMd9GAFxLOBRQeYnR39gRlZn2ClXGyQIAsSRxOlixhIIfD78veD2HWr9sQ8scC0Qbq53WiLVhR
        jhA8W9fi+tfUisgNHWatvdb70z9jb/nOS7bvzRft4MvPW/cHf8ps43ozTRyqmYAbyiQhhHuVE+T5
        PUqcYmQV7nYAidVrOcjmmZHE2rB7tn79xoJVyVoarp0orH9cCT2ezLkOM72IgUVxC4CVYV0CGbLk
        cMV3kjg5PLwUSE86SMh9uHwQlDiuaYM/V6LOboFwL7E+hHHtJMrW32ij0tg732EPvfq8bXn1BTv5
        0it2/KMfM+vaovhejYb6QnVo0r/8ZzWJxYE09QK19A5fw+0hUKweNwPPd4H4DtstwEPCxRT/IA1b
        KdQL/0j4VArOCYulzeKU4HX6d2nY3YjvE7wdfzkNu+NwKyRWbhG6hb8ofFWYFKaEEeH/Ch8QGoSQ
        dhFxams1cwtpQf9B+J8ZfFz4bBF+U/gV4R8Lf0Q4JjQL2QothccFlw8LsTTLQY3w08K8gLwgDAmx
        tMV4RHD5b0Iszd2A9wkuPyPE0txRWC2JlZXBHuF3heeFPyYcTcOY1P+WcFb4VaFLWEycxA9P1jhK
        sByZFb4l/FWhUyiuYAyrRZwfFqYF5EVhqxBLF8MacW4zVlpiZaToEV4S/o0wIDD5O4gjzbDwFeGX
        hTonjpNngSiBy5yAybomXBC+ncFVYVRwBc0KlTkuZCsZw2oQ5zFhTEBeF5g9YulKYY04dwCK5bna
        fQeFPyA8I2wR2J2qWGJlCB8Uvii0CPcLuGhfEl4R0Pm/KeC97BbQ93eUJA5WRwlcIEA596tdIFP8
        wP8sQCSX88JeIXafY6WJQ10wrQjH/UIsXTmsEecOQVZElJ8Q8inmheeFnxQWP5yLSCR/9PpVAd3l
        mrXtJQGd5vr7BdY7G9LrfyX8SknisM5RApdvClxXisMC6wmX/yeUW5CvJHEwtV8TEKzjw0Is3VLA
        rfu5FLh8sTR3A+564gAXESRLnCyeE+5Lk5WUSN5M6jNCb3pdTJwdArJT4Pr9wunVIg64T7gsIFSM
        RVYsHVgp4rDr8R+FM8J3Bdy1WLrfS7gniAMQkaNLOCm8T/gXwjXByfOKsPgdq4xE8n1SQE9r02uI
        g3v2jPA9AptiTMS4ccSz9MiXJE6Rq7Yc4gC2ll3+rhBLA1Zrc2AN9xBxQLGIKEPCx1PigF8VktcV
        IhLJE+JgYbLEYb3+OwLLDHbZsjuxbBgsIA5YICRIZbnEeYvgW8H/W4ilAWvEWT3c08RBRJQBAWsD
        cWaFB9OoRRLJ86DAZoC7ZllX7d3CdQGyePp3CleixMHarICrBvAPWVghzwl1QizdzRKHfGnwnxJ+
        QfiQ8E+FvyI8K1S6LX6zYOeFtdHTwh8X/o7wzwXqA35W+DPC24ScEMujUlAWzxp+XGBXyMvgedpP
        CYRTzp8VXJZDnFbhrcJfEOhTyuAhMvkybo1C7L5KwLoX1+ePCngk3oZ/JvDs5E8IKCqbPWGNXEpE
        lh9PiQM+lAYvEvIoAgRhA4kx4zpLHPqYjaFPC7459vPCb5Qkzgq5apg432Fjs6Dw5LUIyyUOg4aC
        fEdwyxYTttJRYN8ZqQRsKmCyQSV1QrnocLbulxLqih9NnXhaHcuvFGjzjwk8K6u0LJdqiNMhoMy0
        qVTfEs568q8JbUIsnxhow08KrEPLjZsL7Twn7Eg0dLGILD3CREqcbwlRd015xMD4/i+BCfiQ8HnB
        27NZYNL/IYGNpzeF31eSOIgSuNwMcfxZykoTh4H9H0JW6GAG+jWBjsYEZ4XwE0Isv2Jkt6P/pRBL
        k8UPCC7jAjt6KAZbnQ58ZjZKssLzgj4hlmcxmPV4AJdVNtrM6zReBs+sWOzGnqtVShw2dnjYlxXq
        Tf3pQ8orJu1nBBQrll8WTcJ/EbJCe64IpwXaABkpg350ofwDQTFLiMjy5ZQ4c0JXGrxAlEcMPNyk
        bX9JKOUVYXl5c4C3Z5pWmzg8P3FFoWMxfbF01RKHxn1EcOGhFO7KEQElpJHM5CgAbgD1d0GxNgmx
        fLOoljh0/tsF2syEwfYmsxa7MQ6eQu8S/pzgO47IPxRieWZB39E3LkxIuA24OpTlZVBmv7BFwO3I
        btBUQhzqyLtaLpAft4x6E0ff0se0E1eYCcrlEwLxsXwdf0hw4uMJ4M4ybusEJkPagGtNm+hHHlyz
        Vv4RIUwwpURk+eWUOOBgGrxAuL8EcGshD6+PPSB4O6gH7/thhdgowAItIM1qEOcPCi7/SYilAdUS
        B7Ppnc/bC/jAsXQO/FVmCxc6pxSJHdUSp1owUG4RIfNSa55HBbcizM6VPp+qdnOANaLL/xFQ6Fg6
        B5MQVtMFMsXSAfqcdxsRxu/3C7F0SyImIsuHM8R5LA0uSCyfImwX/q3ApMZkTD/jUjN5QHCIHdIu
        Io6vb9KCXJZDHBZz3kkIL87F0oFqiEO+XxcQNh54RSKWrhg0+hsCgsLiy8bSOVabOGx/fkFwKfd2
        Awr3UcGFTZBYuhiqIQ6ulq9J8eUrdSF5OIiCISy0CwpWBNY2THQI5Sx7gyQmIsvP3iRxHFgbxoPd
        NDa4FrlvJYkDlMBlOcThTWle+ERg7qAQSweqIc6DgueLv7+U5ciCRbULrl0sjWO1iQPI14Xdv1ga
        gNvJ2g3BnQhv6FaIaojzE4ILr9fH0pRC1o1kGzeWBuKwkYPgppVa8y6JmIgsH1wh4iyJksS5ie1o
        /FOUMrs45SW5WFpHNcRhB8flvUIsTSmwq+bb458T/KFXDLeCOJh/FxQ8lgbw1oXLfxdiaUqhGuKQ
        N8LCn5k2lqYUcD1d2FKOpaG/WSsgrH3ZTo+lqwjFcluJU1eXLnQkSuDCLME7PTHgKn2vwAISd4KZ
        JCuElXtPDVRDHPbVEYhZ7eACXlhFcBXKLWRvBXH+tuBSzpVlQe3C74tiaUqhUuJgudnRQtg5Y5Ee
        S1cKWEVfd0KOWBrw1wUXNozYyIilWxLFUo44sftvBpUSZznCE1cGqpIftVVDHPbUEbYr/cW8asAL
        py7lfp9zJxGHB5oukCiWphQqJQ47ZowZgjWuxgUGbDP7DhtuZSwNgCg+eSE8j2LXqmq3rVhuK3H8
        15+IElQrWIEvCwzQNmFRgSVQKXEgofvIbwhLbX3GkN1dY7s2lgbcDHFYTPIqB892eNCH4vNEnyfv
        WbjbgpQjTnZLmdfcY2lKoVLiMF4TAvLbQixNOaD4vvDn+Yu/whLDAQGr5oJryEYJk0LFGwbFcluJ
        w/rGRQlc2Clh4chrF38jgz8v8GoJaw32vlnflHqAVA6VEgf3wd0JBodnOTxMqwbZAeNpf6wcsBzi
        sIZiOxdS+wZGpVIpccptIsRQKXH2Cb4uZUct1nfl8F8F35GDgEvtyK0XuKf4gTCbH/yUg63hslav
        WG4rcRAnjxK4LPc5TqVYDnFWQlaSOCzgefLtgr+Py4Irgi/PKx2/XoSsy3InEedmhe3+Sn6mDjF4
        PvVrgls7FzZxeKbCg9fYvQFZEVm2Co+nWPDmQOzem8HdTBw6+pM3iXKv31RDHGbXLKFxdfg9B/58
        uZ27Stc4t5o4ED7WX5WC9lfzXiAE4rkJu7E8bMwKDyL56zwl10CVSOy+m8HdTBz86eW4hZWiGuKw
        jnHhh0+VbIiAO5U4WIBYmlsBXhnijRP/FS+C9f73QnR3thKJ3XczyJKm8EM2NgggjxK43CnE4QHa
        ywKy3M2BSlENcXg1xSX7242lsBziVPvsqlLi8OqMv1T5MSGW5lYCC8MbEr7TB3lYU8fSJopbRmL3
        3AwWEQfCNDQkfzhECVzuFOIAfy+K94mW/QygAlRDHLeCvHgJuWNpYqiUOPypLRc2Y2JpSqFS4rCx
        46/N0Merac2rAVvVbgn5E19LPl/KSix+JbCIOEh9PX/C9o4ljr8VzRYmW76xNCuBaojDLhQCgapR
        uEqJwx8LcWHXLpamFColDvDtZHa2bvaHdiuF7Dt97MCFt5NvN6LE8Wc5SuByJxGH30y48GvKWJqV
        wJ1EHP5qkAuL71iaUqiGONmfapT74yq3GvwBFpdK/k7fqiNKHBclcLmTiMMawvf+s399ZKVRDXF8
        psZV4wl6LE0MlRKH2Z81HcI6ZKmfUWRRDXF4MdeFZzPVvj2wWvgNAWGd43+m6bYiS5q7hTjM6LwS
        4sLDstUY4GqIw06ayxNCLE0xaAf5upQjDqBfXPijj0u9/+f4UcFlKeJk38JmcuJ3T7F0txJsWviP
        /ngQf6v+fkRZlCTOHbqr5uBNXH8yz1qHJ9D8UnApNwmCoXDsxpV7JQRUQxz+qIQL79KV+/EXFpJX
        cfgDENmfHy9FHKxMdoeJvyhZSonYoMDV4isL2Z8fL0UcwM4V+SM8hGRi4il/uedRgHh2wrCO5bbj
        6XusciVjhXXh4bHLLwmxtLccJYmzQn/lplJUSxw6lbVO9nUNlJCf/KIs/DUZlATw8+JfFHhLm5/2
        sjPDw1Ne6Y/l7aiGOCgw+brwi07K5i+0kM87hD8t8CvY7E+N/e8xIEsRB7A9myUbL7rymyTemGa9
        R5uYRIofIrpUQhyUn69UOHkQf9gMWfnrM963/LWbfy3w/h8TAa8zsQPmfzEmBsYCC4KbzZ8M4w+d
        /32B9/loH78g5b2+3xKybzLwjmL275vdVmRJE4jjDz95S1oJXO404gBmuB8U6NDlyEoSB7D24o90
        VyLUmR+K/clwlUglxGHC4B7fNi4nKD4vkfL3DFwqIQ7AYvG0nh8gLkeWIk41QjsgZdlXb241sqQJ
        xHFJLQ6dB1g0Lrp5BYEf62U9lYZVCn6myx/M5r0mZrHi3wMxQ/MXZ4jjfbF/IvxhYaltTf74hteJ
        12diaYrBq/mk57tB2XpgWfirMUwKWCH/00O8ZuJl8AcrivMrBd5kZubnL+z7i5UIr6ew/mMG581v
        rAd/tMPLYDKI5VcKvDbDcyN+A+WWOitYBN7P4414fgSHpWcsyv1CFZcTy4LbhVXhgba7oC64l/xV
        JCwcfV/NhsuqAylJnMTyJJ/y49ueg4PDiz5qy6cCNwxsDB+95ROC/ulBwHnxtacB3E9eo9fHTHNK
        AGH9vevs6OFj9tTbnrbvf9e77T3vfm8B7/2B99n73vP+EP69z77L3v/eD9iP/PCP2g9+4Ifs2e95
        pz399mfs+NETtmloc8iH737yAV0+m9jR0mnrujbaxuGddvTZd9iR93yf7X/2KWsaHrSWjm7Vka9k
        16vJDTrovEFQu+s3DVvTwX3WcPKwNT/xoDU+dsoaHzhurcePWu7YUes6fty67j9pXQ+estxDJ6zz
        0VPW9YjOjyn9jvusft1Gq23sVN5NVlvTJFPOJxGbrfvgXuu7/4h1Hdhj1tqqchtVOh/fVf+STtfG
        91V5GJ3LmfX3W+eRo9b5wCnrfvQh6338Eet47CHLPfKAtT50v+UevD+U2/XACWs/vN+a79tudes3
        qA1dZho3a2yyjhOq74MnrefoIZXZFvKuaWjWuGicGHSED5bWCS26Z6DPmnZss67Dh6z71P3W8dBJ
        lafjYw9Yj8rve/Rh633wAes7dsJ6Dh6xlq33WW3/oPJWmS2Um7OOvQdt6KFHbUB1twY+Ecko1wRd
        aFCLdaZC1cZatZl2d/ZYbucu639A+T7xqHU8+ai1vPVRa3v749aq8joef8K63vqkdT31Vmt/4jFr
        OHbEbMsWs+5+jRvfp+VDkk3WVKv6k3ed0NZkNYPrbcPDj9rg0++0dc8+a23PPG3N73zaOt/zLut7
        t/D2t6nfVMfudapLi+rZpm7gy+q8DCD9ZdwkxYRxFCRLHL6ZOTCwIXy7k292Jk2vDZ9A5+O0kIeO
        cJJkiQO4Jp57/AjpiJubmQ+kmRibDEQaHtxkJ4/fb48+/JidOvmAHTpw2Hbdt9u2bdkevk7Nh3Ah
        xZ5de+3BUw/ZE489GQgDkd7xzLP25ONvsQP7Dobvi0J0PvfOd0dbG1TXnqFAnANPvd12PvM22yRF
        s4529U1zMoDhm510to5SrBZNFn0HD1j3iWPWcvSgNR7Ya7U7tpuhkCKbtfIxXUEEsw5h67DZnu3W
        fGy/dZ48Yn3HRYzdu61e9Q1KATkbUOJG6z+0Xwq5z3qVJ0pNHB/bbaxp1iBpwKgL9RBpmjdutC6R
        sGf/AevYf9DaD+y3DtUrd/igdUpxOk8es977T1iH6thz4oitP3ncNojUvbv3WNMGFFltVBmetvvQ
        gdA+q6cMJgwVF/6VQJymRmtct866d91n648qv+Mn1JZj1iqyNx1UnkLz/r3Wun+fdR46aP0ixaDI
        M3hQZe7cb/UD6ofOPrPmnOX27rd1Dz4Y0lg9C+qasAyo4buq4VPcOvJ9VT6lv3E41HnwxEkbOHlK
        7TlqLSJti+qdO3nCckdVB02qLWpbmyarzocetJ6UuAP7D1lTzzpl1xb6sb5O/UzePMzv67F1x5TP
        ocPWJb3qefJJyz36mLU88pCI+Xi47nr4Edv2tqdEdPVNm8aS++ubA3nQjZn89CKyZFGQLHH4uCwf
        xh2iYVJeJ05jfVO43rJpa1BStyROliyBIAzpCePoeUAayDM5PhXIAVFOHDtpe3fvs43rBwPBIBRp
        KYMjeUAMQF6EY1UgDKQ7duR4qCvpSA/Ztg5tsx1Du2x4yx7b/fjjNvSwZs0j+6VQCVnonIZGzVg6
        ZyCb1w1Y7327guI37rnPGnbtFDmYRaVwdKqlqNE9KLrqgSWxbhFpaL017N5hPVLs3n37pLwiWmMy
        e9XU6x7N9L3791jrru3WvXdXosDECXx6t4bZV/0VyKt7e3bssP5du2zwwCHr27nbWoY3mfVKMdsh
        rqxRs9KDfoVt3GBt27db3569tm6vFHv7Dlm99YHYrXv3WE7K3rFbZYq8YTZGeV1q1QutzdY20G89
        27Za/57dQZFbtjJZbDTr0WQButUPXVIu0LfOGjYOWm6zyty519bLwnRu22mNg1iBHmtSPVoOH7Dm
        nTuUf6oTTAj80pjysaidnVa3YcA61UYmKoDVqd+0WeUOqZwe9a3I39mrc7VRk7ht3mqNqhsTSN/B
        Q7Zek8rwzn3W1iHLo34MkxR93tZmrZuGbPDUcesT0bDYrYePaHz2Wp3Gt2Wf7r//Adv0xFsCwYdl
        lfAUwmSmetYxXpKp/IzN5eOkAUGCOQ0vemLA+SZo8tVolBMwgzOTQwiUFtcIJXZr4srNNWm45ugE
        AHwNukkz/c/9vZ83rULswx/6xWBZUH5IgyVzgnGvn3OETFg/6kEcZXHOPVs3b7P9mjX27dlfcNkg
        4O7tIsxWzWaD222YmVezJoobBi7UCyXSufJv0EB1adD6pLBtW7ZazeZhq9+qQWTmrkvcgXqhxWTN
        anLBLQhWAoWAiG067+u2xs1DltuyWbO3BrOZAUjJqVmwd89Oa96+SbO6FIpZMa0Dn3kPpFH6Zs36
        PXK5+u/bYZ3DqgNWuknEJR5QHhasSeUB2gIh2tutYWDAOraJmPftVB2kZINDQSFaBcKDKwqkvDVy
        zerkRmmArKWn03rV3r5t26yNMgPppLhNIiiTAyQHlAUIw62Tu96gvu7ZuiOQu3unXNChjSLObqvZ
        u8Nqt6n/UEQsK25o+JK30CELITeqaedWy+3baa27d1rDZk0MIp1pTK1JfR5cJpVRI+CCocz0AxOZ
        2skYDYh0W2TduvpFKqwoE6CUv76nR5PH1mBxek7K6m0TofEOpLcm62R1Qo9c+V37LCcSbpALmjt2
        UGHKW+3Lz0/ZhKzNWH5KVidOGhAE4iRIiOPANXNF5OhE4Ryrgxvn5ECZOUexuXaQR5YQKLpbl+3q
        dNYlWIgsyTwteVIWrqFbH8K9DO6BkBAQN+/+E6dstwaQdc624e22aWCzdKrfcuq8mk0bg2LTOXUy
        yYE40EHK2T20yTqGhi3HTLppkzVt3WINUqI2rVda2+X317eH1QgfZwd41WptMjsxaEG5lJ8UqkZK
        bC2JJaOMxoaEOO0iYv3wBim1lISZN/SV7qMeOq/TLNw+PGTdWmPkhgatobNLy6NWuQ5SVKUM6QAz
        KyRKSR/IBFRmTW+/NYkwWKgmtadFY8R5u9oS1nHB2iovORe1Om/szFnX4ID1qU6t60UY6h4IqXb5
        uovyA4HSssjHoWvcpVa5lpCzVvVu3CmS7lBbNUmEdnl9aXOL8ujX+nKb1s+75eIKEKhWRAqWlPyZ
        pOS+5po6rFmTVlh3uBsLcZvkxWj91715i3XIKua01qGfAnFETvq/VevYDk2SNVs13t3Kl8mlmU/2
        q31OSFmy7iOHrV3kqjsoi6x+yI9q/S3STMjaXM2PL00cF4iT/eY/Cgo5UG7ArA8RmO1RThQ6uwYK
        ZjlVaj+S3q9RdNwz8sHS7NyxK7h+nhaCAfLxeyFVtgzygEQcsWBYHdw91kjgsBatlDEkv7urRW6G
        Bq4WC7Cu1xoGNTtJyetlLZw4zbI2EKdlYL3VadbD12/UzFm/cb1mX7kliq+X24ACs7xt1cDm1PEd
        mrm0MkgUI+R1Y8KhntBbNliTDQongg5o4lmv/IQw+yoOVw1Yc6s19/VJATeEdEGBWQek+TAaSX7q
        w0DhxFLRjgKJUCyUvkPuXJ+8Aa3LGqVYjXgMWBHSQe5AHNVRCphTv3SqT3KqUyA8yk26MONrHFR3
        6o9OhMVymCB0DGWSl4DSirT0W63yqd2sNdaQ2skkpTGqUX0TkuvIpseGNM1muYLre4P1ad00bO0i
        fFNnd0gvR1//JuC/Rk1Q9dKFYMVV70AEoCVFrcpnw6NQH/VbrfqgrlcWrAvS0CYh5EfvaRKHOFrX
        5Pbssrq9ctP2bLX8a2csPzYuiwNxpm00P1meOPPzWqwXtt7mbHZ22mZmpsJxdj7x8y5duWinz75p
        L3/7JXvj9Os2NTNp5y+eC9eXr15KfEHd60fAvdOzU+Gc9B5GPq+/+d2Q54VL521iarxwr9/PfVev
        X7HvvPZtG5sYLYRNTk+E85m5abt4+YKdOXfavv3qK/b1b3zNvvyVL9nnv/g5+8rzz9mbZ96wyTmZ
        3PmJMIPMX7xo+XMXberN05anXXOTNqtZZXJixKauXbGJi+dt6sJ5mx+5ZvlJdd716zZ3Ttdnz1v+
        wkWbv3rV8hPsBupe3Yf/O5kep5mhVNak4rhmthpT2HhIo75UWfm5GZu5dMmmz5yzaeqia/LKK828
        7p1RuRPnFHf+QjLrzSh+di6sB+c1LjP5eQ18IlLnAJQKBStYrSx5cItxffsGrKm713I6olQ1uGq1
        oqEIVJdrs2atX5p6paztuDAiFJOmlBRLCqEhKIRlIlCk8tAEhxKGiTUhkyttY5dcVRExTE5YFa1h
        AsGoH3VjIpDLhEsJeXBt64c3BrJxX6uI16p618va0SYmpWaVkfgwafm8jEw961V+cDsFyEL7hcQl
        pDxdU88GpW3CM4AyWmYoTYt6T1O00qg+1EUy+bUXLf/6WctPaUw0LqMzo2F8yxLHSQOBiiNRUFfo
        0fERO3v+jL363e8EhX7t9VftxZe/FRQXEjlJSMt92XMnBAS6cu2ynbtwNgBSQAYI5enHJ8cCGSnL
        48kbghEOKb77xmuBwK985+VQH+oCib/10jfDNWVMq+Eo8sysyHNFin/+ko4ixjQkRsknRZzrIo7S
        Xr5s8yJLfnpKCiulndA9V3V9Vvecu2z5i1csf+2azWtGmtWkEtql+k7rCDifCsirw+dFnvkCceZ1
        pEzun714yWYhodoUSCNMT43Z9NUrNqs65K+PiLgJ0fJzIs6cJjWtB+fZUFGeEGhamFSZARrksPtD
        GToviCxYbXtnIE2LgDsaLEVqbWqbpULtWqtp0V/boVmZjQbWH0HpknUJqptQM7H1YfOoVoA0EEgI
        lgiIsLXyAmpzsszd3Vbb32v1sqAJcZS3FLpGXkqDLAoWPGxy4M6yLS9LV9/Vpbr0WLPqQ72oH3m6
        TQ47gMneVUIawGYD7Qm1Y219A+GuUE+iZcM0WchHEXGSCQeS0V9TZ87Y+Cuv2uS3X9MEqbEOxMFY
        JP0a9LaIE46yxHGFBpxDHkiCxYA8KCuz/Qu/+/UQNjJ2vUCa7H0OSIAlwVpAAq6dVMRfH70W8oc0
        EOvayNWQnrSEQ5iXXnkxgPIhLmHEkSdHLBnkm1WewSJAHClt/hLKLzKIhLMpcaYmRzTbX02UeXRU
        M72Io3oERef6MvcJHLlXxJmXYs/JatE/CYHUT9Q/ILEOACJBqqDUKjM/MhLIMQcBM8SZmZYvfe1q
        YtUocyqtw7yIMyvizGp8gEjEGCXkSQibWDzKJ69ktszLguU1ycxpLCbUd2OaGCbVr4kkiubEaejs
        kLLjAt0gDkrH+o0ZehFpHE6cYAsSsJmEO1Wf6whEAMHSQBzcZS3um3JdWmrI0rBjxjoKi9Eokmh9
        Uy9r1NjVqbWe3HJZwJpWEVEWplC+/i8AqwmIDfVI6pzU2/9LnlVBE1rFkZRBL+kjTeJj58/ayOuv
        29ibb2qiVP/PJMQhnv4s5kMWZYmTJQBAUZj53XVjdv/q175in/7sp4Kb9M0XfzeEo/BuSbAWbnWw
        OMRBBAhBXpCR/CAeRIQUkAHykBfnkATrAlGwKpxjZXDVsC5Zq0R+HKekRJOqM0eUNlibMblBqguz
        iRNnflSEQGHH5aIpLijt3KxmfnUuYbhOxCuP+dERmxMpIU/SyUrnUFnhwW6wEupLuVpYPfotP6v4
        MU0S18hjLBDPZ7O5KRFxRPlfE8bI1/PSkWde0wJHz5d7dC+EddJSxux8MtgQEqLOqQ+mx8dEGrmj
        Y6OhTM83kJ96qNyChPVPQgKUDkRJkyJsJoXUKeQeNcgdq9c6pF6WBDKEzQXlBHHqG7SOE2FaWzrC
        hkmtrw2DG5eQJ1i/zgy0PqnFulCm/ncL48TNkjdZ/0HURLDIWOdp6TYTGi5vaL/Gdl59Mzc2YmOX
        LtjIhXM2flnuM6444xTAxKUjfVUCyqw8ccIgSaEggV+jpO5SofBYHIjziU993D71mU8GMqHcKD7K
        zVrGCYOrBREAxINsrEtYo3zthecLxMAFhEhZskAi8iQ/J4zXiyOEwerh4k3MyhVTOMqbH5dCopwT
        yeJvSgo2nhfZphQ2DhQOGdQun2lCx+mamYmOnlWedDbHQBwsQ1iLgLTDUXBXdlkLr1twuyZ0z6jK
        GReBpMROnHncQxalo7JCk1Ju5eP9nOSZ5ss5hE7HhviQR5o2KSsljjCn9kPKGbV5Suu2edw/peUY
        yIo7qrqECWJacWoLceSDFXNZQJy6FClxUFhXXRS3DveKxTmuFpsGmTUOO5lNjVpXCQ11OE3J+iwQ
        CDeStRLPxXKyRKCdTQddswlQD1lS0gjBDUuF+tL+ZCKZVt0duLKarNVnc+r/0P5p9Y/6Ym7kenCP
        J69etnHp0ZR0hv7y/gmTYqavYwikKUccKgZxIItXknAfMMJxsVDq5776Zfv4J3/HfvvjvxVIBJkA
        4bh0z3/9q+EacmGlSPPJT3/CPveFz4Y4iAKZII2vVwDrGVwxSAFhsVxOZurg9SAOhLqqfqw16MCg
        HCjmVOIiLSAOMw1KFIiTKE7ibvlAyGLK3WMtMqu8sThhjaI6hIGAPIFAKPhCzKXKGogTLFiiqEF5
        lT99HAYJUk2kCqwB8/VTIC9WMJBGR6xnioQkylcgH8oK+XlYqgCBPKzr0rqQbnZG5NVkUGgH+Quh
        XkLSdsZ3Xnkmli4guI6cJ3UjresDx7DporyZaELe6ssEilP49IxILCTWUffqCILrlE5SYYyY4GQt
        Azinn2m/7gnurdoOGKuCTqoMR6gX7ZDrPa22M3nManIM1l6kwTWfunIpbMpMy0OhbiHv9F68Bbfu
        paAbShMnRhSvbHEcigyBUPJvfPMF+9JzX7TPfv4zgSSQA6JwhFSA8C9++QuBMLhnEKTYomClABaE
        8rL18Lr4OeVDKKxQILrCEuJooFn0YXUUh8JNiAwTbDdOMzAMkAZH5MESYKF8Ac62JGmn5pVW5Akd
        jEIwuKyDAnGEYHUEFAqk1oFBCIrMNcSBILqHvg19p2O4P8SlxFFYUjZpFJchy2LofvInPwbcr+kn
        yqYsTQjBqqVxQaHStoS1lhMnrXuIV9lMPEwgUfKQNi3T2xHOmbXJk51J+oh1mtdPfUuZALK4ZVwA
        2qT6QOowQRWIw9iIlLpvoVVJ9DC0G4S+Vz70adp2CDMrb2P2utaW168nEGHm2EFl7Kkv5Sov16lZ
        1Ttpd9K2GHRD5cRxpXULhKJyLE6DomMhIJFvIuBqQRBcM9wviAJBfL0DUVjrcE6+5JM04gZhgDeu
        uEzCqAvE4X62iIuJE1wudb4TJ8/GAYOcEgdlQ2GSnbEbxJkR5kSeBbOiykHxGNCEqInCQdhkxk7q
        GRQHixSsCopVhjiqJ3Wg7HGVPZmWHcpPMS8sIE9QOPWB7guKzRa2xnIWEk5RrvLMECfpJ00uQrA6
        tAfioORSvKRuySaE4wZ5UuVMQZmhDV4X8ppU3wQLjkJCGumXtxP3Nkw21JU46i7QhqC4shDsds5p
        fSawyYFyz8vaz8yOh3DGjd3KZGMkca1DH5Mflp52B+tOf6sOjC1rVF/HMt4OxlGELlhF1XdamBQf
        0BsfoxhUaGVrHJBV1uJzlDZGIicZA4ZCQyp3p2L5Aq4J93OP93Dy4pojIIy8HSFfpYUALAzDYMlN
        wtXiuQqzFZsDgQhyZUKHhhlSnad8Z3QPsz6Dg1vHrlUYXOUblAPCQELNnihgKD8MZLpNLHBOfROl
        0b0iTVhXSIHo29Am4pgh3RpJsXh+luSRbGcHV1FHgAUKazABRQ1kUX0TBVVeKXHw6WcgodZMARCH
        dGmfhToLuJ/B6kAE8gh5Ua+ELIuIQxmhnOQYyueavnHi0I+QhjZBGnSLdMXEgazBStMWxpdJiz6k
        zUm7gzXA+gjTM2Nat4o8GjfiZ5TW+yMhvQBx3LsY072aNAJ5wqSkMOoQJpgUBdJI73QNcWgvjxUS
        4kQ4kSI0rBQSMq0MGEwwqw4DM+q8WLpqkc3TERaDxKG86aChPDyDQTEJo8O5JjzsMqljwzMa0kuB
        bpA2JbY6q6A4KfCh3Y+GsD5BcG+A0rjShHIEXBbCPL9YPHkkZS+ED9oNJH3gCgq8P+jfWSlw2AhI
        ywzlAZ0nrlPSJ9l4h5cRKzcb7wh9muZLe8jX6xXGXkqNFQSc+zoiyYt2LG5rICiTm/IL2/bq57Bm
        Sfud8kK93VWGlEAE9Q0c+jQQKpA0qfsiqB7el5DF4WExFDo8htgN1aA4n9CBGUXPpl0OPO9snpx7
        HJ3ig5mFK4lfL1IgZl5X/DLIEscHFGISV5i9y5TP0eOL6xCQaWs5eD8D7wuIg6KRp5fn8Pw9Pluv
        BSBtGYR1jRDeMhGK+8PHp1CfFD5Wxe0oRqivFD5MKExsPhEIgRAQIQvIsWCSXKgTK4kFnV6M2A3V
        oFReNGQlGpPNO5vnjXJvKG8WYUDSuOL4QlwFxClWGj/3+GrKzypvITzT1nK40d6FE4nnWSjPkeaf
        LTNbriPb1hiKieP94CiuT1aRfazKIdSBMsib/k2xoF2gqC/9nuJysv3kdVsuFmW22sgWHotfDrJ5
        Lsx3oaIU4OGVxi+BYoValIawLIrjS2JhO4uxuL03FDUojBSobHlV16c0ivsg6YfF9S0OK49MGeRX
        jGx8UbpAoEj/rBSigfcMwo7OTSCWZzWI5VkNYnlmEFMKV5YQF8uzGhTlfesRIUalgDyrRBoQDbxn
        EFOGahDLM4OsksYQzbMaRMqsCrE8s0h34aLhIJbnrYRbllKIESaDMAaxfIVycZUgGrhSWKBEkfib
        Ram8V7PMLLycUojdk0WpdJXen0W16UGpeyot39NVgtj9S2IpkixBnlLl3lSdUkQDVwpewSxi6ZaL
        WL6xsFKoNF0p3Oz9K4lK2xsLXy68/VnE0i0fcUIUUI5UQqxOHsYaMBteLaKBa1jDGsohb/8f05bl
        E07MnLkAAAAASUVORK5CYII=
        " id="shrs-icon-pleiades-image10"></image> </g> </symbol>
    <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-telework"><title>demarche mon demenagement icon</title> <metadata id="shrs-icon-demarche-telework-metadata909"> <rdf:rdf> <cc:work rdf:about=""> <dc:format>image/svg+xml</dc:format> <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type> <dc:title>demarche mon demenagement icon</dc:title> </cc:work> </rdf:rdf> </metadata>  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="shrs-icon-demarche-telework-namedview905" showgrid="false" inkscape:zoom="5.3636364" inkscape:cx="34.958589" inkscape:cy="26.744902" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-monDemenagement"></sodipodi:namedview>   <g id="shrs-icon-demarche-telework-g2048" style="fill-opacity:1"> <path id="shrs-icon-demarche-telework-path898" d="M 44,7.5 C 28.053829,7.5011298 13.954203,17.853778 9.178178,33.067913 4.4021529,48.282049 10.055402,64.835525 23.14,73.95 V 46.8 l -7.88,6.44 -3.59,-4.36 11.6,-9.51 V 29.05 h 5.65 v 5.67 L 44.19,22.36 76.33,48.89 72.74,53.25 65.13,47 V 73.75 C 78.086607,64.556969 83.606505,48.037515 78.77915,32.902057 73.951796,17.766598 59.886643,7.4936664 44,7.5 Z" style="fill-opacity:1" inkscape:connector-curvature="0" sodipodi:nodetypes="cscccccccccccccsc"></path> </g> <path style="fill:none;fill-opacity:1;stroke:none;stroke-width:0.18644068" d="m 41.484012,78.870628 c -3.870673,-0.364021 -9.762748,-1.81726 -10.546778,-2.60129 -0.198001,-0.198001 -0.360002,-0.573055 -0.360002,-0.833452 0,-0.958685 0.412054,-4.142572 0.74782,-5.778306 0.798516,-3.890097 1.636868,-5.039318 4.382458,-6.007518 0.921144,-0.324831 1.939748,-0.693246 2.263566,-0.8187 0.54806,-0.212332 0.635411,-0.187138 1.263658,0.36447 2.849321,2.501737 7.801778,2.501737 10.651099,0 0.632473,-0.555319 0.71191,-0.577725 1.263657,-0.356429 0.323818,0.129877 1.332639,0.49172 2.241826,0.804094 2.766558,0.950523 3.596879,2.08423 4.404682,6.014083 0.460669,2.241099 0.798719,5.406943 0.649768,6.085108 -0.07212,0.32838 -0.318094,0.647737 -0.617109,0.801231 -1.657355,0.850769 -6.790183,1.99559 -10.471764,2.335613 -2.40044,0.221699 -3.437566,0.220127 -5.872881,-0.0089 z" id="shrs-icon-demarche-telework-path2034" inkscape:connector-curvature="0"></path> <path style="fill:none;fill-opacity:1;stroke:none;stroke-width:0.18644068" d="m 43.019252,63.040216 c -2.539199,-0.640558 -4.536536,-2.988252 -5.468289,-6.427491 -0.45906,-1.694458 -0.563046,-5.196327 -0.192184,-6.472047 0.945783,-3.253378 3.734404,-4.961575 7.781591,-4.766689 3.014135,0.145141 5.064081,1.362072 6.165568,3.66013 0.531073,1.107989 0.602537,1.457401 0.672987,3.290471 0.179239,4.6637 -1.793795,8.936086 -4.754322,10.294958 -1.096867,0.503459 -3.08773,0.702608 -4.205351,0.420668 z" id="shrs-icon-demarche-telework-path2036" inkscape:connector-curvature="0"></path> <g id="shrs-icon-demarche-telework-g2041"> <path inkscape:connector-curvature="0" id="shrs-icon-demarche-telework-path2038" d="M 39.712826,87.728284 C 30.886884,86.700377 22.946846,83.41568 16.306778,78.045487 14.09827,76.259344 10.686433,72.726959 9.0050953,70.485819 4.3189553,64.239428 1.2860339,56.684675 0.36684384,48.968658 0.08858012,46.63281 0.08488942,41.303794 0.36000033,39.087302 2.1883267,24.356987 10.633827,12.045279 23.678927,5.0933583 31.808861,0.76080033 42.057177,-0.87229813 51.272148,0.69629978 67.063179,3.3842932 80.278631,14.555023 85.44142,29.578827 c 1.713177,4.985379 2.392021,9.059516 2.392021,14.355932 0,6.941653 -1.362677,12.990941 -4.283798,19.01695 -2.237608,4.615984 -4.55309,7.939013 -8.031467,11.526243 -6.621424,6.828636 -14.671572,11.088416 -24.246028,12.829914 -1.694114,0.308141 -3.063539,0.401153 -6.525424,0.443199 -2.409746,0.02927 -4.675,0.01902 -5.033898,-0.02278 z m 9.881356,-2.057938 C 56.477803,84.81895 63.24836,82.072367 69.077232,77.766743 77.212018,71.757807 83.000469,62.775518 85.106807,52.892691 85.79949,49.642659 86.016282,47.535384 86.018784,44.02798 86.022363,39.021796 85.45088,35.446013 83.897437,30.754466 78.873989,15.583164 66.007557,4.8078709 49.960416,2.3331173 47.358894,1.9319166 41.192273,1.8765791 38.687402,2.2319567 28.028419,3.7441955 18.482313,8.9366284 11.890517,16.807641 6.7167127,22.985483 3.5926253,29.953938 2.3200565,38.155098 c -0.3849881,2.481084 -0.4455723,8.5525 -0.1088345,10.90678 1.4353913,10.035438 5.421335,18.11112 12.311745,24.944092 3.050919,3.025485 6.619403,5.568678 10.659925,7.597128 7.615384,3.823128 15.612378,5.155536 24.41129,4.067248 z" style="fill:none;fill-opacity:1;stroke:none;stroke-width:0.18644068"></path> </g> <g id="shrs-icon-demarche-telework-g2057"> <path style="fill-opacity:1" d="M44,0A44,44,0,1,0,88,44,44,44,0,0,0,44,0Zm0,86A42,42,0,1,1,86,44,42,42,0,0,1,44,86Z" id="shrs-icon-demarche-telework-path888"></path> <path inkscape:connector-curvature="0" d="m 56.915838,77.038726 a 28.154036,27.226737 0 0 1 -4.114851,1.102804 40.793646,39.450042 0 0 1 -8.245652,1.002538 40.793646,39.450042 0 0 1 -8.245651,-1.002538 28.154036,27.226737 0 0 1 -4.11485,-1.102804 c -1.068587,-0.385584 -1.702561,-0.543681 -1.666675,-1.781435 a 33.345446,32.247161 0 0 1 0.542267,-4.785216 c 0.350879,-1.927969 0.797451,-4.715804 2.527922,-5.972836 0.980865,-0.713349 2.280713,-0.94856 3.421068,-1.391993 a 13.748069,13.295254 0 0 0 1.527121,-0.643941 7.9027472,7.6424576 0 0 0 6.008798,2.548771 7.9027472,7.6424576 0 0 0 6.016774,-2.544915 13.748069,13.295254 0 0 0 1.52712,0.643942 c 1.140356,0.439576 2.424254,0.678643 3.421069,1.380423 1.738445,1.257034 2.177042,4.056438 2.527921,5.972833 a 33.345446,32.247161 0 0 1 0.542268,4.792932 c 0.03588,1.241612 -0.598088,1.41127 -1.674649,1.781435 z M 44.555335,63.280764 c -5.183436,0 -7.053461,-5.012713 -7.543893,-9.146272 -0.598089,-5.089831 1.877998,-8.922627 7.543893,-8.922627 5.665895,0 8.141984,3.832796 7.543894,8.922627 -0.490433,4.125848 -2.360457,9.146272 -7.543894,9.146272 z" id="shrs-icon-demarche-telework-path1467" style="fill-opacity:1;stroke-width:0.3921046"></path> </g> </symbol>
	<symbol viewBox="0 0 88 88" id="shrs-icon-alert3"><title>Planche Icones CSP_V1</title><path id="shrs-icon-alert3-alerte" d="M 85.912223,72.913267 49.875832,14.292289 a 7.008361,7.6297923 0 0 0 -5.70152,-3.31128 6.765497,7.3653936 0 0 0 -5.713087,3.31128 L 1.9391064,72.913267 a 8.2920706,9.0273285 0 0 0 -0.4972929,9.002147 7.8179075,8.5111214 0 0 0 6.9389712,4.608093 H 79.50524 a 7.9219921,8.6244352 0 0 0 6.938971,-4.608093 8.0954664,8.8132915 0 0 0 -0.531988,-9.002147 z m -41.969211,6.761054 a 5.5974367,6.0937615 0 0 1 -5.516482,-6.118942 5.5280471,6.018219 0 0 1 5.516482,-6.106352 5.6205667,6.1189423 0 0 1 0,12.225294 z M 39.051037,28.87199 a 6.6845423,7.2772607 0 0 1 9.841774,0 6.9389712,7.5542498 0 0 1 1.769438,5.577555 l -2.162645,27.698916 a 9.9342938,10.815167 0 0 0 -4.556592,-1.019824 11.669037,12.70373 0 0 0 -4.533461,1.070186 L 37.339425,34.499906 a 6.9389712,7.5542498 0 0 1 1.711612,-5.627916 z" style="stroke-width:1.20667"></path></symbol>
	 <symbol viewBox="0 0 88 88" id="shrs-icon-page-clocking"><title>demarche mes conges icon</title> <metadata id="shrs-icon-page-clocking-metadata3850"> <rdf:rdf> <cc:work rdf:about=""> <dc:format>image/svg+xml</dc:format> <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type> <dc:title>demarche mes conges icon</dc:title> </cc:work> </rdf:rdf> </metadata>  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="2880" inkscape:window-height="1526" id="shrs-icon-page-weeklyplanning-namedview3846" showgrid="false" inkscape:zoom="2.6818182" inkscape:cx="44" inkscape:cy="44" inkscape:window-x="2869" inkscape:window-y="-11" inkscape:window-maximized="1" inkscape:current-layer="g3843"></sodipodi:namedview>  <g id="shrs-icon-page-weeklyplanning-g3843"> <path class="cls-1" d="M44.34,0a44,44,0,1,0,44,44A44,44,0,0,0,44.34,0Zm0,86a42,42,0,1,1,42-42A42,42,0,0,1,44.34,86Z" id="shrs-icon-page-weeklyplanning-path3839"></path> <g id="shrs-icon-page-weeklyplanning-g3882" transform="translate(0.9457633,0.3728815)"> <circle cx="43.799999" cy="44" r="36.200001" id="shrs-icon-page-weeklyplanning-circle73"></circle> <path id="shrs-icon-page-weeklyplanning-path1152" class="st1" d="m 28.4,61.1 v -5.2 c 7.6,0 31.7,0 40.9,0 v 5.2 c -8.7,0.1 -35.1,0 -40.9,0 z m 0,-14.5 v -5.2 c 8.9,0 29.3,0 40.9,0 v 5.2 c -10.1,0 -33.3,-0.1 -40.9,0 z m 0,-14.5 v -5.2 c 11.2,0 28.4,0 41,0 v 5.2 c -5.2,0 -34.6,0 -41,0 z" inkscape:connector-curvature="0" style="fill:#fcfcfc"></path> <path class="st2" d="m 24.5,27.3 -0.9,-0.9 c -0.1,-0.1 -0.3,-0.1 -0.4,0 l -3.8,3.8 -1.6,-1.6 c -0.1,-0.1 -0.3,-0.1 -0.4,0 l -0.9,0.9 c -0.1,0.1 -0.1,0.3 0,0.4 l 2.7,2.7 c 0.1,0.1 0.3,0.1 0.4,0 l 5,-5 c 0,0 0,-0.2 -0.1,-0.3 z" id="shrs-icon-page-weeklyplanning-path78" inkscape:connector-curvature="0" style="fill:#ffffff"></path> <path class="st2" d="m 24.5,41.2 -0.9,-0.9 c -0.1,-0.1 -0.3,-0.1 -0.4,0 l -3.8,3.8 -1.6,-1.6 c -0.1,-0.1 -0.3,-0.1 -0.4,0 l -0.9,0.9 c -0.1,0.1 -0.1,0.3 0,0.4 l 2.7,2.7 c 0.1,0.1 0.3,0.1 0.4,0 l 5,-5 c 0,0 0,-0.2 -0.1,-0.3 z" id="shrs-icon-page-weeklyplanning-path80" inkscape:connector-curvature="0" style="fill:#ffffff"></path> <path class="st2" d="m 24.5,55.7 -0.9,-0.9 c -0.1,-0.1 -0.3,-0.1 -0.4,0 L 19.4,58.6 17.7,57 c -0.1,-0.1 -0.3,-0.1 -0.4,0 l -0.9,0.9 c -0.1,0.1 -0.1,0.3 0,0.4 l 2.7,2.7 c 0.1,0.1 0.3,0.1 0.4,0 l 5,-5 c 0.1,0 0.1,-0.2 0,-0.3 z" id="shrs-icon-page-weeklyplanning-path82" inkscape:connector-curvature="0" style="fill:#ffffff"></path> </g> </g> <style type="text/css" id="shrs-icon-page-weeklyplanning-style67"> .st0{fill:none;stroke:#000000;stroke-width:3;stroke-miterlimit:10;} .st1{fill:#FCFCFC;} .st2{fill:#FFFFFF;} </style> </symbol>
	 <symbol viewBox="0 0 88 88" id="shrs-icon-demarche-clocking"><title>demarche mes conges icon</title> <metadata id="shrs-icon-demarche-clocking-metadata3850"> <rdf:rdf> <cc:work rdf:about=""> <dc:format>image/svg+xml</dc:format> <dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type> <dc:title>demarche mes conges icon</dc:title> </cc:work> </rdf:rdf> </metadata>  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#ffffff" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="2880" inkscape:window-height="1526" id="shrs-icon-demarche-weeklyplanning-namedview3846" showgrid="false" inkscape:zoom="2.6818182" inkscape:cx="44" inkscape:cy="44" inkscape:window-x="2869" inkscape:window-y="-11" inkscape:window-maximized="1" inkscape:current-layer="g3843"></sodipodi:namedview>  <g id="shrs-icon-demarche-weeklyplanning-g3843"> <path class="cls-1" d="M44.34,0a44,44,0,1,0,44,44A44,44,0,0,0,44.34,0Zm0,86a42,42,0,1,1,42-42A42,42,0,0,1,44.34,86Z" id="shrs-icon-demarche-weeklyplanning-path3839"></path> <g id="shrs-icon-demarche-weeklyplanning-g3882" transform="translate(0.9457633,0.3728815)"> <circle cx="43.799999" cy="44" r="36.200001" id="shrs-icon-demarche-weeklyplanning-circle73"></circle> <path id="shrs-icon-demarche-weeklyplanning-path1152" class="st1" d="m 28.4,61.1 v -5.2 c 7.6,0 31.7,0 40.9,0 v 5.2 c -8.7,0.1 -35.1,0 -40.9,0 z m 0,-14.5 v -5.2 c 8.9,0 29.3,0 40.9,0 v 5.2 c -10.1,0 -33.3,-0.1 -40.9,0 z m 0,-14.5 v -5.2 c 11.2,0 28.4,0 41,0 v 5.2 c -5.2,0 -34.6,0 -41,0 z" inkscape:connector-curvature="0" style="fill:#ffffff"></path> <path class="st2" d="m 24.5,27.3 -0.9,-0.9 c -0.1,-0.1 -0.3,-0.1 -0.4,0 l -3.8,3.8 -1.6,-1.6 c -0.1,-0.1 -0.3,-0.1 -0.4,0 l -0.9,0.9 c -0.1,0.1 -0.1,0.3 0,0.4 l 2.7,2.7 c 0.1,0.1 0.3,0.1 0.4,0 l 5,-5 c 0,0 0,-0.2 -0.1,-0.3 z" id="shrs-icon-demarche-weeklyplanning-path78" inkscape:connector-curvature="0" style="fill:#ffffff"></path> <path class="st2" d="m 24.5,41.2 -0.9,-0.9 c -0.1,-0.1 -0.3,-0.1 -0.4,0 l -3.8,3.8 -1.6,-1.6 c -0.1,-0.1 -0.3,-0.1 -0.4,0 l -0.9,0.9 c -0.1,0.1 -0.1,0.3 0,0.4 l 2.7,2.7 c 0.1,0.1 0.3,0.1 0.4,0 l 5,-5 c 0,0 0,-0.2 -0.1,-0.3 z" id="shrs-icon-demarche-weeklyplanning-path80" inkscape:connector-curvature="0" style="fill:#ffffff"></path> <path class="st2" d="m 24.5,55.7 -0.9,-0.9 c -0.1,-0.1 -0.3,-0.1 -0.4,0 L 19.4,58.6 17.7,57 c -0.1,-0.1 -0.3,-0.1 -0.4,0 l -0.9,0.9 c -0.1,0.1 -0.1,0.3 0,0.4 l 2.7,2.7 c 0.1,0.1 0.3,0.1 0.4,0 l 5,-5 c 0.1,0 0.1,-0.2 0,-0.3 z" id="shrs-icon-demarche-weeklyplanning-path82" inkscape:connector-curvature="0" style="fill:#ffffff"></path> </g> </g> <style type="text/css" id="shrs-icon-demarche-weeklyplanning-style67"> .st0{fill:none;stroke:#000000;stroke-width:3;stroke-miterlimit:10;} .st1{fill:#FFFFFF;} .st2{fill:#FFFFFF;} </style> </symbol>
	 		<symbol viewBox="0 0 90 90" id="shrs-icon-demarche-clockingcorrection">
		<metadata id="metadata5114">
		<rdf:rdf>
		  <cc:work rdf:about="">
			<dc:format>image/svg+xml</dc:format>
			<dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type>
			<dc:title>demarche ckcorr</dc:title>
		  </cc:work>
		</rdf:rdf>
	  </metadata>
	  <defs id="defs5112"></defs>
	  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="namedview5110" showgrid="false" inkscape:zoom="3.1363636" inkscape:cx="33.260962" inkscape:cy="40.181628" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-clockingcorrection"></sodipodi:namedview>
	  <title id="title5091">demarche ckcorr</title>
	  <g transform="translate(-0.809322,-0.80932209)" id="g1165">
		<g id="g1163">
		  <g id="g1161">
			<path inkscape:connector-curvature="0" class="st0" d="m 35.2,80.3 c 3.1,0.9 6.4,1.3 9.8,1.3 3.4,0 6.7,-0.5 9.8,-1.3 z" id="path1157"></path>
		  </g>
		</g>
	  </g>
	  <g id="g2180" transform="matrix(0.66954073,0,0,0.66954073,13.924836,26.971881)">
		<g id="g2176">
		  <g id="g1753">
			<g id="g1758"></g>
		  </g>
		</g>
	  </g>
	  <g transform="translate(-0.809322,-0.80932209)" id="g2329">
		<path sodipodi:nodetypes="sscsssccsscccccccccccccccsssccsssssscccsssccsssccccc" inkscape:connector-curvature="0" id="path1159" d="M 45,8.4003906 C 24.8,8.4003906 8.4003906,24.8 8.4003906,45 c 0,8.054089 2.5981214,15.521735 7.0039064,21.572266 V 44.787109 c 0,-3.61552 2.945575,-6.49414 6.494141,-6.49414 h 45.929687 c 3.548566,0 6.494688,2.87862 6.427734,6.49414 V 66.945312 C 78.890008,60.820025 81.599609,53.265476 81.599609,45 81.599609,24.8 65.2,8.4003906 45,8.4003906 Z M 74.255859,66.945312 v 0 M 61.570312,77.527344 c 0,0 1.676967,-0.91653 0,0 m 0,0 H 28.09375 c 9.860184,5.468616 21.5588,6.513533 33.476562,0 z M 18.4175,70.398438 c 1.447652,1.429029 3.324773,2.845433 4.741,3.916015 H 66.609 c 1.297779,-0.945272 2.595558,-1.977243 3.891,-3.115234 l 0.609,-0.621094 3.75e-4,-19.363281 H 18.617188 Z M 71.5,23.449219 c 0.15,0 0.300391,0.05039 0.400391,0.15039 L 73.800781,25.5 C 73.900781,25.6 74,25.700391 74,25.900391 l -0.300781,0.298828 -10,10 c -0.1,0.1 -0.298438,0.201172 -0.398438,0.201172 -0.1,0 -0.30039,-0.101172 -0.40039,-0.201172 l -5.5,-5.5 c -0.2,-0.2 -0.2,-0.598828 0,-0.798828 L 59.300781,28 c 0.2,-0.2 0.695159,-0.263159 0.798828,0 l 3.300782,3.300781 7.699218,-7.701172 c 0.1,-0.1 0.250391,-0.15039 0.400391,-0.15039 z m -49.601562,18.05664 c -1.80776,0 -3.28125,1.47349 -3.28125,3.28125 v 3.146485 h 52.492187 v -3.146485 c 0,-1.80776 -1.47349,-3.28125 -3.28125,-3.28125 z m 1.673828,14.060547 h 20.554687 v 5.021485 H 23.572266 Z"></path>
		<path id="path5093" d="m 44.809322,0.80932209 a 44,44 0 1 0 44,43.99999991 44,44 0 0 0 -44,-43.99999991 z m 0,85.99999991 a 42,42 0 1 1 42,-42 42,42 0 0 1 -42,42 z" inkscape:connector-curvature="0"></path>
	  </g>
	</symbol>
		<symbol viewBox="0 0 90 90" id="shrs-icon-demarche-clockingcorrectionmanager">
		
		<metadata id="metadata5114">
		<rdf:rdf>
		  <cc:work rdf:about="">
			<dc:format>image/svg+xml</dc:format>
			<dc:type rdf:resource="http://purl.org/dc/dcmitype/StillImage"></dc:type>
			<dc:title>demarche ckcorr man</dc:title>
		  </cc:work>
		</rdf:rdf>
	  </metadata>
	  <defs id="defs5112"></defs>
	  <sodipodi:namedview pagecolor="#ffffff" bordercolor="#666666" borderopacity="1" objecttolerance="10" gridtolerance="10" guidetolerance="10" inkscape:pageopacity="0" inkscape:pageshadow="2" inkscape:window-width="1366" inkscape:window-height="705" id="namedview5110" showgrid="false" inkscape:zoom="3.1363636" inkscape:cx="33.260962" inkscape:cy="40.181628" inkscape:window-x="-8" inkscape:window-y="-8" inkscape:window-maximized="1" inkscape:current-layer="shrs-icon-demarche-clockingcorrectionmanager"></sodipodi:namedview>
	  <title id="title5091">demarche ckcorr man</title>
	  <g transform="translate(-0.809322,-0.80932209)" id="g1165">
		<g id="g1163">
		  <g id="g1161">
			<path inkscape:connector-curvature="0" class="st0" d="m 35.2,80.3 c 3.1,0.9 6.4,1.3 9.8,1.3 3.4,0 6.7,-0.5 9.8,-1.3 z" id="path1157"></path>
		  </g>
		</g>
	  </g>
	  <g id="g2180" transform="matrix(0.66954073,0,0,0.66954073,13.924836,26.971881)">
		<g id="g2176">
		  <g id="g1753">
			<g id="g1758"></g>
		  </g>
		</g>
	  </g>
	  <g transform="translate(-0.809322,-0.80932209)" id="g2329">
		<path sodipodi:nodetypes="sscsssccsscccccccccccccccsssccsssssscccsssccsssccccc" inkscape:connector-curvature="0" id="path1159" d="M 45,8.4003906 C 24.8,8.4003906 8.4003906,24.8 8.4003906,45 c 0,8.054089 2.5981214,15.521735 7.0039064,21.572266 V 44.787109 c 0,-3.61552 2.945575,-6.49414 6.494141,-6.49414 h 45.929687 c 3.548566,0 6.494688,2.87862 6.427734,6.49414 V 66.945312 C 78.890008,60.820025 81.599609,53.265476 81.599609,45 81.599609,24.8 65.2,8.4003906 45,8.4003906 Z M 74.255859,66.945312 v 0 M 61.570312,77.527344 c 0,0 1.676967,-0.91653 0,0 m 0,0 H 28.09375 c 9.860184,5.468616 21.5588,6.513533 33.476562,0 z M 18.4175,70.398438 c 1.447652,1.429029 3.324773,2.845433 4.741,3.916015 H 66.609 c 1.297779,-0.945272 2.595558,-1.977243 3.891,-3.115234 l 0.609,-0.621094 3.75e-4,-19.363281 H 18.617188 Z M 71.5,23.449219 c 0.15,0 0.300391,0.05039 0.400391,0.15039 L 73.800781,25.5 C 73.900781,25.6 74,25.700391 74,25.900391 l -0.300781,0.298828 -10,10 c -0.1,0.1 -0.298438,0.201172 -0.398438,0.201172 -0.1,0 -0.30039,-0.101172 -0.40039,-0.201172 l -5.5,-5.5 c -0.2,-0.2 -0.2,-0.598828 0,-0.798828 L 59.300781,28 c 0.2,-0.2 0.695159,-0.263159 0.798828,0 l 3.300782,3.300781 7.699218,-7.701172 c 0.1,-0.1 0.250391,-0.15039 0.400391,-0.15039 z m -49.601562,18.05664 c -1.80776,0 -3.28125,1.47349 -3.28125,3.28125 v 3.146485 h 52.492187 v -3.146485 c 0,-1.80776 -1.47349,-3.28125 -3.28125,-3.28125 z m 1.673828,14.060547 h 20.554687 v 5.021485 H 23.572266 Z"></path>
		<path id="path5093" d="m 44.809322,0.80932209 a 44,44 0 1 0 44,43.99999991 44,44 0 0 0 -44,-43.99999991 z m 0,85.99999991 a 42,42 0 1 1 42,-42 42,42 0 0 1 -42,42 z" inkscape:connector-curvature="0"></path>
	  </g>
	</symbol>
	
</svg>
<div class="pt-wrapper foryou-header-nav"><div class="main-menu" style="display:none"><div class="left-part"><button id="burgerMenuButton" class="access-menu" data-i18n="[title]appforyou_title_menu" title="Menu"><i class="fa fa-bars fa-lg"></i></button><div class="logosoprahr pt-trigger" data-animation="2" data-goto="1"><a id="logosoprahr" href="javascript:void(0);" data-i18n="[title]appforyou_title_home" title="Accueil"><svg aria-hidden="true" style="fill:#fff;height:30px;padding-left:12px;padding-right:12px;width:46px;margin-top:5px"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-home"></use></svg></a></div><div class="logopleiades"><a id="logopleiades"><svg aria-hidden="true" class="shrs-icon shrs-icon_size_large shrs-transition"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-pleiades"></use></svg></a></div><div id="envname" style="margin-top: 5px; font-weight: bold; font-size: xx-large; color: antiquewhite;">Environnement de 4You AXA</div><div id="btns-spaces"><div class="space-switcher"><a href="#/syd" id="space-switcher-link"><span id="manager" data-i18n="[title]appforyou_title_manager" title="Accéder à mon espace"><svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-person"></use></svg> </span><span id="expert" data-i18n="[title]appforyou_title_expert" title="Accéder à mon espace gestionnaire"><svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-gestionnaire"></use></svg></span></a><div class="black-disk shrs-transition"></div></div><h3 class="space-name"></h3></div></div><h1 id="pageTitle"></h1><div class="right-part"><div id="gestrole-selection-space" class="gestrole-selection-space-content hide"><a class="gestrole-selection_icon" data-i18n="[title]appforyou_title_gestRoleSelection" title="Ouvrir la séléction d'un rôle gestionnaire par défaut"><svg aria-hidden="true" class="shrs-button__icon shrs-icon shrs-icon_size_large"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-gestionnaire"></use></svg></a></div><div id="search-space" class="search-space-content hide"><div id="navbarInputSearch" class="bare"><label class="hide" for="navBarSearchTextId"></label> <input id="navBarSearchTextId" class="ui-autocomplete-input" type="text" name="navBar-search-text" data-i18n="[placeholder]appforyou_placeholder_search" autocomplete="off" min="2" placeholder="Rechercher une page"> <a class="search_icon" data-i18n="[title]appforyou_title_search" title="Rechercher"><svg aria-hidden="true" class="shrs-button__icon shrs-icon shrs-icon_size_large"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-search"></use></svg> </a><a href="javascript:void(0)" class="search-close" data-i18n="[title]appforyou_title_search_close" title="Fermer la zone de Recherche"><i class="fa fa-times"></i></a></div></div><div id="question-space"><a href="#/demarches/ticketing/create"><div id="inputquestion" data-i18n="[title]appforyou_title_help" title="Aide"><svg aria-hidden="true" class="shrs-icon shrs-question-mark shrs-icon_size_large"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-help"></use></svg></div></a></div><div class="navbar-logout-space pull-right"><a href="#/logout" id="navbar-logout-link-id" class="navbar-logout-link" data-i18n="[title]appforyou_title_logout" title="Se déconnecter"><svg aria-hidden="true" class="shrs-icon navbar-logout-icon shrs-icon_size_large"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-shutdown"></use></svg></a></div></div><script id="main-menu-script" type="text/html"> {{#each this.menu_content}} {{>menu-items}} {{/each}} <li class="poweredBy"><a id="4YOU" href="javascript:void(0);">Powered by&nbsp;<img src="/resources/images/PoweredBySopraHR.png" srcset="/resources/images/PoweredBySopraHR-big.png 2x" alt=""></a></li></script><script id="menu-items" type="test/html"> {{#if !this.uiConfig.hidden}} {{#if this.id === 'legacyMenuId'}} {{#if this.children && this.children.length}} {{>menuLegacy-items}} {{else}} {{>menuLegacy-noitems}} {{/if}} {{else}} {{>menuForyou-items}} {{/if}} {{/if}} </script><script id="menuLegacy-items" type="test/html"> {{#if !this.children || !this.children.length}} <li class="white clickable {{#if this.legacyRoles !== undefined && this.legacyRoles}}has-multi-roles{{else}}empty-menu-items{{/if}}"> {{>menuLegacy-item}} </li> {{else}} <li class="white submenu {{>menuLegacy-attributes}}"> {{>menuLegacy-root-item}} <ul> {{#each this.children}} {{>menuLegacy-items}} {{/each}} </ul></li> {{/if}} </script><script id="menuLegacy-attributes" type="test/html"> {{#if this.children.length === 0}} empty-menu-items {{/if}} {{#if this.uiConfig.classes}} {{#each this.uiConfig.classes}} {{this}} {{/each}} {{/if}} </script><script id="menuLegacy-item" type="text/html"><a class="{{this.accessMode !== 'standalone' && this.accessMode !== 'embedded' ? 'link-with-icon' : 'link-without-icon'}}" href="javascript:void(0);" on-click="handleLegacyItemClick(this)"><span>{{this.name}}</span></a> {{#if this.accessMode !== 'standalone' && this.accessMode !== 'embedded'}} {{#if !this.legacyRoles}} <a class="icon-container" href="{{this.link}}" target="_blank"><img class="sub-menu-icon" src="/theme/images/wte_new_window.gif" alt="open in new tab"> </a> {{else}} <a class="icon-container" href="javascript:void(0);" on-click="openLegacyRolesPopup(this)"><img class="sub-menu-icon" src="/theme/images/wte_new_window.gif" alt="open in new tab"> </a> {{/if}} {{/if}} </script><script id="menuLegacy-root-item" type="text/html"><a href="javascript:void(0);" {{#if this.clickable}} class="link-with-icon" {{/if}}> {{#if this.uiConfig.icon}} <svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{this.uiConfig.icon}}"></use></svg> {{/if}} <span>{{this.name}}</span></a> {{#if this.id === 'legacyMenuId'}} <span id="loading-legacy-site-map-icon" class="hide"></span> {{/if}} {{#if this.clickable}} <a class="icon-container" href="javascript:void(0);" on-click="handleLegacyItemClick(this)"><svg aria-hidden="true" class="shrs-icon shrs-icon_size_medium sub-menu-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-open-transaction"></use></svg> </a> {{/if}} <i class="fa fa-caret-right fa-lg sub-menu-icon" aria-hidden="true"></i></script><script id="menuLegacy-noitems" type="test/html"><li class="{{this.uiConfig.theme ? this.uiConfig.theme : ''}}" on-click="openLegacyRolesPopup({category: null, id: null, name: null, fullName: null, link: this.link, legacyRoles: null})"> {{>menu-item}} </li></script><script id="menuForyou-items" type="test/html"> {{#if this.subItems === undefined}} <li class="{{this.uiConfig.theme ? this.uiConfig.theme : ''}} {{this.subItems ? 'submenu' : 'clickable'}}" on-click="handleClick(this.action)"> {{>menu-item}} </li> {{/if}} {{#if this.subItems !== undefined}} <li class="{{this.uiConfig.theme ? this.uiConfig.theme : ''}} {{this.subItems ? 'submenu' : 'clickable'}}"> {{>submenu-root-item}} <ul> {{#each this.subItems}} {{>menu-items}} {{/each}} </ul></li> {{/if}} </script><script id="menu-item" type="text/html"><a {{>menu-item-attr}}> {{#if this.uiConfig.icon !== undefined}} <svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{this.uiConfig.icon}}"></use></svg> {{/if}} <span>{{this.label}}</span></a> {{#if this.id === 'legacyMenuId'}} <span id="loading-legacy-site-map-icon" class="hide"></span> {{/if}} {{#if this.action.openinothertab !== undefined && this.action.openinothertab}} <i class="fa fa-files-o fa-lg sub-menu-icon" aria-hidden="true"></i> {{/if}} </script><script id="submenu-root-item" type="text/html"><a {{>menu-item-attr}}> {{#if this.uiConfig.icon !== undefined}} <svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{this.uiConfig.icon}}"></use></svg> {{/if}} <span>{{this.label}}</span></a> {{#if this.subItems.length > 0}} <i class="fa fa-caret-right fa-lg sub-menu-icon hidden-xs" aria-hidden="true"></i> {{/if}} </script><script id="menu-item-attr" type="text/html">id={{this.id}} {{#if this.uiConfig.classes !== undefined}} class="{{#each this.uiConfig.classes}} {{this}} {{/each}}" {{/if}} {{#each this.uiConfig.attributes}} {{this.attr}} = "{{this.value}}" {{/each}} href = {{(this.action !== undefined && this.action.href !== undefined) ? this.action.href : "javascript:void(0);"}} </script></div><ul id="mainMenuList"></ul><ul id="gestRoleSelectList" class="hide"><div id="gestRoleSelectSpinner" class="hide"></div></ul><div class="mobile-header" style="display:none"><div class="col-xs-10"><div id="mobile-pageTitle"></div></div><div class="col-xs-2 text-right"><button class="showInfo shrs-button shrs-button_neutral shrs-button_type_icon shrs-transition" aria-label="The action behind the icon button" style="display:none"><svg aria-hidden="true" class="shrs-button__icon shrs-icon shrs-icon_size_large shrs-transition"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-info"></use></svg> <span class="shrs-assistive-text" data-i18n="appforyou_info">Info</span></button></div></div><div class="spaces"><div id="login-1752143932922" data-space-instanceid="login-1752143932922" class="pt-perspective">  <div id="content" class="pt-page pt-page-0 pt-page-current" data-page-name="login" data-page-number="0" data-page-title="Se connecter" data-page-show-title="undefined" style="height: 100vh; margin-top: -59px;">
			<div id="loginGrid" class="grid-stack large grid-stack-instance-1005" data-gs-current-height="1" style="height: 1px;"><div class="grid-stack-item area" data-gs-locked="yes" grid-stack-item-hidden="false" id="sA" data-gs-x="0" data-gs-y="0" data-gs-width="12" data-gs-height="1"><div class="grid-stack-item-content"><div class="grid-stack grid-stack-1 grid-stack-instance-8446 grid-stack-nested" data-nbcolumns="1" data-userlocked="null" data-gs-current-height="1" style="height: 1px;"><div data-gs-no-resize="false" data-gs-no-move="false" data-gs-x="0" data-gs-y="0" data-gs-width="1" data-gs-height="1" class="grid-stack-item"><div class="grid-stack-item-content " data-placeholder-instance-id="login-c4fa221e-1bbd-fd314613198d" data-placeholder-widget-id="" data-placeholder-name="login" data-placeholder-hidden="false"><div class="widget-body loaded"><widget-login ng-version="16.2.12"></widget-login></div></div></div></div></div><div class="shadowSector"></div></div></div>
		</div>  </div></div></div><div id="modalPopupProgressBarDiv" class="hide"><button type="button" style="display:none" class="btn btn-default btn-radius-md isenabled btn_abondan" data-i18n="appforyou_button_cancel">Abandonner</button></div><div id="4YOUmodal" class="modal fade" tabindex="-1" role="dialog" data-keyboard="true" aria-modal="true" aria-labelledby="modal-heading" style="display:none"><div class="modal-dialog"><div class="modal-content"><div class="closeButton"><i tabindex="0" class="fa fa-times" data-dismiss="modal" aria-label="Fermer la modale"></i></div><div class="content"><div id="modalPopupHeader" class="header"></div><div id="modalPopupBodySpinner" class="hide body"></div><div id="modalPopupBody" class="body"></div><div id="modalPopupFooter" class="footer"></div></div></div></div></div><script type="text/x-handlebars" id="tpl-grid"> {{#each grids}} <div id="content" class="pt-page pt-page-{{ pageNumber }}" data-page-name="{{ pageName }}" data-page-number="{{ pageNumber }}">
			<div id="{{ gridId }}" class="grid-stack large"></div>
		</div> {{/each}} </script><style type="text/css">#mainMenuList a{text-decoration:none}</style><script data-main="main.js" src="/resources/js/lib/require/require-2.1.17.min.js"></script></body></html>