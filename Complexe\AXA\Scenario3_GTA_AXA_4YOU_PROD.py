#!/usr/bin/env python3
import logging
import sys
import os
import json
from selenium.webdriver.common.by import By

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from test_automation import (
    TestAutomation,
    check_url_availability,
    check_and_log,
    reset_scenario_variables,
)

def load_credentials():
    """Load credentials from JSON file"""
    try:
        script_dir = os.path.dirname(os.path.abspath(__file__))
        credentials_path = os.path.join(script_dir, "credentials.json")
        with open(credentials_path, "r") as f:
            credentials = json.load(f)
        return credentials["axa"]["username"], credentials["axa"]["password"]
    except Exception as e:
        logging.error(f"Error loading credentials: {e}")
        raise

# Set the URL
url = "https://ple5-adp-axa.soprahronline.sopra/app/foryou/#/login"

# Pre-check the URL
if not check_url_availability(url):
    print("Exiting script as the URL is not reachable.")
    exit(1)

# Load credentials
username, password = load_credentials()

# Reset scenario variables
reset_scenario_variables()

# Instantiate TestAutomation class
test = TestAutomation(url)

try:
    test.setup_driver()

    # Transaction 1: Navigation and authentication
    check_and_log(
        test.driver,
        test.navigate_to_url,
        "Step 1: Navigate to URL",
        measure_time=True,
        unique_str="step1"
    )

    shadow_host = check_and_log(
        test.driver,
        test.locate_shadow_host,
        "Step 2: Locate shadow host",
        measure_time=True,
        unique_str="step2"
    )

    test.shadow_root = check_and_log(
        test.driver,
        lambda: test.access_shadow_root(shadow_host),
        "Step 3: Access shadow root",
        measure_time=True,
        unique_str="step3"
    )

    test.take_screenshot("Before login")

    # Transaction 2: Login process
    username_field = check_and_log(
        test.driver,
        test.locate_username_field,
        "Step 4: Locate username field",
        measure_time=True,
        unique_str="step4"
    )
    username_field.send_keys(username)

    password_field = check_and_log(
        test.driver,
        test.locate_password_field,
        "Step 5: Locate password field",
        measure_time=True,
        unique_str="step5"
    )
    password_field.send_keys(password)

    login_button = check_and_log(
        test.driver,
        test.locate_login_button,
        "Step 6: Locate login button",
        measure_time=True,
        unique_str="step6"
    )
    login_button.click()

    test.take_screenshot("After login")

    # Transaction 3: Menu navigation
    check_and_log(
        test.driver,
        test.locate_burger_menu_button,
        "Step 7: Open burger menu",
        measure_time=True,
        unique_str="step7"
    ).click()

    check_and_log(
        test.driver,
        test.locate_gestion_administrative_paie_link,
        "Step 8: Select Gestion Administrative/Paie",
        measure_time=True,
        unique_str="step8"
    ).click()

    check_and_log(
        test.driver,
        test.locate_link_text_temps_activite,
        "Step 9: Select Temps et activité",
        measure_time=True,
        unique_str="step9"
    ).click()

    check_and_log(
        test.driver,
        test.locate_link_text_suivi_des_temps,
        "Step 10: Select Suivi des temps",
        measure_time=True,
        unique_str="step10"
    ).click()

    test.take_screenshot("After navigation")

    # Transaction 4: AXA France tab
    axa_france_tab = check_and_log(
        test.driver,
        lambda: test.shadow_root.find_element(By.CSS_SELECTOR, "span.white-2"),
        "Step 11: Locate AXA France tab",
        measure_time=True,
        unique_str="step11"
    )
    axa_france_tab.click()
    test.take_screenshot("AXA France tab opened")

    # Transaction 5: Logout process
    logout_button = check_and_log(
        test.driver,
        test.locate_logout_button,
        "Step 12: Locate logout button",
        measure_time=True,
        unique_str="step12"
    )
    logout_button.click()
    test.take_screenshot("After logout")

except Exception as e:
    logging.error(f"Test failed: {str(e)}")
    test.take_screenshot("Error_state")
    raise

finally:
    test.teardown_driver()
