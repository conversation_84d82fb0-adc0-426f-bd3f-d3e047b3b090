<html lang="fr" class="no-js"><head><meta charset="utf-8"><meta http-equiv="x-ua-compatible" content="ie=edge"><title data-i18n="appforyou_title"></title><meta name="description" content=""><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=0"><link rel="shortcut icon" type="image/png" href="/resources/icons/favicon.png"></head><body class=""><div class="pt-wrapper foryou-header-nav"><div class="main-menu" style="display:none"><div class="left-part"><button id="burgerMenuButton" class="access-menu" data-i18n="[title]appforyou_title_menu"><i class="fa fa-bars fa-lg"></i></button><div class="logosoprahr pt-trigger" data-animation="2" data-goto="1"><a id="logosoprahr" href="javascript:void(0);" data-i18n="[title]appforyou_title_home"><svg aria-hidden="true" style="fill:#fff;height:30px;padding-left:12px;padding-right:12px;width:46px;margin-top:5px"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-home"></use></svg></a></div><div class="logopleiades"><a id="logopleiades"><svg aria-hidden="true" class="shrs-icon shrs-icon_size_large shrs-transition"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-pleiades"></use></svg></a></div><div id="btns-spaces"><div class="space-switcher"><a href="#/syd" id="space-switcher-link"><span id="manager" data-i18n="[title]appforyou_title_manager"><svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-person"></use></svg> </span><span id="expert" data-i18n="[title]appforyou_title_expert"><svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-gestionnaire"></use></svg></span></a><div class="black-disk shrs-transition"></div></div><h3 class="space-name"></h3></div></div><h1 id="pageTitle"></h1><div class="right-part"><div id="gestrole-selection-space" class="gestrole-selection-space-content hide"><a class="gestrole-selection_icon" data-i18n="[title]appforyou_title_gestRoleSelection"><svg aria-hidden="true" class="shrs-button__icon shrs-icon shrs-icon_size_large"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-gestionnaire"></use></svg></a></div><div id="search-space" class="search-space-content hide"><div id="navbarInputSearch" class="bare"><label class="hide" for="navBarSearchTextId"></label> <input id="navBarSearchTextId" class="ui-autocomplete-input" type="text" name="navBar-search-text" data-i18n="[placeholder]appforyou_placeholder_search" autocomplete="off" min="2"> <a class="search_icon" data-i18n="[title]appforyou_title_search"><svg aria-hidden="true" class="shrs-button__icon shrs-icon shrs-icon_size_large"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-search"></use></svg> </a><a href="javascript:void(0)" class="search-close" data-i18n="[title]appforyou_title_search_close"><i class="fa fa-times"></i></a></div></div><div id="question-space"><a href="#/demarches/ticketing/create"><div id="inputquestion" data-i18n="[title]appforyou_title_help"><svg aria-hidden="true" class="shrs-icon shrs-question-mark shrs-icon_size_large"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-help"></use></svg></div></a></div><div class="navbar-logout-space pull-right"><a href="#/logout" id="navbar-logout-link-id" class="navbar-logout-link" data-i18n="[title]appforyou_title_logout"><svg aria-hidden="true" class="shrs-icon navbar-logout-icon shrs-icon_size_large"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-shutdown"></use></svg></a></div></div><script id="main-menu-script" type="text/html"> {{#each this.menu_content}} {{>menu-items}} {{/each}} <li class="poweredBy"><a id="4YOU" href="javascript:void(0);">Powered by&nbsp;<img src="/resources/images/PoweredBySopraHR.png" srcset="/resources/images/PoweredBySopraHR-big.png 2x" alt=""></a></li></script><script id="menu-items" type="test/html"> {{#if !this.uiConfig.hidden}} {{#if this.id === 'legacyMenuId'}} {{#if this.children && this.children.length}} {{>menuLegacy-items}} {{else}} {{>menuLegacy-noitems}} {{/if}} {{else}} {{>menuForyou-items}} {{/if}} {{/if}} </script><script id="menuLegacy-items" type="test/html"> {{#if !this.children || !this.children.length}} <li class="white clickable {{#if this.legacyRoles !== undefined && this.legacyRoles}}has-multi-roles{{else}}empty-menu-items{{/if}}"> {{>menuLegacy-item}} </li> {{else}} <li class="white submenu {{>menuLegacy-attributes}}"> {{>menuLegacy-root-item}} <ul> {{#each this.children}} {{>menuLegacy-items}} {{/each}} </ul></li> {{/if}} </script><script id="menuLegacy-attributes" type="test/html"> {{#if this.children.length === 0}} empty-menu-items {{/if}} {{#if this.uiConfig.classes}} {{#each this.uiConfig.classes}} {{this}} {{/each}} {{/if}} </script><script id="menuLegacy-item" type="text/html"><a class="{{this.accessMode !== 'standalone' && this.accessMode !== 'embedded' ? 'link-with-icon' : 'link-without-icon'}}" href="javascript:void(0);" on-click="handleLegacyItemClick(this)"><span>{{this.name}}</span></a> {{#if this.accessMode !== 'standalone' && this.accessMode !== 'embedded'}} {{#if !this.legacyRoles}} <a class="icon-container" href="{{this.link}}" target="_blank"><img class="sub-menu-icon" src="/theme/images/wte_new_window.gif" alt="open in new tab"> </a> {{else}} <a class="icon-container" href="javascript:void(0);" on-click="openLegacyRolesPopup(this)"><img class="sub-menu-icon" src="/theme/images/wte_new_window.gif" alt="open in new tab"> </a> {{/if}} {{/if}} </script><script id="menuLegacy-root-item" type="text/html"><a href="javascript:void(0);" {{#if this.clickable}} class="link-with-icon" {{/if}}> {{#if this.uiConfig.icon}} <svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{this.uiConfig.icon}}"></use></svg> {{/if}} <span>{{this.name}}</span></a> {{#if this.id === 'legacyMenuId'}} <span id="loading-legacy-site-map-icon" class="hide"></span> {{/if}} {{#if this.clickable}} <a class="icon-container" href="javascript:void(0);" on-click="handleLegacyItemClick(this)"><svg aria-hidden="true" class="shrs-icon shrs-icon_size_medium sub-menu-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-open-transaction"></use></svg> </a> {{/if}} <i class="fa fa-caret-right fa-lg sub-menu-icon" aria-hidden="true"></i></script><script id="menuLegacy-noitems" type="test/html"><li class="{{this.uiConfig.theme ? this.uiConfig.theme : ''}}" on-click="openLegacyRolesPopup({category: null, id: null, name: null, fullName: null, link: this.link, legacyRoles: null})"> {{>menu-item}} </li></script><script id="menuForyou-items" type="test/html"> {{#if this.subItems === undefined}} <li class="{{this.uiConfig.theme ? this.uiConfig.theme : ''}} {{this.subItems ? 'submenu' : 'clickable'}}" on-click="handleClick(this.action)"> {{>menu-item}} </li> {{/if}} {{#if this.subItems !== undefined}} <li class="{{this.uiConfig.theme ? this.uiConfig.theme : ''}} {{this.subItems ? 'submenu' : 'clickable'}}"> {{>submenu-root-item}} <ul> {{#each this.subItems}} {{>menu-items}} {{/each}} </ul></li> {{/if}} </script><script id="menu-item" type="text/html"><a {{>menu-item-attr}}> {{#if this.uiConfig.icon !== undefined}} <svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{this.uiConfig.icon}}"></use></svg> {{/if}} <span>{{this.label}}</span></a> {{#if this.id === 'legacyMenuId'}} <span id="loading-legacy-site-map-icon" class="hide"></span> {{/if}} {{#if this.action.openinothertab !== undefined && this.action.openinothertab}} <i class="fa fa-files-o fa-lg sub-menu-icon" aria-hidden="true"></i> {{/if}} </script><script id="submenu-root-item" type="text/html"><a {{>menu-item-attr}}> {{#if this.uiConfig.icon !== undefined}} <svg aria-hidden="true" class="shrs-icon"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="{{this.uiConfig.icon}}"></use></svg> {{/if}} <span>{{this.label}}</span></a> {{#if this.subItems.length > 0}} <i class="fa fa-caret-right fa-lg sub-menu-icon hidden-xs" aria-hidden="true"></i> {{/if}} </script><script id="menu-item-attr" type="text/html">id={{this.id}} {{#if this.uiConfig.classes !== undefined}} class="{{#each this.uiConfig.classes}} {{this}} {{/each}}" {{/if}} {{#each this.uiConfig.attributes}} {{this.attr}} = "{{this.value}}" {{/each}} href = {{(this.action !== undefined && this.action.href !== undefined) ? this.action.href : "javascript:void(0);"}} </script></div><ul id="mainMenuList"></ul><ul id="gestRoleSelectList" class="hide"><div id="gestRoleSelectSpinner" class="hide"></div></ul><div class="mobile-header" style="display:none"><div class="col-xs-10"><div id="mobile-pageTitle"></div></div><div class="col-xs-2 text-right"><button class="showInfo shrs-button shrs-button_neutral shrs-button_type_icon shrs-transition" aria-label="The action behind the icon button" style="display:none"><svg aria-hidden="true" class="shrs-button__icon shrs-icon shrs-icon_size_large shrs-transition"><use xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href="#shrs-icon-info"></use></svg> <span class="shrs-assistive-text" data-i18n="appforyou_info"></span></button></div></div><div class="spaces"></div></div><div id="modalPopupProgressBarDiv" class="hide"><button type="button" style="display:none" class="btn btn-default btn-radius-md isenabled btn_abondan" data-i18n="appforyou_button_cancel"></button></div><div id="4YOUmodal" class="modal fade" tabindex="-1" role="dialog" data-keyboard="true" aria-modal="true" aria-labelledby="modal-heading" style="display:none"><div class="modal-dialog"><div class="modal-content"><div class="closeButton"><i tabindex="0" class="fa fa-times" data-dismiss="modal" aria-label="Fermer la modale"></i></div><div class="content"><div id="modalPopupHeader" class="header"></div><div id="modalPopupBodySpinner" class="hide body"></div><div id="modalPopupBody" class="body"></div><div id="modalPopupFooter" class="footer"></div></div></div></div></div><script type="text/x-handlebars" id="tpl-grid"> {{#each grids}} <div id="content" class="pt-page pt-page-{{ pageNumber }}" data-page-name="{{ pageName }}" data-page-number="{{ pageNumber }}">
			<div id="{{ gridId }}" class="grid-stack large"></div>
		</div> {{/each}} </script><style type="text/css">#mainMenuList a{text-decoration:none}</style><script data-main="main.js" src="/resources/js/lib/require/require-2.1.17.min.js"></script></body></html>